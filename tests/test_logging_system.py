#!/usr/bin/env python3
"""
日志系统集成测试套件

完整验证日志系统的各个方面：
- 日志路由正确性
- 文件创建和写入
- 异步刷新机制
- 并发安全性
- 性能表现

Author: Assistant
Date: 2025-07-06
"""

import pytest
import os
import sys
import time
import threading
import tempfile
import shutil
from pathlib import Path
from unittest.mock import patch, MagicMock
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from cardgame_ai.utils.logging import get_logger, initialize_logging_system
from cardgame_ai.utils.logging.enhanced_log_router import initialize_log_router, LogCategory
from cardgame_ai.utils.logging.logger_adapter import LoggerAdapter


class TestLoggingSystemRouting:
    """测试日志路由系统"""
    
    @pytest.fixture
    def temp_log_dir(self):
        """创建临时日志目录"""
        temp_dir = tempfile.mkdtemp(prefix='test_logs_')
        yield temp_dir
        shutil.rmtree(temp_dir, ignore_errors=True)
    
    @pytest.fixture
    def router(self, temp_log_dir):
        """初始化路由器"""
        return initialize_log_router(base_dir=temp_log_dir)
    
    def test_mcts_logger_routing(self, router, temp_log_dir):
        """测试MCTS日志正确路由到mcts目录"""
        # 创建MCTS logger
        logger = logging.getLogger('cardgame_ai.mcts.search')
        
        # 创建记录
        record = logger.makeRecord(
            'cardgame_ai.mcts.search', 
            logging.INFO, 
            __file__, 
            0, 
            'MCTS搜索开始', 
            (), 
            None
        )
        
        # 测试路由
        category, handler = router.route_log(record)
        
        assert category == LogCategory.MCTS.value
        assert 'mcts' in handler.baseFilename.lower()
        assert temp_log_dir in handler.baseFilename
    
    def test_training_logger_routing(self, router, temp_log_dir):
        """测试训练日志路由到training目录"""
        logger = logging.getLogger('cardgame_ai.training.happo')
        
        record = logger.makeRecord(
            'cardgame_ai.training.happo',
            logging.INFO,
            __file__,
            0,
            '训练开始',
            (),
            None
        )
        
        category, handler = router.route_log(record)
        
        assert category == LogCategory.TRAINING.value
        assert 'training' in handler.baseFilename.lower()
    
    def test_game_logger_routing(self, router, temp_log_dir):
        """测试游戏日志路由到games目录"""
        logger = logging.getLogger('cardgame_ai.games.doudizhu')
        
        record = logger.makeRecord(
            'cardgame_ai.games.doudizhu',
            logging.INFO,
            __file__,
            0,
            '游戏开始',
            (),
            None
        )
        
        category, handler = router.route_log(record)
        
        assert category == LogCategory.GAME.value
        assert 'games' in handler.baseFilename.lower()
    
    def test_metrics_logger_routing(self, router, temp_log_dir):
        """测试性能指标日志路由"""
        logger = logging.getLogger('cardgame_ai.metrics.performance')
        
        record = logger.makeRecord(
            'cardgame_ai.metrics.performance',
            logging.WARNING,
            __file__,
            0,
            '性能警告',
            (),
            None
        )
        
        category, handler = router.route_log(record)
        
        assert category == LogCategory.METRICS.value
        assert 'metrics' in handler.baseFilename.lower()
    
    def test_debug_logger_routing(self, router, temp_log_dir):
        """测试调试日志路由"""
        logger = logging.getLogger('cardgame_ai.debug.system')
        
        record = logger.makeRecord(
            'cardgame_ai.debug.system',
            logging.DEBUG,
            __file__,
            0,
            '调试信息',
            (),
            None
        )
        
        category, handler = router.route_log(record)
        
        assert category == LogCategory.DEBUG.value
        assert 'debug' in handler.baseFilename.lower()
    
    def test_summary_logger_routing(self, router, temp_log_dir):
        """测试总结日志路由"""
        logger = logging.getLogger('cardgame_ai.summary.report')
        
        record = logger.makeRecord(
            'cardgame_ai.summary.report',
            logging.ERROR,
            __file__,
            0,
            '错误总结',
            (),
            None
        )
        
        category, handler = router.route_log(record)
        
        assert category == LogCategory.SUMMARY.value
        assert 'summary' in handler.baseFilename.lower()
    
    def test_routing_priority_order(self, router):
        """测试路由优先级顺序 - MCTS应该在games之前匹配"""
        # 这个logger名称同时包含mcts和games相关词汇
        logger = logging.getLogger('cardgame_ai.mcts.games.interaction')
        
        record = logger.makeRecord(
            'cardgame_ai.mcts.games.interaction',
            logging.INFO,
            __file__,
            0,
            'MCTS与游戏交互',
            (),
            None
        )
        
        category, handler = router.route_log(record)
        
        # mcts检查应该在games检查之前，所以应该路由到mcts
        assert category == LogCategory.MCTS.value


class TestLoggingSystemFileOperations:
    """测试日志文件操作"""
    
    @pytest.fixture
    def temp_log_dir(self):
        """创建临时日志目录"""
        temp_dir = tempfile.mkdtemp(prefix='test_logs_')
        yield temp_dir
        shutil.rmtree(temp_dir, ignore_errors=True)
    
    def test_log_file_creation(self, temp_log_dir):
        """测试日志文件创建"""
        # 初始化日志系统
        initialize_logging_system(
            base_dir=temp_log_dir,
            buffer_size=100,
            batch_size=10,
            flush_interval=0.1  # 快速刷新用于测试
        )
        
        # 创建不同类型的logger并记录日志
        loggers = {
            'mcts': get_logger('cardgame_ai.mcts.test'),
            'training': get_logger('cardgame_ai.training.test'),
            'games': get_logger('cardgame_ai.games.test'),
            'metrics': get_logger('cardgame_ai.metrics.test'),
            'debug': get_logger('cardgame_ai.debug.test')
        }
        
        # 记录日志
        for log_type, logger in loggers.items():
            logger.info(f'测试{log_type}日志记录')
            logger.debug(f'调试{log_type}日志记录')
            logger.warning(f'警告{log_type}日志记录')
        
        # 等待日志刷新
        time.sleep(0.5)
        
        # 验证目录和文件创建
        expected_dirs = ['mcts', 'training', 'games', 'metrics', 'debug']
        for dir_name in expected_dirs:
            dir_path = Path(temp_log_dir) / dir_name
            assert dir_path.exists(), f"{dir_name}目录未创建"
            
            # 检查是否有日志文件
            log_files = list(dir_path.glob('*.log'))
            assert len(log_files) > 0, f"{dir_name}目录中无日志文件"
    
    def test_log_content_writing(self, temp_log_dir):
        """测试日志内容写入"""
        initialize_logging_system(
            base_dir=temp_log_dir,
            buffer_size=50,
            batch_size=10,
            flush_interval=0.1
        )
        
        # 创建MCTS logger并写入特定内容
        mcts_logger = get_logger('cardgame_ai.mcts.content_test')
        
        test_messages = [
            'MCTS搜索开始 - 目标模拟次数: 100',
            '节点选择 - UCB分数: 0.8542',
            '价值回传 - 路径长度: 5',
            'MCTS搜索完成 - 耗时: 0.152s'
        ]
        
        for msg in test_messages:
            mcts_logger.info(msg)
        
        # 等待写入
        time.sleep(0.5)
        
        # 检查内容
        mcts_dir = Path(temp_log_dir) / 'mcts'
        log_files = list(mcts_dir.glob('*.log'))
        assert len(log_files) > 0
        
        with open(log_files[0], 'r', encoding='utf-8') as f:
            content = f.read()
        
        for msg in test_messages:
            assert msg in content, f"消息 '{msg}' 未在日志文件中找到"
    
    def test_multiple_log_levels(self, temp_log_dir):
        """测试多种日志级别"""
        initialize_logging_system(base_dir=temp_log_dir, flush_interval=0.1)
        
        logger = get_logger('cardgame_ai.test.levels')
        
        # 记录不同级别的日志
        logger.debug('这是DEBUG级别')
        logger.info('这是INFO级别')
        logger.warning('这是WARNING级别')
        logger.error('这是ERROR级别')
        
        time.sleep(0.5)
        
        # 查找日志文件并验证内容
        log_dirs = ['debug', 'training', 'metrics', 'summary']  # LoggerAdapter的映射
        
        found_content = False
        for dir_name in log_dirs:
            dir_path = Path(temp_log_dir) / dir_name
            if dir_path.exists():
                log_files = list(dir_path.glob('*.log'))
                for log_file in log_files:
                    with open(log_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                        if 'DEBUG级别' in content or 'INFO级别' in content:
                            found_content = True
                            break
        
        assert found_content, "未找到任何日志内容"


class TestLoggingSystemConcurrency:
    """测试日志系统并发安全性"""
    
    @pytest.fixture
    def temp_log_dir(self):
        """创建临时日志目录"""
        temp_dir = tempfile.mkdtemp(prefix='test_logs_concurrent_')
        yield temp_dir
        shutil.rmtree(temp_dir, ignore_errors=True)
    
    def test_concurrent_logging(self, temp_log_dir):
        """测试多线程并发日志记录"""
        initialize_logging_system(
            base_dir=temp_log_dir,
            buffer_size=1000,
            batch_size=50,
            flush_interval=0.2
        )
        
        def worker_thread(thread_id, num_messages):
            """工作线程函数"""
            logger = get_logger(f'cardgame_ai.mcts.thread_{thread_id}')
            
            for i in range(num_messages):
                logger.info(f'线程{thread_id} - 消息{i} - 测试并发日志记录')
                logger.debug(f'线程{thread_id} - 调试消息{i}')
                
                # 模拟一些工作
                time.sleep(0.001)
        
        # 启动多个线程
        num_threads = 10
        messages_per_thread = 20
        
        with ThreadPoolExecutor(max_workers=num_threads) as executor:
            futures = [
                executor.submit(worker_thread, i, messages_per_thread)
                for i in range(num_threads)
            ]
            
            # 等待所有线程完成
            for future in as_completed(futures):
                future.result()
        
        # 等待日志刷新
        time.sleep(1.0)
        
        # 验证日志文件
        mcts_dir = Path(temp_log_dir) / 'mcts'
        assert mcts_dir.exists(), "MCTS日志目录未创建"
        
        log_files = list(mcts_dir.glob('*.log'))
        assert len(log_files) > 0, "未创建日志文件"
        
        # 统计总消息数
        total_messages = 0
        for log_file in log_files:
            with open(log_file, 'r', encoding='utf-8') as f:
                content = f.read()
                total_messages += content.count('测试并发日志记录')
        
        expected_messages = num_threads * messages_per_thread
        assert total_messages == expected_messages, f"预期{expected_messages}条消息，实际{total_messages}条"
    
    def test_thread_safety_different_loggers(self, temp_log_dir):
        """测试不同logger的线程安全性"""
        initialize_logging_system(base_dir=temp_log_dir, flush_interval=0.1)
        
        def multi_logger_worker(worker_id):
            """使用多个不同logger的工作线程"""
            loggers = {
                'mcts': get_logger(f'cardgame_ai.mcts.worker_{worker_id}'),
                'training': get_logger(f'cardgame_ai.training.worker_{worker_id}'),
                'games': get_logger(f'cardgame_ai.games.worker_{worker_id}')
            }
            
            for i in range(10):
                for log_type, logger in loggers.items():
                    logger.info(f'工作者{worker_id} - {log_type} - 消息{i}')
        
        # 启动线程
        threads = []
        for i in range(5):
            thread = threading.Thread(target=multi_logger_worker, args=(i,))
            thread.start()
            threads.append(thread)
        
        # 等待完成
        for thread in threads:
            thread.join()
        
        time.sleep(0.5)
        
        # 验证各个目录都有日志
        expected_dirs = ['mcts', 'training', 'games']
        for dir_name in expected_dirs:
            dir_path = Path(temp_log_dir) / dir_name
            if dir_path.exists():  # 某些目录可能因为路由逻辑而不存在
                log_files = list(dir_path.glob('*.log'))
                if log_files:  # 如果有文件，验证内容
                    with open(log_files[0], 'r', encoding='utf-8') as f:
                        content = f.read()
                        assert '工作者' in content


class TestLoggingSystemPerformance:
    """测试日志系统性能"""
    
    @pytest.fixture
    def temp_log_dir(self):
        """创建临时日志目录"""
        temp_dir = tempfile.mkdtemp(prefix='test_logs_perf_')
        yield temp_dir
        shutil.rmtree(temp_dir, ignore_errors=True)
    
    def test_high_volume_logging_performance(self, temp_log_dir):
        """测试高频日志记录性能"""
        initialize_logging_system(
            base_dir=temp_log_dir,
            buffer_size=5000,
            batch_size=500,
            flush_interval=1.0
        )
        
        logger = get_logger('cardgame_ai.mcts.performance_test')
        
        # 记录大量日志的时间
        num_messages = 1000
        start_time = time.time()
        
        for i in range(num_messages):
            logger.info(f'性能测试消息 {i} - MCTS搜索步骤详细信息')
            if i % 100 == 0:
                logger.debug(f'调试检查点 {i}')
        
        log_time = time.time() - start_time
        
        # 等待刷新
        time.sleep(2.0)
        
        # 验证性能 - 1000条消息应该在合理时间内完成
        assert log_time < 1.0, f"日志记录耗时过长: {log_time:.3f}秒"
        
        # 验证所有消息都被记录
        mcts_dir = Path(temp_log_dir) / 'mcts'
        if mcts_dir.exists():
            log_files = list(mcts_dir.glob('*.log'))
            if log_files:
                with open(log_files[0], 'r', encoding='utf-8') as f:
                    content = f.read()
                    message_count = content.count('性能测试消息')
                    assert message_count == num_messages, f"预期{num_messages}条消息，实际{message_count}条"
    
    def test_conditional_logging_performance(self, temp_log_dir):
        """测试条件日志的性能优化"""
        initialize_logging_system(base_dir=temp_log_dir, flush_interval=0.5)
        
        logger = get_logger('cardgame_ai.mcts.conditional_test')
        
        # 模拟MCTS节点访问的条件日志
        start_time = time.time()
        
        for visit_count in range(1000):
            # 模拟我们在search_engine.py中使用的条件
            if visit_count % 10 == 0:  # 每10次访问记录一次
                logger.debug(f'节点访问 {visit_count} - 条件日志记录')
            
            # 模拟路径长度条件
            path_length = visit_count % 25
            if path_length <= 20:  # 只记录较短路径
                logger.debug(f'价值回传 - 路径长度: {path_length}')
        
        conditional_time = time.time() - start_time
        
        time.sleep(1.0)
        
        # 条件日志应该很快
        assert conditional_time < 0.5, f"条件日志耗时过长: {conditional_time:.3f}秒"


class TestLoggingSystemEdgeCases:
    """测试日志系统边界情况"""
    
    @pytest.fixture
    def temp_log_dir(self):
        """创建临时日志目录"""
        temp_dir = tempfile.mkdtemp(prefix='test_logs_edge_')
        yield temp_dir
        shutil.rmtree(temp_dir, ignore_errors=True)
    
    def test_empty_logger_name(self, temp_log_dir):
        """测试空logger名称"""
        initialize_logging_system(base_dir=temp_log_dir, flush_interval=0.1)
        
        # 空名称应该使用默认路由
        logger = get_logger('')
        logger.info('空名称测试')
        
        time.sleep(0.3)
        
        # 应该路由到某个默认目录
        log_dirs = ['main', 'debug', 'training']
        found_log = False
        
        for dir_name in log_dirs:
            dir_path = Path(temp_log_dir) / dir_name
            if dir_path.exists():
                log_files = list(dir_path.glob('*.log'))
                if log_files:
                    found_log = True
                    break
        
        assert found_log, "空名称日志未找到"
    
    def test_very_long_logger_name(self, temp_log_dir):
        """测试很长的logger名称"""
        initialize_logging_system(base_dir=temp_log_dir, flush_interval=0.1)
        
        # 创建很长的logger名称
        long_name = 'cardgame_ai.mcts.' + 'very_long_component_name_' * 10
        logger = get_logger(long_name)
        logger.info('长名称测试')
        
        time.sleep(0.3)
        
        # 应该正确路由到mcts目录
        mcts_dir = Path(temp_log_dir) / 'mcts'
        assert mcts_dir.exists(), "长名称日志未正确路由"
    
    def test_special_characters_in_message(self, temp_log_dir):
        """测试消息中的特殊字符"""
        initialize_logging_system(base_dir=temp_log_dir, flush_interval=0.1)
        
        logger = get_logger('cardgame_ai.mcts.special_chars')
        
        special_messages = [
            'Unicode测试: 🎮🃏♠♥♦♣',
            'JSON格式: {"action": "play_card", "value": 123}',
            'SQL注入测试: \'; DROP TABLE users; --',
            '换行符测试\\n\\r\\t',
            '引号测试: "双引号" \'单引号\''
        ]
        
        for msg in special_messages:
            logger.info(msg)
        
        time.sleep(0.3)
        
        # 验证特殊字符被正确处理
        mcts_dir = Path(temp_log_dir) / 'mcts'
        if mcts_dir.exists():
            log_files = list(mcts_dir.glob('*.log'))
            if log_files:
                with open(log_files[0], 'r', encoding='utf-8') as f:
                    content = f.read()
                    # 至少应该包含一些特殊字符消息
                    assert 'Unicode测试' in content or 'JSON格式' in content
    
    def test_logger_adapter_missing_methods(self, temp_log_dir):
        """测试LoggerAdapter缺失方法的处理"""
        initialize_logging_system(base_dir=temp_log_dir, flush_interval=0.1)
        
        logger = get_logger('cardgame_ai.mcts.adapter_test')
        
        # 这些调用不应该抛出AttributeError
        try:
            # LoggerAdapter没有isEnabledFor方法，但调用应该安全
            logger.info('适配器测试消息')
            logger.debug('适配器调试消息')
            logger.warning('适配器警告消息')
            logger.error('适配器错误消息')
            
            success = True
        except AttributeError as e:
            if 'isEnabledFor' in str(e):
                pytest.fail(f"LoggerAdapter兼容性问题: {e}")
            else:
                raise
        
        assert success, "LoggerAdapter方法调用失败"
        
        time.sleep(0.3)
        
        # 验证消息被记录
        mcts_dir = Path(temp_log_dir) / 'mcts'
        if mcts_dir.exists():
            log_files = list(mcts_dir.glob('*.log'))
            if log_files:
                with open(log_files[0], 'r', encoding='utf-8') as f:
                    content = f.read()
                    assert '适配器测试消息' in content


@pytest.mark.core
class TestLoggingSystemIntegration:
    """日志系统集成测试"""
    
    @pytest.fixture
    def temp_log_dir(self):
        """创建临时日志目录"""
        temp_dir = tempfile.mkdtemp(prefix='test_logs_integration_')
        yield temp_dir
        shutil.rmtree(temp_dir, ignore_errors=True)
    
    def test_complete_logging_workflow(self, temp_log_dir):
        """测试完整的日志工作流程"""
        # 初始化
        initialize_logging_system(
            base_dir=temp_log_dir,
            buffer_size=500,
            batch_size=50,
            flush_interval=0.2
        )
        
        # 模拟MCTS搜索过程的完整日志记录
        mcts_logger = get_logger('cardgame_ai.mcts.search')
        training_logger = get_logger('cardgame_ai.training.happo')
        game_logger = get_logger('cardgame_ai.games.doudizhu')
        
        # 1. 训练开始
        training_logger.info('HAPPO训练开始')
        
        # 2. 游戏初始化
        game_logger.info('斗地主游戏环境初始化')
        game_logger.debug('发牌完成 - 地主: 玩家1')
        
        # 3. MCTS搜索
        mcts_logger.info('MCTS搜索开始 - 目标模拟次数: 100')
        
        for i in range(10):
            mcts_logger.debug(f'模拟 {i} - 节点选择')
            if i % 3 == 0:
                mcts_logger.debug(f'价值回传 - 路径长度: {i+1}')
        
        mcts_logger.info('MCTS搜索完成')
        
        # 4. 训练更新
        training_logger.info('策略网络更新完成')
        training_logger.info('价值网络更新完成')
        
        # 等待所有日志刷新
        time.sleep(1.0)
        
        # 验证完整工作流程
        expected_dirs = ['mcts', 'training', 'games']
        for dir_name in expected_dirs:
            dir_path = Path(temp_log_dir) / dir_name
            # 注意：由于路由逻辑，某些目录可能实际不存在
            # 我们检查至少有一些日志被正确记录
        
        # 至少mcts目录应该存在并有内容
        mcts_dir = Path(temp_log_dir) / 'mcts'
        assert mcts_dir.exists(), "MCTS日志目录不存在"
        
        log_files = list(mcts_dir.glob('*.log'))
        assert len(log_files) > 0, "MCTS日志文件不存在"
        
        with open(log_files[0], 'r', encoding='utf-8') as f:
            content = f.read()
            assert 'MCTS搜索开始' in content
            assert 'MCTS搜索完成' in content
            assert '节点选择' in content
    
    def test_logging_system_resilience(self, temp_log_dir):
        """测试日志系统的容错能力"""
        initialize_logging_system(base_dir=temp_log_dir, flush_interval=0.1)
        
        logger = get_logger('cardgame_ai.mcts.resilience_test')
        
        # 测试各种可能导致问题的情况
        test_cases = [
            None,  # None值
            '',    # 空字符串
            'a' * 10000,  # 超长字符串
            {'dict': 'object'},  # 字典对象
            [1, 2, 3],  # 列表对象
            Exception('test exception'),  # 异常对象
        ]
        
        for i, test_case in enumerate(test_cases):
            try:
                logger.info(f'测试案例 {i}: {test_case}')
                success = True
            except Exception as e:
                # 日志系统应该能处理各种输入而不崩溃
                pytest.fail(f"日志系统处理测试案例 {i} 失败: {e}")
        
        time.sleep(0.3)
        
        # 验证系统仍然正常工作
        logger.info('容错测试完成 - 系统正常')
        
        time.sleep(0.3)
        
        mcts_dir = Path(temp_log_dir) / 'mcts'
        if mcts_dir.exists():
            log_files = list(mcts_dir.glob('*.log'))
            if log_files:
                with open(log_files[0], 'r', encoding='utf-8') as f:
                    content = f.read()
                    assert '容错测试完成' in content


# 运行测试的便捷函数
def run_logging_tests():
    """运行所有日志系统测试"""
    pytest.main([__file__, '-v', '--tb=short'])


if __name__ == '__main__':
    run_logging_tests()