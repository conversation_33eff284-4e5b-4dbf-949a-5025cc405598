# 斗地主AI项目开发规则

> AI代理专用开发标准文档
> 最后更新：2025-07-06

## 项目概述

- **核心算法**：EfficientZero V2 + HAPPO + PPB-MCTS
- **架构类型**：单机优化架构（已移除分布式代码）
- **目标胜率**：85-95%
- **观察空间**：116维统一架构（从656维压缩）
- **动作空间**：198个游戏动作，2300维神经网络输出

## 环境和依赖管理

### 强制要求
- **运行任何Python代码前必须激活conda环境**：`conda activate cardgame`
- **GPU必需**：没有CUDA设备训练将失败
- **Python版本**：3.8-3.11（推荐3.10）

### 依赖安装
```bash
# 完整安装（必须）
pip install -r requirements.txt

# 最小安装（仅核心功能）
pip install torch>=2.0.0 numpy>=1.21.0 PyYAML>=6.0 Jinja2>=3.0.0 psutil>=5.8.0 nvidia-ml-py>=12.535.133
```

## 项目架构

### 核心目录结构

- `cardgame_ai/zhuchengxu/` - **主程序入口目录**
  - `auto_deploy.py` - 唯一训练入口
  - `optimized_main_training.py` - GPU优化训练器
- `cardgame_ai/algorithms/` - 算法实现
  - `efficient_zero_v2/` - 主算法模块
  - `happo/` - 多智能体协作
  - `ppb_mcts.py` - 并行MCTS
- `cardgame_ai/core/` - 核心系统
  - `action_mapping.py` - 动作映射管理
  - `component_manager.py` - 组件管理器
  - `config/` - 配置系统
    - `observation_space_manager.py` - **观察空间管理器（单例）**
- `cardgame_ai/games/doudizhu/` - 游戏实现
  - `compact_observation.py` - 116维观察编码器

### 关键文件依赖

**修改任一文件时必须同步修改：**
- 修改 `action_mapping.py` → 更新 `cardgame_ai/games/doudizhu/action.py`
- 修改 `observation_space_manager.py` → 更新所有使用观察空间的文件
- 修改算法配置 → 更新 `optimized_fixed_config.yaml`
- 修改日志系统 → 更新 `cardgame_ai/utils/logging/`下所有wrapper

## 代码标准

### 代码模块化规则（最高优先级）

**模块设计原则：**
1. **单一职责原则**：每个模块只负责一个明确的功能
2. **模块大小限制**：单个模块代码行数不超过1000行
3. **高内聚低耦合**：模块内部功能紧密相关，模块间依赖最小化
4. **清晰的接口定义**：模块对外暴露明确的API

**模块拆分标准：**
```python
# ✅ 正确：职责单一的模块
# file: cardgame_ai/algorithms/mcts/node.py (约200行)
class MCTSNode:
    """MCTS节点管理"""
    pass

# file: cardgame_ai/algorithms/mcts/search.py (约500行)
class MCTSSearch:
    """MCTS搜索逻辑"""
    pass

# file: cardgame_ai/algorithms/mcts/policy.py (约300行)
class MCTSPolicy:
    """MCTS策略选择"""
    pass

# ❌ 错误：巨型模块
# file: cardgame_ai/algorithms/mcts_all.py (2000+行)
class MCTSEverything:
    """包含所有MCTS功能的巨型类"""
    pass
```

**模块组织规则：**
1. 超过1000行的文件必须拆分
2. 相关功能组织在同一目录下
3. 使用`__init__.py`暴露公共接口
4. 内部实现细节使用下划线前缀

**重构指南：**
- 发现超过800行的模块时，开始规划拆分
- 拆分时保持向后兼容性
- 创建清晰的模块依赖图
- 编写模块级文档说明职责

### 116维统一架构规则

**必须使用 ObservationSpaceManager 管理观察空间：**
```python
from cardgame_ai.core.config.observation_space_manager import get_observation_space_manager
obs_manager = get_observation_space_manager()
observation_shape = obs_manager.get_observation_shape()  # [116]
```

**禁止：**
- ❌ 硬编码观察空间维度（656或116）
- ❌ 直接创建观察空间形状
- ❌ 绕过ObservationSpaceManager
- ❌ 在不同组件中使用不同维度

### 训练入口规则

**必须使用 `auto_deploy.py` 启动训练：**
```bash
python cardgame_ai/zhuchengxu/auto_deploy.py
# 或使用自定义配置
python cardgame_ai/zhuchengxu/auto_deploy.py --config my_config.yaml
```

**禁止直接调用：**
- ❌ `main_training.py` (已移除)
- ❌ `optimized_main_training.py` (仅供内部调用)
- ❌ 任何 scripts/ 目录下的训练脚本

### 配置系统规则

**Phase 4功能通过YAML配置：**
```yaml
# optimized_fixed_config.yaml
phase4:
  preset: "expert"  # minimal, balanced, full, expert
  features:
    intelligent_bidding: true
    humanlike_memory: true
    observation_compression: true  # 必须为true
```

**禁止使用：**
- ❌ 命令行参数配置Phase 4功能
- ❌ 动态配置生成
- ❌ 临时配置文件

### 动作空间规则

**必须使用全局动作映射器：**
```python
from cardgame_ai.core.action_mapping import get_global_action_mapper
mapper = get_global_action_mapper()
action_size = mapper.get_action_size()  # 2300
```

**禁止：**
- ❌ 硬编码动作空间大小
- ❌ 使用565维旧版动作空间
- ❌ 创建新的动作映射器实例

## 功能实现标准

### 模块实现标准

**新建模块检查清单：**
- [ ] 模块职责单一且明确
- [ ] 代码行数 < 1000行
- [ ] 有清晰的公共API
- [ ] 包含模块级文档字符串
- [ ] 遵循项目命名规范
- [ ] 通过单元测试覆盖

**大型功能拆分示例：**
```
cardgame_ai/algorithms/efficient_zero_v2/
├── __init__.py          # 公共接口
├── config.py            # 配置定义 (~100行)
├── model.py             # 神经网络模型 (~500行)
├── mcts.py              # MCTS搜索 (~600行)
├── replay_buffer.py     # 经验回放 (~400行)
├── trainer.py           # 训练器 (~800行)
└── utils.py             # 工具函数 (~200行)
```

### 维度一致性要求

**添加新模块必须：**
1. 从ObservationSpaceManager获取观察空间形状
2. 在初始化时验证维度一致性
3. 运行comprehensive_dimension_test.py验证
4. 更新维度测试用例

**维度验证示例：**
```python
class NewModule:
    def __init__(self):
        obs_manager = get_observation_space_manager()
        self.observation_shape = obs_manager.get_observation_shape()
        assert self.observation_shape == [116], f"期望116维，实际{self.observation_shape}"
```

### Phase 4功能集成

**配置文件中启用Phase 4功能：**
```yaml
# optimized_fixed_config.yaml
phase4:
  preset: "expert"
  features:
    intelligent_bidding: true
    humanlike_memory: true
    game_theory_bomb: true
    farmer_cooperation: true
    spring_rules: true
    statistical_analysis: true
    decision_visualization: true
    observation_compression: true  # 必须启用
    zero_copy_batch: true
```

**预设模式说明：**
- `minimal` - 智能叫地主 + 春天规则
- `balanced` - + 人性化记忆 + 博弈论炸弹
- `full` - + 农民协作 + 统计分析
- `expert` - 全部功能 + 决策可视化

### 算法集成规则

**添加新算法必须：**
1. 在 `cardgame_ai/algorithms/` 创建模块目录
2. 继承正确的基类并支持116维输入
3. 在 `ComponentManager` 注册算法
4. 创建配置文件并设置state_shape: [116]
5. 添加维度兼容性测试

**禁止：**
- ❌ 修改现有算法核心逻辑
- ❌ 破坏116维兼容性
- ❌ 绕过组件管理器

### 日志系统规则

**使用分层日志系统：**
```python
from cardgame_ai.utils.logging import get_logger
logger = get_logger(__name__)
# 使用对应层级
logger.debug()      # DEBUG层
logger.training()   # TRAINING层
logger.metrics()    # METRICS层
logger.game()       # GAME层
logger.summary()    # SUMMARY层
```

**日志文件路径：**
- `logs/games/` - 游戏日志
- `logs/training/` - 训练日志
- `logs/metrics/` - 指标日志

## 框架/插件/第三方库使用标准

### GPU优化要求

**必须检查CUDA可用性：**
```python
if not torch.cuda.is_available():
    raise RuntimeError("GPU是必需的，没有CUDA设备训练将失败")
```

**使用ObservationSpaceManager进行内存优化：**
```python
# 116维架构减少82.3%内存使用
obs_manager = get_observation_space_manager()
obs_manager.set_compression_mode(True)  # 确保压缩启用
```

### 性能基准要求

**116维架构性能指标：**
- 编码速度：4倍提升
- 内存使用：减少82.3%
- 推理时间：<0.2ms
- GPU利用率：>98%

## 工作流标准

### 训练工作流

1. **启动训练前检查：**
   - 激活conda环境 `cardgame`
   - 确认GPU可用
   - 运行 `comprehensive_dimension_test.py` 验证116维架构
   - 清理残留进程

2. **训练过程监控：**
   - 检查日志中观察空间是否为116维
   - 监控GPU利用率(应>95%)
   - 验证MCTS搜索使用torch.Size([1, 116])

3. **检查点管理：**
   - 自动保存到 `checkpoints/`
   - 新检查点应使用116维架构
   - 备份旧的656维检查点

### 测试工作流

**运行测试：**
```bash
pytest -m smoke          # 快速测试
pytest -m core           # 核心测试
pytest -m integration    # 集成测试

# 116维架构验证（必须运行）
python comprehensive_dimension_test.py
```

**维度一致性测试：**
```bash
# 测试特定组件
python tests/test_observation_space_manager.py
```

## 关键文件交互标准

### 维度管理文件同步

修改观察空间时必须同步更新：
1. `core/config/observation_space_manager.py` - 管理器
2. `games/doudizhu/compact_observation.py` - 编码器
3. `optimized_fixed_config.yaml` - state_shape: [116]
4. `algorithms/efficient_zero_model.py` - 模型输入层
5. `training/efficient_zero_v2_trainer.py` - 训练器初始化

### 配置文件同步

修改训练配置时必须同步更新：
1. `optimized_fixed_config.yaml` - 主配置
2. 确保state_shape为[116]
3. 确保observation_compression为true
4. 验证Phase 4功能配置正确

### 算法模块同步

修改算法实现时必须同步：
1. 算法主文件
2. 确保支持116维输入
3. 更新训练器适配
4. 添加维度测试用例

## AI决策标准

### 代码重构决策树

遇到大型模块（>1000行）：
1. 分析模块职责，识别功能边界
2. 创建模块拆分计划
3. 逐步提取独立功能到新模块
4. 保持接口兼容性
5. 更新导入路径和测试

遇到职责不清的模块：
1. 列出模块所有功能
2. 按相关性分组
3. 为每组创建独立模块
4. 重构代码结构

### 维度问题决策树

遇到维度不匹配错误：
1. 检查是否使用ObservationSpaceManager
2. 运行comprehensive_dimension_test.py
3. 检查配置文件state_shape是否为[116]
4. 验证所有组件使用相同维度

遇到内存问题：
1. 确认observation_compression启用
2. 验证使用116维而非656维
3. 减小批次大小
4. 检查GPU内存使用

### 错误处理优先级

1. **维度不匹配** → 立即停止，检查ObservationSpaceManager
2. **CUDA错误** → 检查GPU可用性
3. **内存溢出** → 验证116维架构正确实施
4. **配置错误** → 检查state_shape和observation_compression

## 禁止行为

### 严格禁止（模块化相关）

- ❌ **创建超过1000行的单一模块**
- ❌ **在一个类中混合多个职责**
- ❌ **循环依赖between模块**
- ❌ **将所有功能塞进utils.py**
- ❌ **忽略模块拆分警告**

### 严格禁止（维度相关）

- ❌ **硬编码656或116维度**
- ❌ **绕过ObservationSpaceManager**
- ❌ **在不同组件使用不同维度**
- ❌ **禁用observation_compression**
- ❌ **创建维度转换的临时方案**

### 严格禁止（通用）

- ❌ **创建简化/测试/临时版本**
- ❌ **使用后备/降级策略**
- ❌ **跳过错误继续执行**
- ❌ **修改核心算法逻辑**
- ❌ **使用模拟数据**
- ❌ **绕过组件管理器**
- ❌ **直接修改生产检查点**

### 代码修改禁止

- ❌ 修改ObservationSpaceManager单例模式
- ❌ 修改116维压缩算法
- ❌ 修改 `get_global_action_mapper()` 返回值
- ❌ 降级到656维架构
- ❌ 删除维度验证代码

## 具体示例

### ✅ 正确：模块化设计

```python
# file: cardgame_ai/algorithms/efficient_zero_v2/model.py (约500行)
"""EfficientZero V2 神经网络模型"""

class EfficientZeroNetwork(nn.Module):
    """负责网络架构和前向传播"""
    def __init__(self, config):
        super().__init__()
        # 只包含网络定义相关代码
        
    def forward(self, x):
        # 前向传播逻辑
        pass

# file: cardgame_ai/algorithms/efficient_zero_v2/trainer.py (约800行)
"""EfficientZero V2 训练器"""

from .model import EfficientZeroNetwork

class EfficientZeroTrainer:
    """负责训练流程管理"""
    def __init__(self, config):
        self.model = EfficientZeroNetwork(config)
        # 只包含训练相关代码
```

### ❌ 错误：巨型模块

```python
# file: cardgame_ai/algorithms/efficient_zero_all.py (3000+行)
"""包含所有EfficientZero功能的文件"""

class EfficientZeroEverything:
    def __init__(self):
        # 初始化网络
        # 初始化MCTS
        # 初始化训练器
        # 初始化回放缓冲区
        # ... 太多职责
        
    def forward(self):
        pass
        
    def train(self):
        pass
        
    def mcts_search(self):
        pass
        
    # ... 数千行代码
```

### ✅ 正确：使用ObservationSpaceManager

```python
from cardgame_ai.core.config.observation_space_manager import get_observation_space_manager

class NewAlgorithm:
    def __init__(self):
        # 正确获取观察空间
        obs_manager = get_observation_space_manager()
        self.observation_shape = obs_manager.get_observation_shape()
        
        # 创建网络
        self.network = nn.Sequential(
            nn.Linear(self.observation_shape[0], 256),  # 116 -> 256
            nn.ReLU()
        )
```

### ❌ 错误：硬编码维度

```python
# 错误示例
class WrongAlgorithm:
    def __init__(self):
        # ❌ 硬编码维度
        self.network = nn.Linear(656, 256)
        # 或
        self.network = nn.Linear(116, 256)  # 也是错误的！
```

### ✅ 正确：维度验证

```python
def validate_observation(obs):
    obs_manager = get_observation_space_manager()
    expected_shape = obs_manager.get_observation_shape()
    
    if obs.shape[-1] != expected_shape[0]:
        raise ValueError(f"观察维度错误：期望{expected_shape[0]}，实际{obs.shape[-1]}")
```

### ❌ 错误：忽略维度不匹配

```python
# 错误示例
try:
    output = model(observation)
except RuntimeError:
    # ❌ 忽略维度错误
    observation = observation[:, :116]  # 临时修复
    output = model(observation)
```

## 性能基准

### 模块化架构性能要求

- 模块加载时间：<0.1秒
- 模块间调用开销：<0.01ms
- 代码重用率：>60%
- 测试覆盖率：>85%

### 116维架构性能指标

- 观察编码：<0.05ms
- 内存压缩：82.3%
- 推理延迟：<0.2ms
- 训练吞吐：>1000局/小时

### 训练性能要求

- GPU利用率：>98%
- 批处理大小：>=256（116维架构支持更大批次）
- MCTS模拟：800次（使用PPB并行）
- 检查点保存：<5秒

## 版本兼容性

### Python版本
- 支持：3.8, 3.9, 3.10, 3.11
- 推荐：3.10

### PyTorch版本
- 最低：2.0.0
- 推荐：2.1.0+

### CUDA版本
- 最低：11.7
- 推荐：11.8+

### 架构版本
- 当前：116维统一架构
- 废弃：656维原始架构