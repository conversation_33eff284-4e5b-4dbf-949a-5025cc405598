#!/usr/bin/env python3
"""
训练健康检查器pytest测试
"""

import pytest
import time
import torch
import psutil
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock

from cardgame_ai.monitoring.training_health_checker import (
    TrainingHealthChecker,
    HealthStatus,
    RecoveryAction,
    create_health_checker_with_defaults
)
from cardgame_ai.monitoring.realtime_training_monitor import RealtimeTrainingMonitor


class TestHealthChecker:
    """健康检查器测试"""
    
    @pytest.fixture
    def health_checker(self, tmp_path):
        """创建测试用的健康检查器"""
        config = {
            'check_interval': 0.1,  # 快速测试
            'checkpoint_dir': str(tmp_path / "checkpoints"),
            'max_recovery_attempts': 2
        }
        Path(config['checkpoint_dir']).mkdir(exist_ok=True)
        return TrainingHealthChecker(config)
    
    @pytest.fixture
    def mock_monitor(self):
        """创建模拟的训练监控器"""
        monitor = Mock(spec=RealtimeTrainingMonitor)
        monitor.get_current_stats.return_value = {
            'total_episodes': 100,
            'total_samples': 10000,
            'metrics': {
                'loss': {'value': 0.5, 'trend': 'decreasing'},
                'win_rate': {'value': 75.0}
            }
        }
        return monitor
    
    def test_initialization(self, health_checker):
        """测试初始化"""
        assert health_checker.health_status == HealthStatus.HEALTHY
        assert health_checker.recovery_attempts == 0
        assert not health_checker.checking_active
    
    def test_start_stop_checking(self, health_checker, mock_monitor):
        """测试启动和停止检查"""
        # 启动检查
        health_checker.start_checking(mock_monitor)
        assert health_checker.checking_active
        
        # 等待一些检查周期
        time.sleep(0.3)
        
        # 停止检查
        health_checker.stop_checking()
        assert not health_checker.checking_active
    
    def test_process_alive_check(self, health_checker):
        """测试进程存活检查"""
        # 当前进程应该存活
        assert health_checker._check_process_alive()
        
        # 模拟死亡进程
        health_checker.process_id = 999999  # 不存在的PID
        assert not health_checker._check_process_alive()
    
    def test_training_progress_check(self, health_checker, mock_monitor):
        """测试训练进度检查"""
        health_checker.training_monitor = mock_monitor
        
        # 第一次检查
        status, metrics = health_checker._check_training_progress()
        assert 'episodes_per_minute' in metrics
        assert 'samples_per_minute' in metrics
        
        # 模拟进度
        health_checker.last_episode_count = 90
        health_checker.last_sample_count = 9000
        health_checker.last_check_time = time.time() - 60  # 1分钟前
        
        # 第二次检查
        status, metrics = health_checker._check_training_progress()
        assert metrics['episodes_per_minute'] > 0
        assert status == 'normal'
    
    def test_resource_usage_check(self, health_checker):
        """测试资源使用检查"""
        status, metrics = health_checker._check_resource_usage()
        
        assert 'cpu_percent' in metrics
        assert 'memory_percent' in metrics
        assert metrics['cpu_percent'] >= 0
        assert metrics['memory_percent'] >= 0
    
    @pytest.mark.gpu
    def test_gpu_resource_check(self, health_checker):
        """测试GPU资源检查"""
        if not torch.cuda.is_available():
            pytest.skip("GPU不可用")
        
        from cardgame_ai.core.monitoring.gpu_memory_manager import GPUMemoryManager
        health_checker.gpu_memory_manager = GPUMemoryManager()
        
        status, metrics = health_checker._check_resource_usage()
        assert 'gpu_memory_percent' in metrics
        assert metrics['gpu_memory_percent'] >= 0
    
    def test_checkpoint_integrity_check(self, health_checker, tmp_path):
        """测试检查点完整性检查"""
        checkpoint_dir = tmp_path / "checkpoints"
        
        # 没有检查点
        result = health_checker._check_checkpoint_integrity()
        assert not result['valid']
        
        # 创建有效检查点
        checkpoint_file = checkpoint_dir / "checkpoint.pth"
        torch.save({'model_state_dict': {}, 'epoch': 1}, checkpoint_file)
        
        result = health_checker._check_checkpoint_integrity()
        assert result['valid']
        assert result['latest_checkpoint'] == str(checkpoint_file)
        assert result['size_mb'] > 0
    
    def test_recovery_action_determination(self, health_checker):
        """测试恢复动作确定"""
        # 进程死亡
        health_result = {
            'status': HealthStatus.DEAD,
            'issues': ['训练进程已死亡'],
            'metrics': {}
        }
        action = health_checker._determine_recovery_action(health_result)
        assert action == RecoveryAction.RESTART_PROCESS
        
        # GPU内存过高
        health_result = {
            'status': HealthStatus.CRITICAL,
            'issues': ['资源使用异常'],
            'metrics': {
                'resources': {'gpu_memory_percent': 96}
            }
        }
        action = health_checker._determine_recovery_action(health_result)
        assert action == RecoveryAction.REDUCE_BATCH_SIZE
        
        # 训练停滞
        health_result = {
            'status': HealthStatus.CRITICAL,
            'issues': ['训练停滞超过600秒'],
            'metrics': {}
        }
        action = health_checker._determine_recovery_action(health_result)
        assert action == RecoveryAction.RESTART_FROM_CHECKPOINT
    
    def test_clear_cache_recovery(self, health_checker):
        """测试清理缓存恢复"""
        # 执行清理
        health_checker._clear_cache()
        
        # 应该不会抛出异常
        assert True
    
    def test_reduce_batch_size_recovery(self, health_checker, tmp_path):
        """测试减小批次大小恢复"""
        # 设置初始批次大小
        health_checker.config['batch_size'] = 32
        health_checker.config['config_file'] = str(tmp_path / "config.json")
        
        # 执行减小
        health_checker._reduce_batch_size()
        
        # 批次大小应该减小
        assert health_checker.config['batch_size'] < 32
    
    def test_emergency_save(self, health_checker, tmp_path):
        """测试紧急保存"""
        checkpoint_dir = tmp_path / "checkpoints"
        
        # 创建测试检查点
        test_checkpoint = checkpoint_dir / "test.pth"
        torch.save({'test': True}, test_checkpoint)
        
        # 执行紧急保存
        health_checker._emergency_save()
        
        # 检查紧急目录
        emergency_dir = checkpoint_dir / "emergency"
        assert emergency_dir.exists()
        
        # 应该有紧急状态文件
        emergency_files = list(emergency_dir.glob("emergency_state_*.json"))
        assert len(emergency_files) > 0
    
    def test_health_report_generation(self, health_checker):
        """测试健康报告生成"""
        # 添加一些健康历史
        health_checker.health_history.append({
            'timestamp': time.time(),
            'status': HealthStatus.HEALTHY,
            'metrics': {}
        })
        health_checker.health_history.append({
            'timestamp': time.time(),
            'status': HealthStatus.WARNING,
            'metrics': {}
        })
        
        # 生成报告
        health_checker._generate_health_report()
        
        # 应该生成报告文件
        report_files = list(Path(health_checker.checkpoint_dir).glob("health_report_*.json"))
        assert len(report_files) > 0
    
    def test_recovery_callback(self, health_checker):
        """测试恢复回调"""
        callback_called = False
        callback_action = None
        
        def test_callback(action, recovery_log):
            nonlocal callback_called, callback_action
            callback_called = True
            callback_action = action
        
        # 注册回调
        health_checker.register_recovery_callback(test_callback)
        
        # 执行恢复
        health_result = {'status': HealthStatus.CRITICAL, 'issues': [], 'metrics': {}}
        health_checker._execute_recovery(RecoveryAction.CLEAR_CACHE, health_result)
        
        # 回调应该被调用
        assert callback_called
        assert callback_action == RecoveryAction.CLEAR_CACHE
    
    def test_manual_recovery(self, health_checker):
        """测试手动恢复"""
        # 手动触发清理缓存
        success = health_checker.manual_recovery(RecoveryAction.CLEAR_CACHE)
        assert success
        
        # 恢复尝试应该增加
        assert health_checker.recovery_attempts > 0
    
    def test_max_recovery_attempts(self, health_checker):
        """测试最大恢复尝试限制"""
        # 设置已达到最大尝试次数
        health_checker.recovery_attempts = health_checker.max_recovery_attempts
        
        # 确定恢复动作应该返回NONE
        health_result = {
            'status': HealthStatus.CRITICAL,
            'issues': ['测试问题'],
            'metrics': {}
        }
        action = health_checker._determine_recovery_action(health_result)
        assert action == RecoveryAction.NONE
    
    @pytest.mark.integration
    def test_full_health_check_cycle(self, health_checker, mock_monitor, tmp_path):
        """测试完整的健康检查周期"""
        # 创建检查点
        checkpoint_dir = tmp_path / "checkpoints"
        checkpoint_file = checkpoint_dir / "checkpoint.pth"
        torch.save({'model_state_dict': {}}, checkpoint_file)
        
        # 启动检查
        health_checker.start_checking(mock_monitor)
        
        # 执行健康检查
        result = health_checker._perform_health_check()
        
        assert 'status' in result
        assert 'metrics' in result
        assert 'issues' in result
        
        # 停止检查
        health_checker.stop_checking()


class TestHealthCheckerIntegration:
    """健康检查器集成测试"""
    
    @pytest.mark.slow
    def test_with_real_monitor(self, tmp_path):
        """测试与真实监控器的集成"""
        # 创建真实监控器
        monitor = RealtimeTrainingMonitor({'monitor_interval': 0.1})
        
        # 创建健康检查器
        config = {
            'check_interval': 0.2,
            'checkpoint_dir': str(tmp_path / "checkpoints")
        }
        Path(config['checkpoint_dir']).mkdir(exist_ok=True)
        health_checker = TrainingHealthChecker(config)
        
        # 启动
        monitor.start_monitoring()
        health_checker.start_checking(monitor)
        
        # 模拟训练
        for i in range(5):
            monitor.update_training_metrics(
                loss=1.0/(i+1),
                win_rate=50+i*10,
                episodes=i*10,
                samples=i*1000
            )
            time.sleep(0.1)
        
        # 获取健康状态
        status = health_checker.get_health_status()
        assert status['status'] in [HealthStatus.HEALTHY, HealthStatus.WARNING]
        
        # 停止
        health_checker.stop_checking()
        monitor.stop_monitoring()


if __name__ == "__main__":
    pytest.main([__file__, '-v'])