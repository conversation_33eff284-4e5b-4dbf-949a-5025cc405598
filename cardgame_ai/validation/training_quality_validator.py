#!/usr/bin/env python3
"""
训练质量验证主控制器

提供统一的质量验证接口，整合所有质量检查功能，包括：
- 启动前验证
- 训练中监控
- 训练后评估
- 质量报告生成

作者：AI系统
创建时间：2025-07-05
"""

import os
import sys
import json
import yaml
import time
import logging
import argparse
import traceback
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path
from datetime import datetime
from dataclasses import dataclass, field, asdict
from collections import defaultdict
import numpy as np

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# 导入现有的质量检查组件
from comprehensive_dimension_test import DimensionConsistencyValidator
from performance_regression_test_suite import PerformanceRegressionTestSuite
from cardgame_ai.utils.logging import get_logger
from cardgame_ai.core.config.observation_space_manager import get_observation_space_manager
from cardgame_ai.core.component_manager import ComponentManager

logger = get_logger(__name__)


@dataclass
class QualityCheckResult:
    """质量检查结果"""
    check_name: str
    category: str
    passed: bool
    score: float
    details: Dict[str, Any] = field(default_factory=dict)
    error_message: Optional[str] = None
    timestamp: str = field(default_factory=lambda: datetime.now().isoformat())
    duration: float = 0.0


@dataclass
class QualityReport:
    """质量验证报告"""
    report_id: str
    timestamp: str
    total_checks: int
    passed_checks: int
    failed_checks: int
    overall_score: float
    categories: Dict[str, Dict[str, Any]]
    checks: List[QualityCheckResult]
    recommendations: List[str]
    metadata: Dict[str, Any] = field(default_factory=dict)


class QualityValidator:
    """质量验证器基类"""
    
    def __init__(self, name: str, category: str):
        self.name = name
        self.category = category
        self.logger = get_logger(f"{__name__}.{name}")
    
    def validate(self) -> QualityCheckResult:
        """执行验证"""
        raise NotImplementedError
    
    def get_requirements(self) -> Dict[str, Any]:
        """获取验证要求"""
        return {}


class DimensionConsistencyQualityValidator(QualityValidator):
    """维度一致性质量验证器"""
    
    def __init__(self):
        super().__init__("dimension_consistency", "architecture")
        self.validator = DimensionConsistencyValidator()
    
    def validate(self) -> QualityCheckResult:
        """执行维度一致性验证"""
        start_time = time.time()
        try:
            # 运行所有维度测试
            self.validator.run_all_tests()
            
            # 计算得分
            total_tests = len(self.validator.test_results)
            passed_tests = sum(1 for v in self.validator.test_results.values() if v)
            score = (passed_tests / total_tests * 100) if total_tests > 0 else 0
            
            # 判断是否通过
            passed = score >= 95.0  # 95%通过率要求
            
            duration = time.time() - start_time
            
            return QualityCheckResult(
                check_name=self.name,
                category=self.category,
                passed=passed,
                score=score,
                details={
                    'total_tests': total_tests,
                    'passed_tests': passed_tests,
                    'test_results': dict(self.validator.test_results),
                    'dimension_violations': self.validator.dimension_violations,
                    'performance_metrics': self.validator.performance_metrics
                },
                duration=duration
            )
            
        except Exception as e:
            duration = time.time() - start_time
            return QualityCheckResult(
                check_name=self.name,
                category=self.category,
                passed=False,
                score=0.0,
                error_message=str(e),
                details={'traceback': traceback.format_exc()},
                duration=duration
            )


class PerformanceQualityValidator(QualityValidator):
    """性能质量验证器"""
    
    def __init__(self):
        super().__init__("performance_regression", "performance")
        self.test_suite = PerformanceRegressionTestSuite()
    
    def validate(self) -> QualityCheckResult:
        """执行性能验证"""
        start_time = time.time()
        try:
            # 运行性能测试
            results = self.test_suite.run_all_tests()
            report = self.test_suite.generate_report(results)
            
            # 获取关键指标
            summary = report['summary']
            score = summary['success_rate']
            passed = score >= 90.0  # 90%成功率要求
            
            duration = time.time() - start_time
            
            return QualityCheckResult(
                check_name=self.name,
                category=self.category,
                passed=passed,
                score=score,
                details={
                    'summary': summary,
                    'key_metrics': report['key_metrics'],
                    'regression_analysis': report.get('regression_analysis', []),
                    'improvement_analysis': report.get('improvement_analysis', [])
                },
                duration=duration
            )
            
        except Exception as e:
            duration = time.time() - start_time
            return QualityCheckResult(
                check_name=self.name,
                category=self.category,
                passed=False,
                score=0.0,
                error_message=str(e),
                details={'traceback': traceback.format_exc()},
                duration=duration
            )


class ConfigurationQualityValidator(QualityValidator):
    """配置质量验证器"""
    
    def __init__(self, config_path: Optional[str] = None):
        super().__init__("configuration", "setup")
        self.config_path = config_path or "optimized_fixed_config.yaml"
    
    def validate(self) -> QualityCheckResult:
        """验证配置文件"""
        start_time = time.time()
        try:
            # 加载配置
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            # 验证必需字段
            required_fields = [
                'algorithm', 'environment', 'network', 'training',
                'mcts', 'self_play', 'optimization', 'phase4'
            ]
            
            missing_fields = []
            for field in required_fields:
                if field not in config:
                    missing_fields.append(field)
            
            # 验证关键参数
            checks = {
                'observation_shape': config.get('observation_shape') == [116],
                'action_space': config.get('action_space') == 2300,
                'batch_size': 32 <= config.get('training', {}).get('batch_size', 0) <= 512,
                'gpu_enabled': config.get('device', '').startswith('cuda'),
                'phase4_enabled': config.get('phase4', {}).get('enabled', False)
            }
            
            # 计算得分
            passed_checks = sum(1 for v in checks.values() if v)
            total_checks = len(checks) + len(required_fields)
            score = ((passed_checks + len(required_fields) - len(missing_fields)) / total_checks * 100)
            
            passed = len(missing_fields) == 0 and all(checks.values())
            
            duration = time.time() - start_time
            
            return QualityCheckResult(
                check_name=self.name,
                category=self.category,
                passed=passed,
                score=score,
                details={
                    'config_path': self.config_path,
                    'missing_fields': missing_fields,
                    'parameter_checks': checks,
                    'phase4_features': config.get('phase4', {}).get('features', {})
                },
                duration=duration
            )
            
        except Exception as e:
            duration = time.time() - start_time
            return QualityCheckResult(
                check_name=self.name,
                category=self.category,
                passed=False,
                score=0.0,
                error_message=str(e),
                details={'traceback': traceback.format_exc()},
                duration=duration
            )


class EnvironmentQualityValidator(QualityValidator):
    """环境质量验证器"""
    
    def __init__(self):
        super().__init__("environment", "setup")
    
    def validate(self) -> QualityCheckResult:
        """验证运行环境"""
        start_time = time.time()
        try:
            checks = {}
            
            # Python版本检查
            import sys
            python_version = sys.version_info
            checks['python_version'] = 3.8 <= python_version.major + python_version.minor/10 <= 3.11
            
            # PyTorch检查
            try:
                import torch
                checks['pytorch_installed'] = True
                checks['pytorch_version'] = torch.__version__
                checks['cuda_available'] = torch.cuda.is_available()
                if torch.cuda.is_available():
                    checks['cuda_device_count'] = torch.cuda.device_count()
                    checks['cuda_device_name'] = torch.cuda.get_device_name(0)
            except ImportError:
                checks['pytorch_installed'] = False
                checks['cuda_available'] = False
            
            # 内存检查
            import psutil
            memory_info = psutil.virtual_memory()
            checks['total_memory_gb'] = memory_info.total / (1024**3)
            checks['available_memory_gb'] = memory_info.available / (1024**3)
            checks['memory_sufficient'] = memory_info.available > 4 * (1024**3)  # 至少4GB可用
            
            # 磁盘空间检查
            disk_usage = psutil.disk_usage('/')
            checks['disk_free_gb'] = disk_usage.free / (1024**3)
            checks['disk_sufficient'] = disk_usage.free > 10 * (1024**3)  # 至少10GB可用
            
            # 计算得分
            critical_checks = [
                checks.get('python_version', False),
                checks.get('pytorch_installed', False),
                checks.get('cuda_available', False),
                checks.get('memory_sufficient', False),
                checks.get('disk_sufficient', False)
            ]
            
            passed_critical = sum(1 for c in critical_checks if c)
            score = (passed_critical / len(critical_checks)) * 100
            passed = all(critical_checks)
            
            duration = time.time() - start_time
            
            return QualityCheckResult(
                check_name=self.name,
                category=self.category,
                passed=passed,
                score=score,
                details=checks,
                duration=duration
            )
            
        except Exception as e:
            duration = time.time() - start_time
            return QualityCheckResult(
                check_name=self.name,
                category=self.category,
                passed=False,
                score=0.0,
                error_message=str(e),
                details={'traceback': traceback.format_exc()},
                duration=duration
            )


class TrainingQualityValidator:
    """训练质量验证主控制器"""
    
    def __init__(self, config_path: Optional[str] = None):
        """
        初始化训练质量验证器
        
        Args:
            config_path: 配置文件路径
        """
        self.config_path = config_path
        self.logger = get_logger(__name__)
        
        # 初始化验证器
        self.validators = {
            'environment': EnvironmentQualityValidator(),
            'configuration': ConfigurationQualityValidator(config_path),
            'dimension_consistency': DimensionConsistencyQualityValidator(),
            'performance': PerformanceQualityValidator()
        }
        
        # 监控状态
        self.monitoring_active = False
        self.monitoring_thread = None
        self.monitoring_data = defaultdict(list)
        
        self.logger.info("训练质量验证器初始化完成")
    
    def validate_prerequisites(self) -> Tuple[bool, List[QualityCheckResult]]:
        """
        验证启动前提条件
        
        Returns:
            (是否全部通过, 检查结果列表)
        """
        self.logger.info("开始执行启动前验证...")
        
        # 执行环境和配置验证
        prerequisite_validators = ['environment', 'configuration']
        results = []
        
        for validator_name in prerequisite_validators:
            validator = self.validators[validator_name]
            self.logger.info(f"执行 {validator_name} 验证...")
            
            result = validator.validate()
            results.append(result)
            
            if result.passed:
                self.logger.info(f"✅ {validator_name} 验证通过 (得分: {result.score:.1f})")
            else:
                self.logger.error(f"❌ {validator_name} 验证失败: {result.error_message}")
        
        all_passed = all(r.passed for r in results)
        return all_passed, results
    
    def validate_architecture(self) -> Tuple[bool, QualityCheckResult]:
        """
        验证架构一致性
        
        Returns:
            (是否通过, 检查结果)
        """
        self.logger.info("开始执行架构验证...")
        
        validator = self.validators['dimension_consistency']
        result = validator.validate()
        
        if result.passed:
            self.logger.info(f"✅ 架构验证通过 (得分: {result.score:.1f})")
        else:
            self.logger.error(f"❌ 架构验证失败: {result.error_message}")
        
        return result.passed, result
    
    def validate_performance(self) -> Tuple[bool, QualityCheckResult]:
        """
        验证性能基准
        
        Returns:
            (是否通过, 检查结果)
        """
        self.logger.info("开始执行性能验证...")
        
        validator = self.validators['performance']
        result = validator.validate()
        
        if result.passed:
            self.logger.info(f"✅ 性能验证通过 (得分: {result.score:.1f})")
        else:
            self.logger.error(f"❌ 性能验证失败: {result.error_message}")
        
        return result.passed, result
    
    def start_monitoring(self, interval: float = 60.0):
        """
        开始实时监控
        
        Args:
            interval: 监控间隔（秒）
        """
        if self.monitoring_active:
            self.logger.warning("监控已在运行中")
            return
        
        import threading
        
        self.monitoring_active = True
        self.monitoring_thread = threading.Thread(
            target=self._monitoring_loop,
            args=(interval,),
            daemon=True
        )
        self.monitoring_thread.start()
        
        self.logger.info(f"实时监控已启动，间隔: {interval}秒")
    
    def stop_monitoring(self):
        """停止实时监控"""
        if not self.monitoring_active:
            return
        
        self.monitoring_active = False
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=5.0)
        
        self.logger.info("实时监控已停止")
    
    def _monitoring_loop(self, interval: float):
        """监控循环"""
        while self.monitoring_active:
            try:
                # 收集监控数据
                monitor_data = self._collect_monitoring_data()
                
                # 记录数据
                timestamp = datetime.now()
                for key, value in monitor_data.items():
                    self.monitoring_data[key].append({
                        'timestamp': timestamp,
                        'value': value
                    })
                
                # 检查异常
                self._check_monitoring_alerts(monitor_data)
                
            except Exception as e:
                self.logger.error(f"监控循环错误: {e}")
            
            time.sleep(interval)
    
    def _collect_monitoring_data(self) -> Dict[str, Any]:
        """收集监控数据"""
        data = {}
        
        try:
            import psutil
            import torch
            
            # CPU和内存
            data['cpu_percent'] = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            data['memory_percent'] = memory.percent
            data['memory_available_gb'] = memory.available / (1024**3)
            
            # GPU（如果可用）
            if torch.cuda.is_available():
                data['gpu_memory_allocated_mb'] = torch.cuda.memory_allocated() / (1024**2)
                data['gpu_memory_cached_mb'] = torch.cuda.memory_reserved() / (1024**2)
                
                # GPU利用率（需要nvidia-ml-py）
                try:
                    import pynvml as nvml
                    nvml.nvmlInit()
                    handle = nvml.nvmlDeviceGetHandleByIndex(0)
                    utilization = nvml.nvmlDeviceGetUtilizationRates(handle)
                    data['gpu_utilization'] = utilization.gpu
                except:
                    pass
            
        except Exception as e:
            self.logger.error(f"收集监控数据失败: {e}")
        
        return data
    
    def _check_monitoring_alerts(self, data: Dict[str, Any]):
        """检查监控告警"""
        # CPU告警
        if data.get('cpu_percent', 0) > 90:
            self.logger.warning(f"⚠️ CPU使用率过高: {data['cpu_percent']:.1f}%")
        
        # 内存告警
        if data.get('memory_percent', 0) > 85:
            self.logger.warning(f"⚠️ 内存使用率过高: {data['memory_percent']:.1f}%")
        
        # GPU利用率告警
        if 'gpu_utilization' in data and data['gpu_utilization'] < 80:
            self.logger.warning(f"⚠️ GPU利用率偏低: {data['gpu_utilization']:.1f}%")
    
    def evaluate_model(self, model_path: str) -> Dict[str, Any]:
        """
        评估模型质量
        
        Args:
            model_path: 模型文件路径
            
        Returns:
            评估结果
        """
        self.logger.info(f"开始评估模型: {model_path}")
        
        evaluation = {
            'model_path': model_path,
            'timestamp': datetime.now().isoformat(),
            'checks': {}
        }
        
        try:
            import torch
            
            # 加载模型
            model_data = torch.load(model_path, map_location='cpu')
            
            # 基本检查
            evaluation['checks']['file_exists'] = True
            evaluation['checks']['file_size_mb'] = Path(model_path).stat().st_size / (1024**2)
            
            # 模型结构检查
            if 'model_state_dict' in model_data:
                state_dict = model_data['model_state_dict']
                evaluation['checks']['num_parameters'] = sum(p.numel() for p in state_dict.values())
                evaluation['checks']['model_layers'] = list(state_dict.keys())[:10]  # 前10层
            
            # 训练信息检查
            if 'training_info' in model_data:
                info = model_data['training_info']
                evaluation['checks']['epochs_trained'] = info.get('epoch', 0)
                evaluation['checks']['final_loss'] = info.get('loss', float('inf'))
                evaluation['checks']['win_rate'] = info.get('win_rate', 0)
            
            # 性能指标检查
            win_rate = evaluation['checks'].get('win_rate', 0)
            evaluation['checks']['meets_win_rate_target'] = win_rate >= 85  # 85%胜率目标
            
            evaluation['success'] = True
            
        except Exception as e:
            self.logger.error(f"模型评估失败: {e}")
            evaluation['success'] = False
            evaluation['error'] = str(e)
        
        return evaluation
    
    def generate_report(self, 
                       include_prerequisites: bool = True,
                       include_architecture: bool = True,
                       include_performance: bool = True,
                       save_to_file: bool = True) -> QualityReport:
        """
        生成综合质量报告
        
        Args:
            include_prerequisites: 是否包含前提条件检查
            include_architecture: 是否包含架构检查
            include_performance: 是否包含性能检查
            save_to_file: 是否保存到文件
            
        Returns:
            质量报告
        """
        self.logger.info("开始生成质量验证报告...")
        
        all_results = []
        
        # 执行各项检查
        if include_prerequisites:
            _, prereq_results = self.validate_prerequisites()
            all_results.extend(prereq_results)
        
        if include_architecture:
            _, arch_result = self.validate_architecture()
            all_results.append(arch_result)
        
        if include_performance:
            _, perf_result = self.validate_performance()
            all_results.append(perf_result)
        
        # 按类别分组
        categories = defaultdict(lambda: {
            'total': 0,
            'passed': 0,
            'failed': 0,
            'avg_score': 0,
            'checks': []
        })
        
        for result in all_results:
            cat = categories[result.category]
            cat['total'] += 1
            cat['checks'].append(result)
            
            if result.passed:
                cat['passed'] += 1
            else:
                cat['failed'] += 1
        
        # 计算平均分
        for cat in categories.values():
            scores = [r.score for r in cat['checks']]
            cat['avg_score'] = np.mean(scores) if scores else 0
        
        # 生成建议
        recommendations = self._generate_recommendations(all_results)
        
        # 创建报告
        report = QualityReport(
            report_id=f"QR-{datetime.now().strftime('%Y%m%d-%H%M%S')}",
            timestamp=datetime.now().isoformat(),
            total_checks=len(all_results),
            passed_checks=sum(1 for r in all_results if r.passed),
            failed_checks=sum(1 for r in all_results if not r.passed),
            overall_score=np.mean([r.score for r in all_results]) if all_results else 0,
            categories=dict(categories),
            checks=all_results,
            recommendations=recommendations,
            metadata={
                'monitoring_data_points': sum(len(v) for v in self.monitoring_data.values()),
                'config_path': self.config_path
            }
        )
        
        # 保存报告
        if save_to_file:
            self._save_report(report)
        
        self.logger.info(f"质量报告生成完成: {report.report_id}")
        return report
    
    def _generate_recommendations(self, results: List[QualityCheckResult]) -> List[str]:
        """生成优化建议"""
        recommendations = []
        
        # 检查失败的项目
        failed_checks = [r for r in results if not r.passed]
        
        for check in failed_checks:
            if check.check_name == 'environment':
                if not check.details.get('cuda_available', True):
                    recommendations.append("🔧 建议：安装CUDA和GPU驱动以启用GPU加速")
                if not check.details.get('memory_sufficient', True):
                    recommendations.append("🔧 建议：增加系统内存或优化内存使用")
            
            elif check.check_name == 'configuration':
                missing = check.details.get('missing_fields', [])
                if missing:
                    recommendations.append(f"🔧 建议：在配置文件中添加缺失字段: {', '.join(missing)}")
            
            elif check.check_name == 'dimension_consistency':
                if check.score < 95:
                    recommendations.append("🔧 建议：修复维度不一致问题，确保116维架构在所有组件中统一")
            
            elif check.check_name == 'performance_regression':
                regressions = check.details.get('regression_analysis', [])
                for reg in regressions[:3]:  # 前3个最严重的
                    recommendations.append(f"🔧 建议：优化 {reg['test']}，性能下降 {reg['degradation']:.1f}%")
        
        # 通用建议
        overall_score = np.mean([r.score for r in results]) if results else 0
        if overall_score < 80:
            recommendations.append("⚠️ 整体质量评分较低，建议进行全面的系统优化")
        
        if not recommendations:
            recommendations.append("✅ 所有质量检查通过，系统状态良好")
        
        return recommendations
    
    def _save_report(self, report: QualityReport):
        """保存报告到文件"""
        # 创建报告目录
        report_dir = Path("quality_reports")
        report_dir.mkdir(exist_ok=True)
        
        # 保存JSON格式
        json_file = report_dir / f"{report.report_id}.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(asdict(report), f, indent=2, ensure_ascii=False)
        
        # 保存Markdown格式
        md_file = report_dir / f"{report.report_id}.md"
        self._save_markdown_report(report, md_file)
        
        self.logger.info(f"报告已保存: {json_file} 和 {md_file}")
    
    def _save_markdown_report(self, report: QualityReport, file_path: Path):
        """保存Markdown格式报告"""
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(f"# 训练质量验证报告\n\n")
            f.write(f"**报告ID**: {report.report_id}\n")
            f.write(f"**生成时间**: {report.timestamp}\n\n")
            
            # 总体情况
            f.write("## 总体情况\n\n")
            f.write(f"- **总检查项**: {report.total_checks}\n")
            f.write(f"- **通过项**: {report.passed_checks}\n")
            f.write(f"- **失败项**: {report.failed_checks}\n")
            f.write(f"- **整体评分**: {report.overall_score:.1f}/100\n\n")
            
            # 分类统计
            f.write("## 分类统计\n\n")
            f.write("| 类别 | 总数 | 通过 | 失败 | 平均分 |\n")
            f.write("|------|------|------|------|--------|\n")
            
            for cat_name, cat_data in report.categories.items():
                f.write(f"| {cat_name} | {cat_data['total']} | ")
                f.write(f"{cat_data['passed']} | {cat_data['failed']} | ")
                f.write(f"{cat_data['avg_score']:.1f} |\n")
            
            f.write("\n")
            
            # 详细结果
            f.write("## 详细检查结果\n\n")
            
            for check in report.checks:
                status = "✅" if check.passed else "❌"
                f.write(f"### {status} {check.check_name}\n\n")
                f.write(f"- **类别**: {check.category}\n")
                f.write(f"- **得分**: {check.score:.1f}\n")
                f.write(f"- **耗时**: {check.duration:.2f}秒\n")
                
                if check.error_message:
                    f.write(f"- **错误**: {check.error_message}\n")
                
                # 关键细节
                if check.check_name == 'dimension_consistency':
                    test_results = check.details.get('test_results', {})
                    if test_results:
                        f.write("\n**测试结果**:\n")
                        for test, passed in test_results.items():
                            f.write(f"- {test}: {'✅' if passed else '❌'}\n")
                
                elif check.check_name == 'performance_regression':
                    metrics = check.details.get('key_metrics', {})
                    if metrics:
                        f.write("\n**关键指标**:\n")
                        for metric, value in metrics.items():
                            if value is not None:
                                f.write(f"- {metric}: {value}\n")
                
                f.write("\n")
            
            # 优化建议
            f.write("## 优化建议\n\n")
            for i, rec in enumerate(report.recommendations, 1):
                f.write(f"{i}. {rec}\n")


def main():
    """命令行接口"""
    parser = argparse.ArgumentParser(description="训练质量验证工具")
    parser.add_argument('--config', type=str, default="optimized_fixed_config.yaml",
                       help='配置文件路径')
    parser.add_argument('--validate', choices=['all', 'prerequisites', 'architecture', 'performance'],
                       default='all', help='验证类型')
    parser.add_argument('--monitor', action='store_true', help='启动实时监控')
    parser.add_argument('--evaluate-model', type=str, help='评估模型文件')
    parser.add_argument('--report', action='store_true', help='生成质量报告')
    
    args = parser.parse_args()
    
    # 创建验证器
    validator = TrainingQualityValidator(args.config)
    
    # 执行验证
    if args.validate == 'all' or args.validate == 'prerequisites':
        passed, results = validator.validate_prerequisites()
        print(f"\n前提条件验证: {'✅ 通过' if passed else '❌ 失败'}")
    
    if args.validate == 'all' or args.validate == 'architecture':
        passed, result = validator.validate_architecture()
        print(f"\n架构验证: {'✅ 通过' if passed else '❌ 失败'} (得分: {result.score:.1f})")
    
    if args.validate == 'all' or args.validate == 'performance':
        passed, result = validator.validate_performance()
        print(f"\n性能验证: {'✅ 通过' if passed else '❌ 失败'} (得分: {result.score:.1f})")
    
    # 启动监控
    if args.monitor:
        print("\n启动实时监控...")
        validator.start_monitoring()
        try:
            input("按Enter键停止监控...\n")
        finally:
            validator.stop_monitoring()
    
    # 评估模型
    if args.evaluate_model:
        evaluation = validator.evaluate_model(args.evaluate_model)
        print(f"\n模型评估结果:")
        print(json.dumps(evaluation, indent=2))
    
    # 生成报告
    if args.report:
        report = validator.generate_report()
        print(f"\n质量报告已生成: {report.report_id}")
        print(f"整体评分: {report.overall_score:.1f}/100")
        print(f"\n优化建议:")
        for rec in report.recommendations:
            print(f"  {rec}")


if __name__ == "__main__":
    main()