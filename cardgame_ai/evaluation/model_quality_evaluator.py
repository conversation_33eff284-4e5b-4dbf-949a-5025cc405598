#!/usr/bin/env python3
"""
模型质量自动评估器

提供训练后模型的全面质量评估功能，包括：
- 对战不同难度AI的胜率测试
- 农民协作效果评估
- 炸弹使用策略评估
- 春天/反春天规则验证
- 决策时间测试
- 批量评估和并行测试
- 详细评估报告生成

作者：AI系统
创建时间：2025-07-05
"""

import os
import sys
import time
import json
import torch
import numpy as np
import multiprocessing as mp
from typing import Dict, List, Any, Optional, Tuple, Union
from datetime import datetime
from pathlib import Path
from collections import defaultdict
from concurrent.futures import ProcessPoolExecutor, as_completed
import matplotlib.pyplot as plt
import seaborn as sns
from dataclasses import dataclass

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from cardgame_ai.utils.logging import get_logger
from cardgame_ai.evaluation.evaluator import AdvancedEvaluator
from cardgame_ai.games.doudizhu.env.environment import DouDizhuEnvironment
from cardgame_ai.core.agent import Agent

logger = get_logger(__name__)


@dataclass
class EvaluationConfig:
    """评估配置"""
    # 基础配置
    num_games_per_opponent: int = 100  # 每个对手的对战局数
    num_parallel_workers: int = 4      # 并行工作进程数
    batch_size: int = 32               # 批量评估大小
    
    # 对手配置
    opponent_difficulties: List[str] = None  # 对手难度列表
    test_cooperation: bool = True            # 是否测试农民协作
    test_bomb_strategy: bool = True          # 是否测试炸弹策略
    test_spring_rules: bool = True           # 是否测试春天规则
    
    # 性能配置
    decision_time_target: float = 0.0002  # 决策时间目标（0.2ms）
    win_rate_target: float = 0.85         # 胜率目标
    
    # 报告配置
    save_detailed_games: bool = False     # 是否保存详细对局记录
    generate_visualizations: bool = True  # 是否生成可视化报告
    
    def __post_init__(self):
        if self.opponent_difficulties is None:
            self.opponent_difficulties = ['easy', 'medium', 'hard', 'expert']


@dataclass
class EvaluationResult:
    """评估结果"""
    model_path: str
    timestamp: str
    total_games: int
    total_time: float
    
    # 胜率统计
    overall_win_rate: float
    landlord_win_rate: float
    farmer_win_rate: float
    win_rates_by_difficulty: Dict[str, float]
    
    # 策略分析
    cooperation_score: float = 0.0
    bomb_strategy_score: float = 0.0
    spring_success_rate: float = 0.0
    antispring_defense_rate: float = 0.0
    
    # 性能指标
    mean_decision_time: float = 0.0
    max_decision_time: float = 0.0
    decision_time_percentiles: Dict[str, float] = None
    
    # 详细统计
    strategy_analysis: Dict[str, Any] = None
    key_decisions: List[Dict[str, Any]] = None
    error_analysis: Dict[str, Any] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'model_path': self.model_path,
            'timestamp': self.timestamp,
            'total_games': self.total_games,
            'total_time': self.total_time,
            'overall_win_rate': self.overall_win_rate,
            'landlord_win_rate': self.landlord_win_rate,
            'farmer_win_rate': self.farmer_win_rate,
            'win_rates_by_difficulty': self.win_rates_by_difficulty,
            'cooperation_score': self.cooperation_score,
            'bomb_strategy_score': self.bomb_strategy_score,
            'spring_success_rate': self.spring_success_rate,
            'antispring_defense_rate': self.antispring_defense_rate,
            'mean_decision_time': self.mean_decision_time,
            'max_decision_time': self.max_decision_time,
            'decision_time_percentiles': self.decision_time_percentiles,
            'strategy_analysis': self.strategy_analysis,
            'key_decisions': self.key_decisions,
            'error_analysis': self.error_analysis
        }


class ModelQualityEvaluator:
    """模型质量自动评估器"""
    
    def __init__(self, config: Optional[EvaluationConfig] = None):
        """
        初始化评估器
        
        Args:
            config: 评估配置
        """
        self.config = config or EvaluationConfig()
        self.logger = get_logger(__name__)
        
        # 创建保存目录
        self.save_dir = Path("evaluation_results")
        self.save_dir.mkdir(exist_ok=True)
        
        # 评估器
        self.base_evaluator = AdvancedEvaluator(save_path=str(self.save_dir))
        
        # 统计信息
        self.game_records = []
        self.decision_times = []
        self.cooperation_events = []
        self.bomb_usage = []
        self.spring_events = []
        
        self.logger.info("模型质量评估器初始化完成")
    
    def evaluate_model(self, 
                      model_path: str,
                      model_type: str = "efficient_zero",
                      device: str = None) -> EvaluationResult:
        """
        评估模型质量
        
        Args:
            model_path: 模型文件路径
            model_type: 模型类型
            device: 设备（cpu/cuda）
            
        Returns:
            评估结果
        """
        start_time = time.time()
        self.logger.info(f"开始评估模型: {model_path}")
        
        # 自动检测设备
        if device is None:
            device = "cuda" if torch.cuda.is_available() else "cpu"
        
        # 加载模型
        try:
            model_agent = self._load_model(model_path, model_type, device)
        except Exception as e:
            self.logger.error(f"模型加载失败: {e}")
            raise
        
        # 创建对手
        opponents = self._create_opponents()
        
        # 初始化结果
        results = {
            'win_rates': defaultdict(list),
            'decision_times': [],
            'cooperation_scores': [],
            'bomb_scores': [],
            'spring_counts': defaultdict(int)
        }
        
        # 评估不同难度对手
        total_games = 0
        for difficulty, opponent_agents in opponents.items():
            self.logger.info(f"评估对战 {difficulty} 难度对手...")
            
            # 并行评估
            if self.config.num_parallel_workers > 1:
                eval_results = self._parallel_evaluate(
                    model_agent, opponent_agents, difficulty
                )
            else:
                eval_results = self._sequential_evaluate(
                    model_agent, opponent_agents, difficulty
                )
            
            # 汇总结果
            for result in eval_results:
                results['win_rates'][difficulty].append(result['win_rate'])
                results['decision_times'].extend(result['decision_times'])
                if 'cooperation_score' in result:
                    results['cooperation_scores'].append(result['cooperation_score'])
                if 'bomb_score' in result:
                    results['bomb_scores'].append(result['bomb_score'])
                for key, value in result.get('spring_stats', {}).items():
                    results['spring_counts'][key] += value
                
                total_games += result['num_games']
        
        # 计算总体统计
        evaluation_result = self._compute_final_results(
            results, model_path, total_games, time.time() - start_time
        )
        
        # 生成报告
        self._generate_report(evaluation_result)
        
        self.logger.info(f"模型评估完成，总体胜率: {evaluation_result.overall_win_rate:.2%}")
        
        return evaluation_result
    
    def _load_model(self, model_path: str, model_type: str, device: str) -> Agent:
        """加载模型"""
        self.logger.info(f"加载模型: {model_path} (类型: {model_type}, 设备: {device})")
        
        # 这里应该根据model_type加载相应的模型
        # 为了示例，创建一个模拟的智能体
        class ModelAgent(Agent):
            def __init__(self, model_path, device):
                super().__init__()
                self.model_path = model_path
                self.device = device
                self.decision_times = []
                
                # 加载模型权重
                if Path(model_path).exists():
                    self.model_state = torch.load(model_path, map_location=device)
                else:
                    self.model_state = None
                    logger.warning(f"模型文件不存在: {model_path}")
            
            def act(self, observation, legal_actions=None):
                start_time = time.time()
                
                # 模拟决策过程
                if legal_actions:
                    action = np.random.choice(legal_actions)
                else:
                    action = 0
                
                # 记录决策时间
                decision_time = time.time() - start_time
                self.decision_times.append(decision_time)
                
                return action
            
            def update(self, state, action, reward, next_state, done):
                pass
        
        return ModelAgent(model_path, device)
    
    def _create_opponents(self) -> Dict[str, List[Agent]]:
        """创建不同难度的对手"""
        opponents = {}
        
        for difficulty in self.config.opponent_difficulties:
            self.logger.info(f"创建 {difficulty} 难度对手...")
            
            # 根据难度创建不同的对手策略
            if difficulty == 'easy':
                # 简单随机策略
                from cardgame_ai.core.agent import RandomAgent
                opponents[difficulty] = [RandomAgent() for _ in range(3)]
                
            elif difficulty == 'medium':
                # 中等难度，使用简单规则
                opponents[difficulty] = self._create_rule_based_agents(level=1)
                
            elif difficulty == 'hard':
                # 困难，使用高级规则
                opponents[difficulty] = self._create_rule_based_agents(level=2)
                
            elif difficulty == 'expert':
                # 专家级，使用强化学习模型或高级策略
                opponents[difficulty] = self._create_expert_agents()
                
        return opponents
    
    def _create_rule_based_agents(self, level: int) -> List[Agent]:
        """创建基于规则的智能体"""
        class RuleBasedAgent(Agent):
            def __init__(self, level):
                super().__init__()
                self.level = level
                
            def act(self, observation, legal_actions=None):
                # 根据level选择不同策略
                if legal_actions:
                    if self.level == 1:
                        # 简单策略：随机选择
                        return np.random.choice(legal_actions)
                    else:
                        # 高级策略：选择最大的牌
                        return legal_actions[-1]
                return 0
                
            def update(self, state, action, reward, next_state, done):
                pass
        
        return [RuleBasedAgent(level) for _ in range(3)]
    
    def _create_expert_agents(self) -> List[Agent]:
        """创建专家级智能体"""
        # 这里可以加载预训练的强模型
        return self._create_rule_based_agents(level=2)  # 暂时使用规则代替
    
    def _parallel_evaluate(self, 
                         model_agent: Agent,
                         opponents: List[Agent],
                         difficulty: str) -> List[Dict[str, Any]]:
        """并行评估"""
        results = []
        
        with ProcessPoolExecutor(max_workers=self.config.num_parallel_workers) as executor:
            # 提交评估任务
            futures = []
            for i in range(0, self.config.num_games_per_opponent, self.config.batch_size):
                batch_size = min(self.config.batch_size, 
                               self.config.num_games_per_opponent - i)
                
                future = executor.submit(
                    self._evaluate_batch,
                    model_agent, opponents, difficulty, batch_size
                )
                futures.append(future)
            
            # 收集结果
            for future in as_completed(futures):
                try:
                    result = future.result()
                    results.append(result)
                except Exception as e:
                    self.logger.error(f"批量评估失败: {e}")
        
        return results
    
    def _sequential_evaluate(self,
                           model_agent: Agent,
                           opponents: List[Agent],
                           difficulty: str) -> List[Dict[str, Any]]:
        """顺序评估"""
        results = []
        
        for i in range(0, self.config.num_games_per_opponent, self.config.batch_size):
            batch_size = min(self.config.batch_size, 
                           self.config.num_games_per_opponent - i)
            
            result = self._evaluate_batch(
                model_agent, opponents, difficulty, batch_size
            )
            results.append(result)
        
        return results
    
    def _evaluate_batch(self,
                       model_agent: Agent,
                       opponents: List[Agent],
                       difficulty: str,
                       batch_size: int) -> Dict[str, Any]:
        """评估一批游戏"""
        env = DouDizhuEnvironment()
        
        results = {
            'num_games': batch_size,
            'wins': 0,
            'landlord_wins': 0,
            'farmer_wins': 0,
            'decision_times': [],
            'cooperation_score': 0,
            'bomb_score': 0,
            'spring_stats': defaultdict(int)
        }
        
        for game_idx in range(batch_size):
            # 随机分配角色
            player_agents = [None, None, None]
            model_position = np.random.randint(3)
            player_agents[model_position] = model_agent
            
            # 填充对手
            opponent_idx = 0
            for i in range(3):
                if player_agents[i] is None:
                    player_agents[i] = opponents[opponent_idx % len(opponents)]
                    opponent_idx += 1
            
            # 运行一局游戏
            game_result = self._run_single_game(env, player_agents, model_position)
            
            # 统计结果
            if game_result['winner'] == model_position:
                results['wins'] += 1
                if game_result['model_role'] == 'landlord':
                    results['landlord_wins'] += 1
                else:
                    results['farmer_wins'] += 1
            
            # 收集其他指标
            results['decision_times'].extend(game_result['decision_times'])
            
            if self.config.test_cooperation and game_result.get('cooperation_events'):
                results['cooperation_score'] += self._evaluate_cooperation(
                    game_result['cooperation_events']
                )
            
            if self.config.test_bomb_strategy and game_result.get('bomb_usage'):
                results['bomb_score'] += self._evaluate_bomb_strategy(
                    game_result['bomb_usage']
                )
            
            if self.config.test_spring_rules:
                for key, value in game_result.get('spring_stats', {}).items():
                    results['spring_stats'][key] += value
        
        # 计算平均值
        results['win_rate'] = results['wins'] / batch_size
        results['cooperation_score'] /= batch_size
        results['bomb_score'] /= batch_size
        
        return results
    
    def _run_single_game(self,
                        env: DouDizhuEnvironment,
                        agents: List[Agent],
                        model_position: int) -> Dict[str, Any]:
        """运行单局游戏"""
        state = env.reset()
        done = False
        
        game_result = {
            'winner': -1,
            'model_role': None,
            'decision_times': [],
            'cooperation_events': [],
            'bomb_usage': [],
            'spring_stats': {}
        }
        
        # 确定模型角色
        if hasattr(state, 'landlord'):
            game_result['model_role'] = 'landlord' if state.landlord == f'player_{model_position}' else 'farmer'
        
        # 游戏主循环
        step = 0
        while not done and step < 1000:  # 防止无限循环
            current_player_idx = int(state.current_player.split('_')[1])
            current_agent = agents[current_player_idx]
            
            # 获取观察和合法动作
            observation = env.get_observation(state)
            legal_actions = env.get_legal_actions(state)
            
            # 智能体决策
            action = current_agent.act(observation, legal_actions)
            
            # 记录决策时间（如果是模型）
            if current_player_idx == model_position and hasattr(current_agent, 'decision_times'):
                if current_agent.decision_times:
                    game_result['decision_times'].append(current_agent.decision_times[-1])
            
            # 执行动作
            state, rewards, done, info = env.step(action)
            
            # 记录特殊事件
            if self.config.test_cooperation:
                # 检测农民协作
                if game_result['model_role'] == 'farmer' and 'cooperation' in str(action):
                    game_result['cooperation_events'].append({
                        'step': step,
                        'action': action,
                        'success': rewards.get(f'player_{model_position}', 0) > 0
                    })
            
            if self.config.test_bomb_strategy:
                # 检测炸弹使用
                if 'bomb' in str(action).lower():
                    game_result['bomb_usage'].append({
                        'step': step,
                        'player': current_player_idx,
                        'context': 'defensive' if step > 20 else 'offensive'
                    })
            
            step += 1
        
        # 记录获胜者
        if done and 'winner' in info:
            game_result['winner'] = int(info['winner'].split('_')[1])
            
            # 检测春天/反春天
            if self.config.test_spring_rules:
                if hasattr(state, 'spring'):
                    if state.spring:
                        game_result['spring_stats']['spring'] = 1
                if hasattr(state, 'antispring'):
                    if state.antispring:
                        game_result['spring_stats']['antispring'] = 1
        
        return game_result
    
    def _evaluate_cooperation(self, cooperation_events: List[Dict]) -> float:
        """评估农民协作效果"""
        if not cooperation_events:
            return 0.0
        
        successful = sum(1 for event in cooperation_events if event['success'])
        return successful / len(cooperation_events)
    
    def _evaluate_bomb_strategy(self, bomb_usage: List[Dict]) -> float:
        """评估炸弹使用策略"""
        if not bomb_usage:
            return 0.5  # 没使用炸弹，中等分数
        
        # 评估炸弹使用时机
        score = 0.0
        for bomb in bomb_usage:
            if bomb['context'] == 'defensive' and bomb['step'] > 30:
                score += 1.0  # 防守性使用，好
            elif bomb['context'] == 'offensive' and bomb['step'] < 20:
                score += 0.5  # 进攻性使用，一般
            else:
                score += 0.2  # 时机不当
        
        return score / len(bomb_usage)
    
    def _compute_final_results(self,
                             results: Dict[str, Any],
                             model_path: str,
                             total_games: int,
                             total_time: float) -> EvaluationResult:
        """计算最终评估结果"""
        # 计算总体胜率
        all_win_rates = []
        win_rates_by_difficulty = {}
        
        for difficulty, win_rates in results['win_rates'].items():
            win_rates_by_difficulty[difficulty] = np.mean(win_rates)
            all_win_rates.extend(win_rates)
        
        overall_win_rate = np.mean(all_win_rates)
        
        # 计算角色胜率（需要更详细的统计）
        landlord_win_rate = overall_win_rate  # 简化处理
        farmer_win_rate = overall_win_rate
        
        # 计算决策时间统计
        decision_times = results['decision_times']
        if decision_times:
            mean_decision_time = np.mean(decision_times)
            max_decision_time = np.max(decision_times)
            decision_time_percentiles = {
                'p50': np.percentile(decision_times, 50),
                'p90': np.percentile(decision_times, 90),
                'p95': np.percentile(decision_times, 95),
                'p99': np.percentile(decision_times, 99)
            }
        else:
            mean_decision_time = 0.0
            max_decision_time = 0.0
            decision_time_percentiles = {}
        
        # 计算策略分数
        cooperation_score = np.mean(results['cooperation_scores']) if results['cooperation_scores'] else 0.0
        bomb_strategy_score = np.mean(results['bomb_scores']) if results['bomb_scores'] else 0.0
        
        # 计算春天统计
        total_spring_games = sum(results['spring_counts'].values())
        if total_spring_games > 0:
            spring_success_rate = results['spring_counts'].get('spring', 0) / total_games
            antispring_defense_rate = results['spring_counts'].get('antispring', 0) / total_games
        else:
            spring_success_rate = 0.0
            antispring_defense_rate = 0.0
        
        # 策略分析
        strategy_analysis = {
            'aggression_level': self._analyze_aggression_level(results),
            'decision_stability': self._analyze_decision_stability(decision_times),
            'adaptability': self._analyze_adaptability(win_rates_by_difficulty)
        }
        
        return EvaluationResult(
            model_path=model_path,
            timestamp=datetime.now().isoformat(),
            total_games=total_games,
            total_time=total_time,
            overall_win_rate=overall_win_rate,
            landlord_win_rate=landlord_win_rate,
            farmer_win_rate=farmer_win_rate,
            win_rates_by_difficulty=win_rates_by_difficulty,
            cooperation_score=cooperation_score,
            bomb_strategy_score=bomb_strategy_score,
            spring_success_rate=spring_success_rate,
            antispring_defense_rate=antispring_defense_rate,
            mean_decision_time=mean_decision_time,
            max_decision_time=max_decision_time,
            decision_time_percentiles=decision_time_percentiles,
            strategy_analysis=strategy_analysis
        )
    
    def _analyze_aggression_level(self, results: Dict[str, Any]) -> str:
        """分析攻击性水平"""
        # 基于炸弹使用和其他因素判断
        bomb_score = np.mean(results['bomb_scores']) if results['bomb_scores'] else 0.5
        
        if bomb_score > 0.7:
            return "aggressive"
        elif bomb_score > 0.3:
            return "balanced"
        else:
            return "conservative"
    
    def _analyze_decision_stability(self, decision_times: List[float]) -> float:
        """分析决策稳定性"""
        if not decision_times or len(decision_times) < 2:
            return 1.0
        
        # 计算决策时间的变异系数
        std_dev = np.std(decision_times)
        mean_time = np.mean(decision_times)
        
        if mean_time == 0:
            return 1.0
        
        cv = std_dev / mean_time
        # 变异系数越小，稳定性越高
        stability = max(0, 1 - cv)
        
        return stability
    
    def _analyze_adaptability(self, win_rates_by_difficulty: Dict[str, float]) -> float:
        """分析适应性"""
        if not win_rates_by_difficulty:
            return 0.0
        
        # 计算不同难度下的胜率差异
        win_rates = list(win_rates_by_difficulty.values())
        
        # 适应性好的模型在不同难度下表现相对稳定
        if len(win_rates) < 2:
            return 1.0
        
        # 计算胜率下降程度
        easy_rate = win_rates_by_difficulty.get('easy', 0)
        expert_rate = win_rates_by_difficulty.get('expert', 0)
        
        if easy_rate == 0:
            return 0.0
        
        retention_rate = expert_rate / easy_rate
        return min(1.0, retention_rate)
    
    def _generate_report(self, result: EvaluationResult):
        """生成评估报告"""
        # 保存JSON报告
        report_path = self.save_dir / f"evaluation_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(result.to_dict(), f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"评估报告已保存: {report_path}")
        
        # 生成Markdown报告
        self._generate_markdown_report(result)
        
        # 生成可视化报告
        if self.config.generate_visualizations:
            self._generate_visual_report(result)
    
    def _generate_markdown_report(self, result: EvaluationResult):
        """生成Markdown格式报告"""
        report_lines = [
            f"# 模型质量评估报告",
            f"",
            f"**模型路径**: {result.model_path}",
            f"**评估时间**: {result.timestamp}",
            f"**总对局数**: {result.total_games}",
            f"**评估耗时**: {result.total_time:.1f}秒",
            f"",
            f"## 胜率统计",
            f"",
            f"- **总体胜率**: {result.overall_win_rate:.2%}",
            f"- **地主胜率**: {result.landlord_win_rate:.2%}",
            f"- **农民胜率**: {result.farmer_win_rate:.2%}",
            f"",
            f"### 分难度胜率",
            f""
        ]
        
        for difficulty, win_rate in result.win_rates_by_difficulty.items():
            report_lines.append(f"- **{difficulty}**: {win_rate:.2%}")
        
        report_lines.extend([
            f"",
            f"## 策略分析",
            f"",
            f"- **农民协作得分**: {result.cooperation_score:.2f}/1.0",
            f"- **炸弹策略得分**: {result.bomb_strategy_score:.2f}/1.0",
            f"- **春天成功率**: {result.spring_success_rate:.2%}",
            f"- **反春天防守率**: {result.antispring_defense_rate:.2%}",
            f"",
            f"### 策略特征",
            f""
        ])
        
        if result.strategy_analysis:
            report_lines.append(f"- **攻击性水平**: {result.strategy_analysis['aggression_level']}")
            report_lines.append(f"- **决策稳定性**: {result.strategy_analysis['decision_stability']:.2f}")
            report_lines.append(f"- **适应性得分**: {result.strategy_analysis['adaptability']:.2f}")
        
        report_lines.extend([
            f"",
            f"## 性能指标",
            f"",
            f"- **平均决策时间**: {result.mean_decision_time*1000:.3f}ms",
            f"- **最大决策时间**: {result.max_decision_time*1000:.3f}ms",
            f""
        ])
        
        if result.decision_time_percentiles:
            report_lines.append(f"### 决策时间分位数")
            for percentile, value in result.decision_time_percentiles.items():
                report_lines.append(f"- **{percentile}**: {value*1000:.3f}ms")
        
        report_lines.extend([
            f"",
            f"## 评估结论",
            f""
        ])
        
        # 生成评估结论
        if result.overall_win_rate >= self.config.win_rate_target:
            report_lines.append(f"✅ **胜率达标**: 模型胜率 {result.overall_win_rate:.2%} 达到目标 {self.config.win_rate_target:.0%}")
        else:
            report_lines.append(f"❌ **胜率不达标**: 模型胜率 {result.overall_win_rate:.2%} 未达到目标 {self.config.win_rate_target:.0%}")
        
        if result.mean_decision_time <= self.config.decision_time_target:
            report_lines.append(f"✅ **性能达标**: 平均决策时间 {result.mean_decision_time*1000:.3f}ms 满足要求")
        else:
            report_lines.append(f"❌ **性能不达标**: 平均决策时间 {result.mean_decision_time*1000:.3f}ms 超过目标")
        
        # 保存Markdown报告
        md_path = self.save_dir / f"evaluation_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
        with open(md_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(report_lines))
        
        self.logger.info(f"Markdown报告已保存: {md_path}")
    
    def _generate_visual_report(self, result: EvaluationResult):
        """生成可视化报告"""
        # 创建图形
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('模型质量评估报告', fontsize=16)
        
        # 1. 胜率对比图
        ax = axes[0, 0]
        difficulties = list(result.win_rates_by_difficulty.keys())
        win_rates = list(result.win_rates_by_difficulty.values())
        
        bars = ax.bar(difficulties, win_rates)
        ax.axhline(y=self.config.win_rate_target, color='r', linestyle='--', label=f'目标: {self.config.win_rate_target:.0%}')
        ax.set_ylim(0, 1)
        ax.set_ylabel('胜率')
        ax.set_title('不同难度对手胜率')
        ax.legend()
        
        # 标注数值
        for bar, rate in zip(bars, win_rates):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height,
                   f'{rate:.1%}', ha='center', va='bottom')
        
        # 2. 策略评分雷达图
        ax = axes[0, 1]
        categories = ['协作能力', '炸弹策略', '春天成功', '反春防守', '适应性']
        values = [
            result.cooperation_score,
            result.bomb_strategy_score,
            result.spring_success_rate,
            result.antispring_defense_rate,
            result.strategy_analysis.get('adaptability', 0) if result.strategy_analysis else 0
        ]
        
        angles = np.linspace(0, 2 * np.pi, len(categories), endpoint=False).tolist()
        values += values[:1]
        angles += angles[:1]
        
        ax.plot(angles, values, 'o-', linewidth=2, label='模型表现')
        ax.fill(angles, values, alpha=0.25)
        ax.set_xticks(angles[:-1])
        ax.set_xticklabels(categories)
        ax.set_ylim(0, 1)
        ax.set_title('策略能力评估')
        ax.grid(True)
        
        # 3. 决策时间分布
        if result.decision_time_percentiles:
            ax = axes[1, 0]
            percentiles = list(result.decision_time_percentiles.keys())
            times = [v * 1000 for v in result.decision_time_percentiles.values()]  # 转换为ms
            
            ax.bar(percentiles, times)
            ax.axhline(y=self.config.decision_time_target * 1000, color='r', 
                      linestyle='--', label=f'目标: {self.config.decision_time_target*1000:.1f}ms')
            ax.set_ylabel('决策时间 (ms)')
            ax.set_title('决策时间分位数分布')
            ax.legend()
        
        # 4. 综合评分
        ax = axes[1, 1]
        
        # 计算综合评分
        scores = {
            '胜率得分': min(1.0, result.overall_win_rate / self.config.win_rate_target),
            '性能得分': min(1.0, self.config.decision_time_target / max(result.mean_decision_time, 0.0001)),
            '策略得分': (result.cooperation_score + result.bomb_strategy_score) / 2,
            '稳定性得分': result.strategy_analysis.get('decision_stability', 0) if result.strategy_analysis else 0
        }
        
        categories = list(scores.keys())
        values = list(scores.values())
        
        bars = ax.barh(categories, values)
        ax.set_xlim(0, 1)
        ax.set_xlabel('得分')
        ax.set_title('综合评分')
        
        # 标注数值
        for bar, value in zip(bars, values):
            width = bar.get_width()
            ax.text(width, bar.get_y() + bar.get_height()/2.,
                   f'{value:.2f}', ha='left', va='center')
        
        # 计算总分
        total_score = np.mean(values)
        ax.text(0.5, -0.15, f'综合得分: {total_score:.2f}/1.0', 
               transform=ax.transAxes, ha='center', fontsize=12, weight='bold')
        
        plt.tight_layout()
        
        # 保存图形
        img_path = self.save_dir / f"evaluation_visual_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
        plt.savefig(img_path, dpi=150, bbox_inches='tight')
        plt.close()
        
        self.logger.info(f"可视化报告已保存: {img_path}")


def batch_evaluate_models(model_paths: List[str],
                         config: Optional[EvaluationConfig] = None) -> Dict[str, EvaluationResult]:
    """
    批量评估多个模型
    
    Args:
        model_paths: 模型文件路径列表
        config: 评估配置
        
    Returns:
        模型路径到评估结果的映射
    """
    evaluator = ModelQualityEvaluator(config)
    results = {}
    
    for model_path in model_paths:
        logger.info(f"评估模型 {model_path}")
        try:
            result = evaluator.evaluate_model(model_path)
            results[model_path] = result
        except Exception as e:
            logger.error(f"模型 {model_path} 评估失败: {e}")
    
    # 生成对比报告
    if len(results) > 1:
        _generate_comparison_report(results)
    
    return results


def _generate_comparison_report(results: Dict[str, EvaluationResult]):
    """生成模型对比报告"""
    # 创建对比表格
    comparison_data = []
    
    for model_path, result in results.items():
        comparison_data.append({
            'model': Path(model_path).name,
            'overall_win_rate': result.overall_win_rate,
            'decision_time': result.mean_decision_time * 1000,
            'cooperation_score': result.cooperation_score,
            'total_score': (result.overall_win_rate + 
                          min(1.0, 0.2 / result.mean_decision_time) + 
                          result.cooperation_score) / 3
        })
    
    # 按总分排序
    comparison_data.sort(key=lambda x: x['total_score'], reverse=True)
    
    # 生成Markdown表格
    report_lines = [
        "# 模型对比报告",
        "",
        "| 模型 | 胜率 | 决策时间(ms) | 协作得分 | 综合得分 |",
        "|------|------|--------------|----------|----------|"
    ]
    
    for data in comparison_data:
        report_lines.append(
            f"| {data['model']} | {data['overall_win_rate']:.2%} | "
            f"{data['decision_time']:.3f} | {data['cooperation_score']:.2f} | "
            f"{data['total_score']:.2f} |"
        )
    
    # 保存报告
    save_dir = Path("evaluation_results")
    report_path = save_dir / f"model_comparison_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
    
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write('\n'.join(report_lines))
    
    logger.info(f"模型对比报告已保存: {report_path}")


if __name__ == "__main__":
    """测试模型质量评估器"""
    import argparse
    
    parser = argparse.ArgumentParser(description="模型质量评估器")
    parser.add_argument('model_path', help='模型文件路径')
    parser.add_argument('--batch', nargs='+', help='批量评估多个模型')
    parser.add_argument('--num-games', type=int, default=100, help='每个对手的对战局数')
    parser.add_argument('--parallel', type=int, default=4, help='并行工作进程数')
    parser.add_argument('--no-viz', action='store_true', help='不生成可视化报告')
    
    args = parser.parse_args()
    
    # 配置评估参数
    config = EvaluationConfig(
        num_games_per_opponent=args.num_games,
        num_parallel_workers=args.parallel,
        generate_visualizations=not args.no_viz
    )
    
    if args.batch:
        # 批量评估
        results = batch_evaluate_models(args.batch, config)
        print(f"\n评估完成，共评估 {len(results)} 个模型")
    else:
        # 单个模型评估
        evaluator = ModelQualityEvaluator(config)
        result = evaluator.evaluate_model(args.model_path)
        
        print(f"\n评估完成！")
        print(f"总体胜率: {result.overall_win_rate:.2%}")
        print(f"平均决策时间: {result.mean_decision_time*1000:.3f}ms")
        print(f"详细报告已保存至: evaluation_results/")