#!/usr/bin/env python3
"""
日志系统健康检查模块

专门针对5层日志架构的健康检查机制，包括：
- 日志完整性验证
- 配置合法性检查
- 性能监控与优化建议
- 自动修复机制
- 健康报告生成

作者：AI系统
创建时间：2025-07-06
"""

import os
import sys
import time
import json
import threading
import psutil
from typing import Dict, Any, Optional, List, Tuple, Set
from datetime import datetime, timedelta
from pathlib import Path
from collections import deque, defaultdict
import yaml
import gzip
from dataclasses import dataclass, asdict

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from cardgame_ai.utils.logging import get_logger

from cardgame_ai.monitoring.alert_system import AlertEngine, AlertRule


@dataclass
class LogLayerHealth:
    """日志层健康状态"""
    layer_name: str
    is_healthy: bool
    last_write_time: Optional[float] = None
    file_count: int = 0
    total_size_mb: float = 0.0
    write_speed_mb_per_sec: float = 0.0
    issues: List[str] = None
    
    def __post_init__(self):
        if self.issues is None:
            self.issues = []


@dataclass 
class LogSystemHealth:
    """日志系统整体健康状态"""
    overall_status: str  # healthy, warning, critical, failed
    timestamp: float
    layer_health: Dict[str, LogLayerHealth]
    configuration_valid: bool
    performance_metrics: Dict[str, float]
    auto_repairs_performed: List[str]
    recommendations: List[str]
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        result = asdict(self)
        # 转换layer_health中的LogLayerHealth对象
        result['layer_health'] = {
            k: asdict(v) for k, v in self.layer_health.items()
        }
        return result


class LogHealthChecker:
    """日志健康检查器"""
    
    # 5层日志架构定义
    LOG_LAYERS = ['debug', 'training', 'game', 'summary', 'games']
    
    # 健康阈值
    THRESHOLDS = {
        'max_file_age_hours': 24,  # 最大文件年龄（小时）
        'min_write_speed_mb': 0.1,  # 最小写入速度（MB/s）
        'max_file_size_mb': 500,    # 最大单文件大小（MB）
        'max_layer_size_gb': 10,    # 最大层总大小（GB）
        'min_free_space_gb': 5,     # 最小剩余空间（GB）
        'max_io_latency_ms': 100,   # 最大IO延迟（毫秒）
        'max_buffer_usage': 0.9,    # 最大缓冲区使用率
    }
    
    def __init__(self, 
                 log_base_dir: str = "logs",
                 config_file: Optional[str] = None,
                 check_interval: float = 60.0,
                 enable_auto_repair: bool = True):
        """
        初始化健康检查器
        
        Args:
            log_base_dir: 日志基础目录
            config_file: 配置文件路径
            check_interval: 检查间隔（秒）
            enable_auto_repair: 是否启用自动修复
        """
        self.log_base_dir = Path(log_base_dir)
        self.config_file = config_file
        self.check_interval = check_interval
        self.enable_auto_repair = enable_auto_repair
        
        # 获取日志器
        self.logger = get_logger()
        
        # 告警引擎
        self.alert_engine = AlertEngine()
        
        # 健康检查线程
        self.check_thread = None
        self.checking = False
        
        # 统计信息
        self.health_history = deque(maxlen=100)
        self.repair_history = []
        self.last_check_time = 0
        self.consecutive_failures = 0
        
        # 性能监控
        self.io_latencies = deque(maxlen=50)
        self.write_speeds = defaultdict(lambda: deque(maxlen=50))
        
        # 配置缓存
        self._config_cache = None
        self._config_load_time = 0
        
        self.logger.info("日志健康检查器初始化完成")
    
    def start(self):
        """启动健康检查"""
        if self.checking:
            self.logger.warning("健康检查已在运行")
            return
        
        self.checking = True
        self.check_thread = threading.Thread(target=self._check_loop, daemon=True)
        self.check_thread.start()
        
        self.logger.info("日志健康检查已启动")
    
    def stop(self):
        """停止健康检查"""
        if not self.checking:
            return
        
        self.checking = False
        if self.check_thread:
            self.check_thread.join(timeout=5.0)
        
        # 生成最终报告
        self._generate_final_report()
        
        self.logger.info("日志健康检查已停止")
    
    def _check_loop(self):
        """健康检查主循环"""
        while self.checking:
            try:
                # 执行健康检查
                health_status = self.perform_health_check()
                
                # 记录历史
                self.health_history.append(health_status)
                self.last_check_time = time.time()
                
                # 处理告警
                self._process_health_alerts(health_status)
                
                # 自动修复
                if self.enable_auto_repair and health_status.overall_status in ['warning', 'critical']:
                    self._perform_auto_repair(health_status)
                
            except Exception as e:
                self.logger.error(f"健康检查错误: {e}")
                self.consecutive_failures += 1
                
                if self.consecutive_failures > 3:
                    # 创建告警规则
                    rule = AlertRule(
                        id="health_check_failure",
                        name="健康检查连续失败",
                        metric_name="health_check_failures",
                        condition="gt",
                        threshold=3,
                        severity="critical",
                        message=f"健康检查连续失败 {self.consecutive_failures} 次: {str(e)}",
                        labels={"component": "LogHealthChecker", "error": str(e)}
                    )
                    self.alert_engine.add_rule(rule)
            
            time.sleep(self.check_interval)
    
    def perform_health_check(self) -> LogSystemHealth:
        """
        执行完整的健康检查
        
        Returns:
            日志系统健康状态
        """
        start_time = time.time()
        
        # 1. 检查各层健康状态
        layer_health = {}
        for layer in self.LOG_LAYERS:
            layer_health[layer] = self._check_layer_health(layer)
        
        # 2. 验证配置
        config_valid = self._validate_configuration()
        
        # 3. 收集性能指标
        performance_metrics = self._collect_performance_metrics()
        
        # 4. 确定整体状态
        overall_status = self._determine_overall_status(layer_health, config_valid, performance_metrics)
        
        # 5. 生成建议
        recommendations = self._generate_recommendations(layer_health, performance_metrics)
        
        # 记录检查耗时
        check_duration = time.time() - start_time
        performance_metrics['health_check_duration_ms'] = check_duration * 1000
        
        return LogSystemHealth(
            overall_status=overall_status,
            timestamp=time.time(),
            layer_health=layer_health,
            configuration_valid=config_valid,
            performance_metrics=performance_metrics,
            auto_repairs_performed=[],
            recommendations=recommendations
        )
    
    def _check_layer_health(self, layer: str) -> LogLayerHealth:
        """检查单个日志层的健康状态"""
        layer_path = self.log_base_dir / layer
        health = LogLayerHealth(layer_name=layer, is_healthy=True)
        
        # 检查目录是否存在
        if not layer_path.exists():
            health.is_healthy = False
            health.issues.append(f"目录不存在: {layer_path}")
            return health
        
        # 统计文件信息
        try:
            file_stats = self._analyze_layer_files(layer_path)
            health.file_count = file_stats['count']
            health.total_size_mb = file_stats['total_size_mb']
            health.last_write_time = file_stats['last_modified']
            
            # 检查文件年龄
            if health.last_write_time:
                age_hours = (time.time() - health.last_write_time) / 3600
                if age_hours > self.THRESHOLDS['max_file_age_hours']:
                    health.issues.append(f"文件过旧: {age_hours:.1f}小时未更新")
                    health.is_healthy = False
            
            # 检查大小限制
            if health.total_size_mb > self.THRESHOLDS['max_layer_size_gb'] * 1024:
                health.issues.append(f"层大小过大: {health.total_size_mb:.1f}MB")
                health.is_healthy = False
            
            # 计算写入速度（如果有历史数据）
            if layer in self.write_speeds and len(self.write_speeds[layer]) > 0:
                health.write_speed_mb_per_sec = sum(self.write_speeds[layer]) / len(self.write_speeds[layer])
                
                if health.write_speed_mb_per_sec < self.THRESHOLDS['min_write_speed_mb']:
                    health.issues.append(f"写入速度过慢: {health.write_speed_mb_per_sec:.3f}MB/s")
                    health.is_healthy = False
            
        except Exception as e:
            health.is_healthy = False
            health.issues.append(f"文件分析失败: {e}")
        
        return health
    
    def _analyze_layer_files(self, layer_path: Path) -> Dict[str, Any]:
        """分析层目录中的文件"""
        stats = {
            'count': 0,
            'total_size_mb': 0.0,
            'last_modified': None,
            'file_types': defaultdict(int)
        }
        
        for file_path in layer_path.rglob('*'):
            if file_path.is_file():
                stats['count'] += 1
                
                try:
                    file_stat = file_path.stat()
                    stats['total_size_mb'] += file_stat.st_size / (1024 * 1024)
                    
                    if stats['last_modified'] is None or file_stat.st_mtime > stats['last_modified']:
                        stats['last_modified'] = file_stat.st_mtime
                    
                    # 统计文件类型
                    suffix = file_path.suffix.lower()
                    stats['file_types'][suffix] += 1
                    
                except Exception as e:
                    self.logger.debug(f"无法访问文件 {file_path}: {e}")
        
        return stats
    
    def _validate_configuration(self) -> bool:
        """验证配置文件"""
        if not self.config_file:
            # 使用默认配置
            return True
        
        config_path = Path(self.config_file)
        if not config_path.exists():
            # 创建告警规则
            rule = AlertRule(
                id="config_file_missing",
                name="配置文件不存在",
                metric_name="config_validation_status",
                condition="eq",
                threshold=0,
                severity="warning",
                message=f"配置文件不存在: {config_path}",
                labels={"component": "LogHealthChecker", "config_path": str(config_path)}
            )
            self.alert_engine.add_rule(rule)
            return False
        
        try:
            # 检查是否需要重新加载配置
            config_mtime = config_path.stat().st_mtime
            if config_mtime > self._config_load_time:
                # 加载并验证配置
                with open(config_path, 'r', encoding='utf-8') as f:
                    if config_path.suffix in ['.yaml', '.yml']:
                        config = yaml.safe_load(f)
                    else:
                        config = json.load(f)
                
                # 验证必需字段
                required_fields = ['base_dir', 'buffer_size', 'flush_interval']
                for field in required_fields:
                    if field not in config:
                        # 创建告警规则
                        rule = AlertRule(
                            id=f"config_field_missing_{field}",
                            name=f"配置缺少必需字段: {field}",
                            metric_name="config_field_validation",
                            condition="eq",
                            threshold=0,
                            severity="warning",
                            message=f"配置缺少必需字段: {field}",
                            labels={"component": "LogHealthChecker", "missing_field": field}
                        )
                        self.alert_engine.add_rule(rule)
                        return False
                
                # 验证数值范围
                if config.get('buffer_size', 0) < 100:
                    # 创建告警规则
                    rule = AlertRule(
                        id="buffer_size_too_small",
                        name="缓冲区大小过小",
                        metric_name="config_buffer_size",
                        condition="lt",
                        threshold=100,
                        severity="warning",
                        message=f"缓冲区大小过小: {config.get('buffer_size')}",
                        labels={"component": "LogHealthChecker", "buffer_size": str(config.get('buffer_size'))}
                    )
                    self.alert_engine.add_rule(rule)
                    return False
                
                # 缓存配置
                self._config_cache = config
                self._config_load_time = config_mtime
            
            return True
            
        except Exception as e:
            # 创建告警规则
            rule = AlertRule(
                id="config_validation_error",
                name="配置验证失败",
                metric_name="config_validation_error",
                condition="eq",
                threshold=1,
                severity="critical",
                message=f"配置验证失败: {e}",
                labels={"component": "LogHealthChecker", "error": str(e)}
            )
            self.alert_engine.add_rule(rule)
            return False
    
    def _collect_performance_metrics(self) -> Dict[str, float]:
        """收集性能指标"""
        metrics = {}
        
        try:
            # 磁盘使用情况
            disk_usage = psutil.disk_usage(str(self.log_base_dir))
            metrics['disk_usage_percent'] = disk_usage.percent
            metrics['disk_free_gb'] = disk_usage.free / (1024**3)
            
            # IO延迟（简单测试）
            io_start = time.time()
            test_file = self.log_base_dir / '.health_check_test'
            test_file.write_text("test")
            test_file.unlink()
            io_latency_ms = (time.time() - io_start) * 1000
            metrics['io_latency_ms'] = io_latency_ms
            self.io_latencies.append(io_latency_ms)
            
            # 平均IO延迟
            if self.io_latencies:
                metrics['avg_io_latency_ms'] = sum(self.io_latencies) / len(self.io_latencies)
            
            # 内存使用
            memory = psutil.virtual_memory()
            metrics['memory_usage_percent'] = memory.percent
            metrics['memory_available_gb'] = memory.available / (1024**3)
            
            # 获取日志器统计
            if hasattr(self.logger, 'get_stats'):
                logger_stats = self.logger.get_stats()
                metrics.update({
                    f'logger_{k}': v for k, v in logger_stats.items()
                    if isinstance(v, (int, float))
                })
            
        except Exception as e:
            self.logger.error(f"性能指标收集失败: {e}")
        
        return metrics
    
    def _determine_overall_status(self,
                                 layer_health: Dict[str, LogLayerHealth],
                                 config_valid: bool,
                                 performance_metrics: Dict[str, float]) -> str:
        """确定整体健康状态"""
        # 统计不健康的层
        unhealthy_layers = [lh for lh in layer_health.values() if not lh.is_healthy]
        critical_issues = []
        warning_issues = []
        
        # 检查层健康
        if len(unhealthy_layers) > len(self.LOG_LAYERS) / 2:
            critical_issues.append("超过一半的日志层不健康")
        elif unhealthy_layers:
            warning_issues.append(f"{len(unhealthy_layers)}个日志层存在问题")
        
        # 检查配置
        if not config_valid:
            warning_issues.append("配置验证失败")
        
        # 检查性能
        if performance_metrics.get('disk_free_gb', float('inf')) < self.THRESHOLDS['min_free_space_gb']:
            critical_issues.append("磁盘空间不足")
        
        if performance_metrics.get('avg_io_latency_ms', 0) > self.THRESHOLDS['max_io_latency_ms']:
            warning_issues.append("IO延迟过高")
        
        if performance_metrics.get('memory_usage_percent', 0) > 90:
            warning_issues.append("内存使用过高")
        
        # 确定状态
        if critical_issues:
            self.consecutive_failures = 0  # 重置连续失败计数
            return 'critical'
        elif warning_issues:
            return 'warning'
        elif any(not lh.is_healthy for lh in layer_health.values()):
            return 'warning'
        else:
            self.consecutive_failures = 0
            return 'healthy'
    
    def _generate_recommendations(self,
                                layer_health: Dict[str, LogLayerHealth],
                                performance_metrics: Dict[str, float]) -> List[str]:
        """生成优化建议"""
        recommendations = []
        
        # 基于层健康状态
        for layer_name, health in layer_health.items():
            if health.total_size_mb > 1000:  # 超过1GB
                recommendations.append(f"建议归档{layer_name}层的历史日志文件")
            
            if health.write_speed_mb_per_sec < 0.5 and health.write_speed_mb_per_sec > 0:
                recommendations.append(f"优化{layer_name}层的写入性能，当前速度过慢")
        
        # 基于性能指标
        if performance_metrics.get('disk_free_gb', float('inf')) < 10:
            recommendations.append("磁盘空间不足10GB，建议清理或扩容")
        
        if performance_metrics.get('avg_io_latency_ms', 0) > 50:
            recommendations.append("IO延迟较高，建议使用SSD或优化写入模式")
        
        # 基于缓冲区使用
        buffer_usage = performance_metrics.get('logger_buffer_usage', 0)
        if buffer_usage > self.THRESHOLDS['max_buffer_usage']:
            recommendations.append("日志缓冲区使用率过高，建议增加缓冲区大小或提高刷新频率")
        
        # 基于历史趋势
        if len(self.health_history) > 10:
            recent_health = list(self.health_history)[-10:]
            unhealthy_count = sum(1 for h in recent_health if h.overall_status != 'healthy')
            if unhealthy_count > 5:
                recommendations.append("系统频繁出现健康问题，建议进行深度诊断")
        
        return recommendations
    
    def _process_health_alerts(self, health_status: LogSystemHealth):
        """处理健康告警"""
        # 生成新告警
        if health_status.overall_status == 'critical':
            # 创建告警规则
            rule = AlertRule(
                id="log_system_critical",
                name="日志系统健康状态危急",
                metric_name="log_system_health_status",
                condition="eq",
                threshold=2,  # 2 = critical
                severity="critical",
                message="日志系统健康状态危急",
                labels={"component": "LogSystem", "status": health_status.overall_status}
            )
            self.alert_engine.add_rule(rule)
        elif health_status.overall_status == 'warning':
            # 检查具体问题
            for layer_name, health in health_status.layer_health.items():
                if not health.is_healthy:
                    # 创建告警规则
                    rule = AlertRule(
                        id=f"log_layer_{layer_name}_unhealthy",
                        name=f"{layer_name}层存在问题",
                        metric_name=f"log_layer_{layer_name}_health",
                        condition="eq",
                        threshold=0,  # 0 = unhealthy
                        severity="warning",
                        message=f"{layer_name}层存在问题: {', '.join(health.issues)}",
                        labels={"component": f"LogLayer_{layer_name}", "issues": ', '.join(health.issues)}
                    )
                    self.alert_engine.add_rule(rule)
    
    def _perform_auto_repair(self, health_status: LogSystemHealth):
        """执行自动修复"""
        repairs_performed = []
        
        try:
            # 1. 修复缺失的目录
            for layer in self.LOG_LAYERS:
                layer_path = self.log_base_dir / layer
                if not layer_path.exists():
                    layer_path.mkdir(parents=True, exist_ok=True)
                    repairs_performed.append(f"创建缺失目录: {layer}")
                    self.logger.info(f"自动创建日志目录: {layer_path}")
            
            # 2. 清理过大的文件
            for layer_name, health in health_status.layer_health.items():
                if health.total_size_mb > self.THRESHOLDS['max_layer_size_gb'] * 1024:
                    self._archive_old_logs(layer_name)
                    repairs_performed.append(f"归档{layer_name}层旧日志")
            
            # 3. 优化性能问题
            if health_status.performance_metrics.get('avg_io_latency_ms', 0) > self.THRESHOLDS['max_io_latency_ms']:
                # 调整缓冲区配置
                if hasattr(self.logger, 'batch_size'):
                    old_batch_size = self.logger.batch_size
                    self.logger.batch_size = min(old_batch_size * 2, 1000)
                    repairs_performed.append(f"增加批量写入大小: {old_batch_size} -> {self.logger.batch_size}")
            
            # 4. 处理磁盘空间
            if health_status.performance_metrics.get('disk_free_gb', float('inf')) < self.THRESHOLDS['min_free_space_gb']:
                self._emergency_cleanup()
                repairs_performed.append("执行紧急磁盘清理")
            
            # 记录修复历史
            if repairs_performed:
                repair_record = {
                    'timestamp': datetime.now().isoformat(),
                    'health_status': health_status.overall_status,
                    'repairs': repairs_performed,
                    'success': True
                }
                self.repair_history.append(repair_record)
                health_status.auto_repairs_performed = repairs_performed
                
                self.logger.info(f"自动修复完成: {', '.join(repairs_performed)}")
            
        except Exception as e:
            self.logger.error(f"自动修复失败: {e}")
            repair_record = {
                'timestamp': datetime.now().isoformat(),
                'health_status': health_status.overall_status,
                'repairs': repairs_performed,
                'success': False,
                'error': str(e)
            }
            self.repair_history.append(repair_record)
    
    def _archive_old_logs(self, layer_name: str, days_to_keep: int = 7):
        """归档旧日志文件"""
        layer_path = self.log_base_dir / layer_name
        archive_path = self.log_base_dir / "archive" / layer_name
        archive_path.mkdir(parents=True, exist_ok=True)
        
        cutoff_time = time.time() - (days_to_keep * 24 * 3600)
        archived_count = 0
        archived_size_mb = 0
        
        for file_path in layer_path.glob('*'):
            if file_path.is_file():
                try:
                    if file_path.stat().st_mtime < cutoff_time:
                        # 压缩并归档
                        archive_file = archive_path / f"{file_path.name}.gz"
                        with open(file_path, 'rb') as f_in:
                            with gzip.open(archive_file, 'wb') as f_out:
                                f_out.writelines(f_in)
                        
                        archived_size_mb += file_path.stat().st_size / (1024 * 1024)
                        file_path.unlink()
                        archived_count += 1
                        
                except Exception as e:
                    self.logger.error(f"归档文件失败 {file_path}: {e}")
        
        if archived_count > 0:
            self.logger.info(f"归档{layer_name}层: {archived_count}个文件, {archived_size_mb:.1f}MB")
    
    def _emergency_cleanup(self):
        """紧急磁盘清理"""
        self.logger.warning("执行紧急磁盘清理...")
        
        # 清理所有层的旧文件
        for layer in self.LOG_LAYERS:
            self._archive_old_logs(layer, days_to_keep=1)
        
        # 清理临时文件
        temp_patterns = ['*.tmp', '*.temp', '.*.swp']
        for pattern in temp_patterns:
            for temp_file in self.log_base_dir.rglob(pattern):
                try:
                    temp_file.unlink()
                except Exception:
                    pass
    
    def _generate_final_report(self):
        """生成最终健康报告"""
        report = {
            'generated_at': datetime.now().isoformat(),
            'check_duration_hours': (time.time() - (self.health_history[0].timestamp if self.health_history else time.time())) / 3600,
            'total_checks': len(self.health_history),
            'health_summary': self._calculate_health_summary(),
            'repair_summary': self._calculate_repair_summary(),
            'performance_summary': self._calculate_performance_summary(),
            'recommendations': self._generate_final_recommendations()
        }
        
        # 保存报告
        report_path = self.log_base_dir / f"health_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        try:
            with open(report_path, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"健康报告已生成: {report_path}")
        except Exception as e:
            self.logger.error(f"生成健康报告失败: {e}")
    
    def _calculate_health_summary(self) -> Dict[str, Any]:
        """计算健康摘要"""
        if not self.health_history:
            return {}
        
        status_counts = defaultdict(int)
        layer_issues = defaultdict(int)
        
        for health in self.health_history:
            status_counts[health.overall_status] += 1
            
            for layer_name, layer_health in health.layer_health.items():
                if not layer_health.is_healthy:
                    layer_issues[layer_name] += 1
        
        total = len(self.health_history)
        return {
            'status_distribution': {
                status: (count / total * 100) for status, count in status_counts.items()
            },
            'layer_issue_frequency': {
                layer: (count / total * 100) for layer, count in layer_issues.items()
            },
            'overall_health_score': (status_counts.get('healthy', 0) / total * 100) if total > 0 else 0
        }
    
    def _calculate_repair_summary(self) -> Dict[str, Any]:
        """计算修复摘要"""
        if not self.repair_history:
            return {'total_repairs': 0, 'success_rate': 100.0}
        
        successful_repairs = sum(1 for r in self.repair_history if r['success'])
        repair_types = defaultdict(int)
        
        for record in self.repair_history:
            for repair in record.get('repairs', []):
                repair_type = repair.split(':')[0]
                repair_types[repair_type] += 1
        
        return {
            'total_repairs': len(self.repair_history),
            'successful_repairs': successful_repairs,
            'success_rate': (successful_repairs / len(self.repair_history) * 100),
            'repair_type_counts': dict(repair_types)
        }
    
    def _calculate_performance_summary(self) -> Dict[str, Any]:
        """计算性能摘要"""
        if not self.health_history:
            return {}
        
        # 收集所有性能指标
        io_latencies = []
        disk_usage = []
        memory_usage = []
        
        for health in self.health_history:
            metrics = health.performance_metrics
            if 'avg_io_latency_ms' in metrics:
                io_latencies.append(metrics['avg_io_latency_ms'])
            if 'disk_usage_percent' in metrics:
                disk_usage.append(metrics['disk_usage_percent'])
            if 'memory_usage_percent' in metrics:
                memory_usage.append(metrics['memory_usage_percent'])
        
        return {
            'avg_io_latency_ms': sum(io_latencies) / len(io_latencies) if io_latencies else 0,
            'max_io_latency_ms': max(io_latencies) if io_latencies else 0,
            'avg_disk_usage_percent': sum(disk_usage) / len(disk_usage) if disk_usage else 0,
            'max_disk_usage_percent': max(disk_usage) if disk_usage else 0,
            'avg_memory_usage_percent': sum(memory_usage) / len(memory_usage) if memory_usage else 0,
            'max_memory_usage_percent': max(memory_usage) if memory_usage else 0
        }
    
    def _generate_final_recommendations(self) -> List[str]:
        """生成最终建议"""
        recommendations = []
        
        health_summary = self._calculate_health_summary()
        repair_summary = self._calculate_repair_summary()
        performance_summary = self._calculate_performance_summary()
        
        # 基于健康评分
        health_score = health_summary.get('overall_health_score', 100)
        if health_score < 90:
            recommendations.append(f"系统健康评分较低({health_score:.1f}%)，建议优化日志配置")
        
        # 基于修复频率
        if repair_summary['total_repairs'] > 10:
            recommendations.append("自动修复频繁触发，建议检查根本原因")
        
        # 基于性能
        if performance_summary.get('avg_io_latency_ms', 0) > 50:
            recommendations.append("平均IO延迟较高，建议优化存储性能或调整写入策略")
        
        # 基于层问题
        layer_issues = health_summary.get('layer_issue_frequency', {})
        for layer, frequency in layer_issues.items():
            if frequency > 20:
                recommendations.append(f"{layer}层频繁出现问题({frequency:.1f}%)，需要重点关注")
        
        return recommendations
    
    def get_current_health(self) -> Optional[LogSystemHealth]:
        """获取当前健康状态"""
        if self.health_history:
            return self.health_history[-1]
        return None
    
    def force_health_check(self) -> LogSystemHealth:
        """强制执行健康检查"""
        return self.perform_health_check()
    
    def get_health_report(self) -> Dict[str, Any]:
        """获取健康报告"""
        current_health = self.get_current_health()
        if not current_health:
            return {'status': 'unknown', 'message': '尚未执行健康检查'}
        
        return {
            'current_status': current_health.overall_status,
            'last_check_time': datetime.fromtimestamp(current_health.timestamp).isoformat(),
            'layer_summary': {
                layer: health.is_healthy 
                for layer, health in current_health.layer_health.items()
            },
            'critical_issues': [
                issue for layer_health in current_health.layer_health.values()
                for issue in layer_health.issues
            ],
            'recommendations': current_health.recommendations,
            'auto_repairs': current_health.auto_repairs_performed
        }


def create_log_health_checker(log_dir: str = "logs", 
                            config_file: Optional[str] = None) -> LogHealthChecker:
    """创建日志健康检查器实例"""
    return LogHealthChecker(
        log_base_dir=log_dir,
        config_file=config_file,
        check_interval=60.0,
        enable_auto_repair=True
    )


if __name__ == "__main__":
    """测试健康检查器"""
    import argparse
    
    parser = argparse.ArgumentParser(description="日志系统健康检查")
    parser.add_argument('--log-dir', default='logs', help='日志目录')
    parser.add_argument('--config', help='配置文件路径')
    parser.add_argument('--check-once', action='store_true', help='执行一次检查')
    parser.add_argument('--monitor', action='store_true', help='持续监控模式')
    
    args = parser.parse_args()
    
    # 创建健康检查器
    checker = create_log_health_checker(args.log_dir, args.config)
    
    if args.check_once:
        # 执行一次检查
        print("执行健康检查...")
        health = checker.force_health_check()
        
        print(f"\n整体状态: {health.overall_status}")
        print("\n各层状态:")
        for layer, layer_health in health.layer_health.items():
            status = "✓" if layer_health.is_healthy else "✗"
            print(f"  {status} {layer}: {layer_health.file_count}个文件, {layer_health.total_size_mb:.1f}MB")
            if layer_health.issues:
                for issue in layer_health.issues:
                    print(f"    - {issue}")
        
        print("\n性能指标:")
        for metric, value in health.performance_metrics.items():
            print(f"  {metric}: {value:.2f}")
        
        if health.recommendations:
            print("\n建议:")
            for rec in health.recommendations:
                print(f"  - {rec}")
    
    elif args.monitor:
        # 持续监控模式
        print("启动日志健康监控...")
        checker.start()
        
        try:
            while True:
                time.sleep(30)
                report = checker.get_health_report()
                print(f"\n[{datetime.now().strftime('%H:%M:%S')}] 状态: {report['current_status']}")
                
                if report.get('critical_issues'):
                    print("关键问题:")
                    for issue in report['critical_issues']:
                        print(f"  - {issue}")
                
                if report.get('auto_repairs'):
                    print("自动修复:")
                    for repair in report['auto_repairs']:
                        print(f"  - {repair}")
                        
        except KeyboardInterrupt:
            print("\n停止监控...")
        finally:
            checker.stop()
    
    else:
        parser.print_help()