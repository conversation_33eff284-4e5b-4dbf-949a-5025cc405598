"""
异步检查点保存器

解决torch.save在训练过程中挂起的问题。
使用子进程异步保存检查点，避免与主训练进程的资源竞争。
"""

import torch
import multiprocessing as mp
import queue
import time
import pickle
from pathlib import Path
from typing import Dict, Any, Optional
from cardgame_ai.utils.logging import get_logger

logger = get_logger(__name__)

class AsyncCheckpointSaver:
    """异步检查点保存器"""
    
    def __init__(self):
        """初始化异步保存器"""
        self.save_queue = mp.Queue()
        self.result_queue = mp.Queue()
        self.worker_process = None
        self._start_worker()
    
    def _start_worker(self):
        """启动工作进程"""
        self.worker_process = mp.Process(target=self._worker_loop, daemon=True)
        self.worker_process.start()
        logger.info("异步检查点保存器已启动")
    
    def _worker_loop(self):
        """工作进程循环"""
        while True:
            try:
                # 获取保存任务
                task = self.save_queue.get(timeout=1)
                if task is None:  # 退出信号
                    break
                
                checkpoint_path, checkpoint_data = task
                
                # 执行保存
                start_time = time.time()
                try:
                    # 创建临时文件
                    temp_path = Path(str(checkpoint_path) + '.tmp')
                    
                    # 使用torch.save保存到临时文件（保持PyTorch格式兼容性）
                    torch.save(checkpoint_data, temp_path)
                    
                    # 原子性重命名
                    temp_path.rename(checkpoint_path)
                    
                    elapsed = time.time() - start_time
                    file_size = checkpoint_path.stat().st_size
                    
                    self.result_queue.put({
                        'success': True,
                        'path': str(checkpoint_path),
                        'size': file_size,
                        'time': elapsed
                    })
                    
                except Exception as e:
                    elapsed = time.time() - start_time
                    self.result_queue.put({
                        'success': False,
                        'path': str(checkpoint_path),
                        'error': str(e),
                        'time': elapsed
                    })
                    
            except queue.Empty:
                continue
            except Exception as e:
                logger.error(f"工作进程异常: {e}")
    
    def save_checkpoint_async(self, checkpoint_path: Path, checkpoint_data: Dict[str, Any]):
        """
        异步保存检查点
        
        Args:
            checkpoint_path: 保存路径
            checkpoint_data: 检查点数据
        """
        # 确保数据可序列化
        try:
            # 先将所有CUDA张量移到CPU
            cpu_data = self._move_to_cpu(checkpoint_data)
            
            # 放入队列
            self.save_queue.put((checkpoint_path, cpu_data))
            logger.info(f"检查点保存任务已提交: {checkpoint_path}")
            
        except Exception as e:
            logger.error(f"提交保存任务失败: {e}")
    
    def _move_to_cpu(self, data: Any) -> Any:
        """递归地将所有CUDA张量移到CPU"""
        if isinstance(data, torch.Tensor):
            return data.cpu() if data.is_cuda else data
        elif isinstance(data, dict):
            return {k: self._move_to_cpu(v) for k, v in data.items()}
        elif isinstance(data, list):
            return [self._move_to_cpu(item) for item in data]
        elif isinstance(data, tuple):
            return tuple(self._move_to_cpu(item) for item in data)
        else:
            return data
    
    def check_results(self) -> Optional[Dict[str, Any]]:
        """
        检查保存结果
        
        Returns:
            保存结果，如果没有结果则返回None
        """
        try:
            result = self.result_queue.get_nowait()
            if result['success']:
                logger.info(f"检查点保存成功: {result['path']}, "
                          f"大小: {result['size']/1024/1024:.2f}MB, "
                          f"耗时: {result['time']:.2f}秒")
            else:
                logger.error(f"检查点保存失败: {result['path']}, "
                           f"错误: {result['error']}, "
                           f"耗时: {result['time']:.2f}秒")
            return result
        except queue.Empty:
            return None
    
    def close(self):
        """关闭保存器"""
        if self.worker_process and self.worker_process.is_alive():
            self.save_queue.put(None)  # 发送退出信号
            self.worker_process.join(timeout=5)
            if self.worker_process.is_alive():
                self.worker_process.terminate()
            logger.info("异步检查点保存器已关闭")

# 全局实例
_async_saver = None

def get_async_saver() -> AsyncCheckpointSaver:
    """获取全局异步保存器实例"""
    global _async_saver
    if _async_saver is None:
        _async_saver = AsyncCheckpointSaver()
    return _async_saver