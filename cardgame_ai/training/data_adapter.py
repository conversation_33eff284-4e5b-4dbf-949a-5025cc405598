"""
训练数据适配器

为缺失的policy和future_values字段生成默认值，
临时解决EfficientZero V2训练中的梯度问题。

作者：AI系统
创建时间：2025-01-05
"""

import torch
import numpy as np
from typing import Any, Optional, List
from cardgame_ai.utils.logging import get_logger

logger = get_logger(__name__)


class BatchDataAdapter:
    """批次数据适配器
    
    在训练时动态为缺失的字段生成合理的默认值。
    这是一个临时解决方案，用于快速恢复训练功能。
    """
    
    def __init__(self, action_space_size: int = 2300, n_steps: int = 5, gamma: float = 0.997):
        """
        初始化数据适配器
        
        Args:
            action_space_size: 动作空间大小，默认2300（斗地主）
            n_steps: 计算future values的步数
            gamma: 折扣因子
        """
        self.action_space_size = action_space_size
        self.n_steps = n_steps
        self.gamma = gamma
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        logger.info(f"BatchDataAdapter初始化: action_space={action_space_size}, n_steps={n_steps}, gamma={gamma}")
    
    def adapt_batch(self, batch: Any) -> Any:
        """
        适配批次数据，为缺失的字段生成默认值
        
        Args:
            batch: 原始批次数据
            
        Returns:
            适配后的批次数据
        """
        # 检查batch是否有必要的基础字段
        if not hasattr(batch, 'observations'):
            logger.error("批次数据缺少observations字段")
            return batch
        
        batch_size = None
        
        # 获取批次大小
        if torch.is_tensor(batch.observations):
            batch_size = batch.observations.shape[0]
        elif isinstance(batch.observations, (list, np.ndarray)):
            batch_size = len(batch.observations)
        else:
            logger.warning("无法确定批次大小")
            return batch
        
        # 检查并生成policy字段
        if not hasattr(batch, 'policy') or batch.policy is None:
            # 生成均匀分布作为默认策略
            uniform_policy = torch.ones(batch_size, self.action_space_size, device=self.device) / self.action_space_size
            batch.policy = uniform_policy
            logger.debug(f"为批次生成均匀分布策略: shape={uniform_policy.shape}")
        else:
            # 确保policy是tensor类型
            if not torch.is_tensor(batch.policy):
                batch.policy = torch.tensor(batch.policy, dtype=torch.float32, device=self.device)
        
        # 检查并生成future_values字段
        if not hasattr(batch, 'future_values') or batch.future_values is None:
            if hasattr(batch, 'rewards') and batch.rewards is not None:
                # 从rewards计算n步bootstrap值
                future_values = self._compute_future_values(batch.rewards)
                batch.future_values = future_values
                logger.debug(f"计算future_values: shape={future_values.shape}")
            else:
                # 如果没有rewards，生成零值
                future_values = torch.zeros(batch_size, self.n_steps, device=self.device)
                batch.future_values = future_values
                logger.debug("使用零值作为future_values")
        else:
            # 确保future_values是tensor类型
            if not torch.is_tensor(batch.future_values):
                batch.future_values = torch.tensor(batch.future_values, dtype=torch.float32, device=self.device)
        
        # 记录适配统计
        if hasattr(self, '_adapt_count'):
            self._adapt_count += 1
        else:
            self._adapt_count = 1
        
        if self._adapt_count % 100 == 0:
            logger.info(f"已适配{self._adapt_count}个批次数据")
        
        return batch
    
    def _compute_future_values(self, rewards: torch.Tensor) -> torch.Tensor:
        """
        从rewards计算n步future values
        
        Args:
            rewards: 奖励序列，shape: (batch_size, seq_len) 或 (batch_size,)
            
        Returns:
            future_values: n步未来价值，shape: (batch_size, n_steps)
        """
        if not torch.is_tensor(rewards):
            rewards = torch.tensor(rewards, dtype=torch.float32, device=self.device)
        else:
            rewards = rewards.to(self.device)
        
        # 处理不同维度的rewards
        if rewards.dim() == 1:
            # 单步奖励，扩展为序列
            batch_size = rewards.shape[0]
            future_values = rewards.unsqueeze(1).repeat(1, self.n_steps)
            # 应用折扣因子
            for i in range(1, self.n_steps):
                future_values[:, i] = future_values[:, i] * (self.gamma ** i)
        elif rewards.dim() == 2:
            # 多步奖励序列
            batch_size, seq_len = rewards.shape
            future_values = torch.zeros(batch_size, self.n_steps, device=self.device)
            
            # 计算每个位置的n步累积奖励
            for i in range(self.n_steps):
                if i < seq_len:
                    # 计算从位置i开始的折扣累积奖励
                    for j in range(min(self.n_steps, seq_len - i)):
                        future_values[:, i] += rewards[:, i + j] * (self.gamma ** j)
                else:
                    # 超出序列长度，使用最后的奖励值
                    future_values[:, i] = rewards[:, -1] * (self.gamma ** (i - seq_len + 1))
        else:
            logger.warning(f"不支持的rewards维度: {rewards.shape}")
            batch_size = rewards.shape[0]
            future_values = torch.zeros(batch_size, self.n_steps, device=self.device)
        
        return future_values
    
    def validate_batch(self, batch: Any) -> dict:
        """
        验证批次数据的完整性
        
        Args:
            batch: 批次数据
            
        Returns:
            验证结果字典
        """
        results = {
            'has_observations': hasattr(batch, 'observations'),
            'has_policy': hasattr(batch, 'policy') and batch.policy is not None,
            'has_future_values': hasattr(batch, 'future_values') and batch.future_values is not None,
            'has_rewards': hasattr(batch, 'rewards') and batch.rewards is not None,
            'has_value': hasattr(batch, 'value') and batch.value is not None,
            'has_actions': hasattr(batch, 'actions') and batch.actions is not None,
        }
        
        # 检查数据形状
        if results['has_policy'] and torch.is_tensor(batch.policy):
            results['policy_shape'] = batch.policy.shape
        
        if results['has_future_values'] and torch.is_tensor(batch.future_values):
            results['future_values_shape'] = batch.future_values.shape
        
        return results


# 便利函数
def create_adapter(config: dict = None) -> BatchDataAdapter:
    """
    创建数据适配器的便利函数
    
    Args:
        config: 配置字典，可选
        
    Returns:
        BatchDataAdapter实例
    """
    if config is None:
        config = {}
    
    action_space_size = config.get('action_space_size', 2300)
    n_steps = config.get('n_steps', 5)
    gamma = config.get('gamma', 0.997)
    
    return BatchDataAdapter(
        action_space_size=action_space_size,
        n_steps=n_steps,
        gamma=gamma
    )