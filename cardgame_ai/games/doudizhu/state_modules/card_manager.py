"""
斗地主状态管理模块基础导入
"""

import numpy as np
from typing import Dict, List, Tuple, Optional, Any, Set
from enum import Enum
from dataclasses import dataclass
from cardgame_ai.utils.logging import get_logger

logger = get_logger(__name__)

from .state_types import CardInfo

class CardManager:
    """牌管理器"""
    
    def __init__(self):
        """初始化牌管理器"""
        self.deck = []
        self.landlord_cards = []
        self.farmer1_cards = []
        self.farmer2_cards = []
        self.played_cards = []
        self._initialize_deck()
        
    def _initialize_deck(self):
        """初始化牌组"""
        # 标准54张牌
        suits = ['spades', 'hearts', 'diamonds', 'clubs']
        ranks = list(range(3, 16))  # 3-K, A, 2
        
        for suit in suits:
            for rank in ranks:
                card = CardInfo(suit=suit, rank=rank, value=rank)
                self.deck.append(card)
        
        # 添加大小王
        self.deck.append(CardInfo(suit='joker', rank=16, value=16))  # 小王
        self.deck.append(CardInfo(suit='joker', rank=17, value=17))  # 大王
        
        logger.debug(f"初始化牌组: {len(self.deck)}张牌")
    
    def shuffle_and_deal(self):
        """洗牌并发牌"""
        import random
        random.shuffle(self.deck)
        
        # 发牌：每人17张，底牌3张
        self.farmer1_cards = self.deck[:17]
        self.farmer2_cards = self.deck[17:34]
        self.landlord_cards = self.deck[34:51]
        self.landlord_bottom_cards = self.deck[51:54]
        
        logger.info("完成洗牌发牌")
    
    def add_bottom_cards_to_landlord(self):
        """将底牌给地主"""
        self.landlord_cards.extend(self.landlord_bottom_cards)
        self.landlord_bottom_cards = []
        logger.debug("底牌已加入地主手牌")
    
    def play_cards(self, player: str, cards: List[CardInfo]):
        """出牌"""
        if player == "landlord":
            hand = self.landlord_cards
        elif player == "farmer1":
            hand = self.farmer1_cards
        else:
            hand = self.farmer2_cards
        
        # 移除出的牌
        for card in cards:
            if card in hand:
                hand.remove(card)
        
        self.played_cards.extend(cards)
        logger.debug(f"{player}出牌: {len(cards)}张")
    
    def get_hand_cards(self, player: str) -> List[CardInfo]:
        """获取手牌"""
        if player == "landlord":
            return self.landlord_cards.copy()
        elif player == "farmer1":
            return self.farmer1_cards.copy()
        else:
            return self.farmer2_cards.copy()
