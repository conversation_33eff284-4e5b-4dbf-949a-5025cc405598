"""
指标定义模块
定义所有监控指标的结构和类型
"""

from dataclasses import dataclass
from typing import Dict, Any, Optional, List, Union
from enum import Enum
from datetime import datetime

class MetricCategory(Enum):
    """指标分类"""
    SYSTEM = "system"
    TRAINING = "training"
    GAME = "game"
    PERFORMANCE = "performance"
    BUSINESS = "business"

class MetricUnit(Enum):
    """指标单位"""
    NONE = ""
    PERCENTAGE = "%"
    MILLISECONDS = "ms"
    SECONDS = "s"
    BYTES = "bytes"
    MEGABYTES = "MB"
    GIGABYTES = "GB"
    COUNT = "count"
    RATE = "rate"
    FPS = "fps"

@dataclass
class MetricDefinition:
    """指标定义"""
    name: str
    display_name: str
    category: MetricCategory
    unit: MetricUnit = MetricUnit.NONE
    description: str = ""
    tags: List[str] = None
    
    def __post_init__(self):
        if self.tags is None:
            self.tags = []

@dataclass
class MetricValue:
    """指标值"""
    definition: MetricDefinition
    value: Union[int, float, str, bool]
    timestamp: datetime
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}

# 预定义指标
SYSTEM_METRICS = {
    'cpu_usage': MetricDefinition(
        name='cpu_usage',
        display_name='CPU使用率',
        category=MetricCategory.SYSTEM,
        unit=MetricUnit.PERCENTAGE,
        description='系统CPU使用百分比',
        tags=['system', 'resource']
    ),
    'memory_usage': MetricDefinition(
        name='memory_usage',
        display_name='内存使用量',
        category=MetricCategory.SYSTEM,
        unit=MetricUnit.GIGABYTES,
        description='系统内存使用量',
        tags=['system', 'resource']
    ),
    'gpu_usage': MetricDefinition(
        name='gpu_usage',
        display_name='GPU使用率',
        category=MetricCategory.SYSTEM,
        unit=MetricUnit.PERCENTAGE,
        description='GPU使用百分比',
        tags=['system', 'gpu']
    ),
    'gpu_memory': MetricDefinition(
        name='gpu_memory',
        display_name='GPU内存使用',
        category=MetricCategory.SYSTEM,
        unit=MetricUnit.GIGABYTES,
        description='GPU内存使用量',
        tags=['system', 'gpu']
    ),
    'disk_usage': MetricDefinition(
        name='disk_usage',
        display_name='磁盘使用率',
        category=MetricCategory.SYSTEM,
        unit=MetricUnit.PERCENTAGE,
        description='磁盘使用百分比',
        tags=['system', 'storage']
    )
}

TRAINING_METRICS = {
    'loss': MetricDefinition(
        name='loss',
        display_name='训练损失',
        category=MetricCategory.TRAINING,
        description='模型训练损失值',
        tags=['training', 'loss']
    ),
    'learning_rate': MetricDefinition(
        name='learning_rate',
        display_name='学习率',
        category=MetricCategory.TRAINING,
        description='当前学习率',
        tags=['training', 'optimizer']
    ),
    'epoch': MetricDefinition(
        name='epoch',
        display_name='训练轮次',
        category=MetricCategory.TRAINING,
        unit=MetricUnit.COUNT,
        description='当前训练轮次',
        tags=['training', 'progress']
    ),
    'batch_size': MetricDefinition(
        name='batch_size',
        display_name='批次大小',
        category=MetricCategory.TRAINING,
        unit=MetricUnit.COUNT,
        description='训练批次大小',
        tags=['training', 'config']
    ),
    'gradient_norm': MetricDefinition(
        name='gradient_norm',
        display_name='梯度范数',
        category=MetricCategory.TRAINING,
        description='梯度的L2范数',
        tags=['training', 'gradient']
    )
}

GAME_METRICS = {
    'win_rate': MetricDefinition(
        name='win_rate',
        display_name='胜率',
        category=MetricCategory.GAME,
        unit=MetricUnit.PERCENTAGE,
        description='游戏胜率',
        tags=['game', 'performance']
    ),
    'episode_length': MetricDefinition(
        name='episode_length',
        display_name='游戏局长',
        category=MetricCategory.GAME,
        unit=MetricUnit.COUNT,
        description='平均游戏步数',
        tags=['game', 'duration']
    ),
    'reward': MetricDefinition(
        name='reward',
        display_name='奖励',
        category=MetricCategory.GAME,
        description='游戏奖励值',
        tags=['game', 'reward']
    ),
    'action_count': MetricDefinition(
        name='action_count',
        display_name='动作数量',
        category=MetricCategory.GAME,
        unit=MetricUnit.COUNT,
        description='游戏中的动作总数',
        tags=['game', 'actions']
    )
}

PERFORMANCE_METRICS = {
    'fps': MetricDefinition(
        name='fps',
        display_name='帧率',
        category=MetricCategory.PERFORMANCE,
        unit=MetricUnit.FPS,
        description='每秒处理帧数',
        tags=['performance', 'speed']
    ),
    'latency': MetricDefinition(
        name='latency',
        display_name='延迟',
        category=MetricCategory.PERFORMANCE,
        unit=MetricUnit.MILLISECONDS,
        description='响应延迟时间',
        tags=['performance', 'latency']
    ),
    'throughput': MetricDefinition(
        name='throughput',
        display_name='吞吐量',
        category=MetricCategory.PERFORMANCE,
        unit=MetricUnit.RATE,
        description='处理吞吐量',
        tags=['performance', 'throughput']
    ),
    'inference_time': MetricDefinition(
        name='inference_time',
        display_name='推理时间',
        category=MetricCategory.PERFORMANCE,
        unit=MetricUnit.MILLISECONDS,
        description='模型推理时间',
        tags=['performance', 'inference']
    )
}

# 合并所有指标定义
ALL_METRICS = {
    **SYSTEM_METRICS,
    **TRAINING_METRICS,
    **GAME_METRICS,
    **PERFORMANCE_METRICS
}

def get_metric_definition(name: str) -> Optional[MetricDefinition]:
    """获取指标定义"""
    return ALL_METRICS.get(name)

def get_metrics_by_category(category: MetricCategory) -> List[MetricDefinition]:
    """按分类获取指标"""
    return [metric for metric in ALL_METRICS.values() if metric.category == category]

def get_metrics_by_tag(tag: str) -> List[MetricDefinition]:
    """按标签获取指标"""
    return [metric for metric in ALL_METRICS.values() if tag in metric.tags]

def register_custom_metric(definition: MetricDefinition):
    """注册自定义指标"""
    ALL_METRICS[definition.name] = definition

class MetricRegistry:
    """指标注册表"""
    
    def __init__(self):
        self.metrics = ALL_METRICS.copy()
        
    def register(self, definition: MetricDefinition):
        """注册指标"""
        self.metrics[definition.name] = definition
        
    def unregister(self, name: str):
        """注销指标"""
        self.metrics.pop(name, None)
        
    def get(self, name: str) -> Optional[MetricDefinition]:
        """获取指标定义"""
        return self.metrics.get(name)
        
    def list_all(self) -> Dict[str, MetricDefinition]:
        """列出所有指标"""
        return self.metrics.copy()
        
    def list_by_category(self, category: MetricCategory) -> Dict[str, MetricDefinition]:
        """按分类列出指标"""
        return {
            name: metric for name, metric in self.metrics.items()
            if metric.category == category
        }
