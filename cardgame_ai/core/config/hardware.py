"""
硬件配置模块
GPU、内存、计算资源相关配置
"""

from dataclasses import dataclass, field
from typing import Optional, Dict, Any, List
from .base import BaseConfig, ConfigRegistry

@dataclass
class GPUConfig(BaseConfig):
    """GPU配置"""
    device: str = "cuda"
    device_ids: List[int] = field(default_factory=lambda: [0])
    
    # 内存管理
    memory_fraction: float = 0.9
    allow_growth: bool = True
    empty_cache_interval: int = 100
    
    # 性能优化
    use_cudnn: bool = True
    cudnn_benchmark: bool = True
    cudnn_deterministic: bool = False
    
    # 混合精度
    use_amp: bool = True
    amp_opt_level: str = "O1"  # O0, O1, O2, O3
    
    # TensorCore
    use_tensor_cores: bool = True
    allow_tf32: bool = True
    
    def validate(self) -> bool:
        """验证配置有效性"""
        if self.device == "cuda":
            import torch
            if not torch.cuda.is_available():
                raise ValueError("CUDA is not available")
            
            for device_id in self.device_ids:
                if device_id >= torch.cuda.device_count():
                    raise ValueError(f"Invalid device id: {device_id}")
                    
        if not 0 <= self.memory_fraction <= 1:
            raise ValueError("memory_fraction must be in [0, 1]")
            
        return True

@dataclass
class CPUConfig(BaseConfig):
    """CPU配置"""
    num_threads: int = -1  # -1 for auto
    thread_affinity: bool = True
    
    # NUMA设置
    numa_nodes: Optional[List[int]] = None
    
    # Intel MKL
    use_mkl: bool = True
    mkl_threads: Optional[int] = None
    
    def __post_init__(self):
        """初始化后处理"""
        super().__post_init__()
        
        if self.num_threads == -1:
            import multiprocessing
            self.num_threads = multiprocessing.cpu_count()

@dataclass
class MemoryConfig(BaseConfig):
    """内存配置"""
    # 系统内存
    max_memory_gb: float = -1  # -1 for auto
    memory_limit_policy: str = "warn"  # warn, error, auto_reduce
    
    # 缓存设置
    cache_size_mb: int = 1024
    use_memory_pool: bool = True
    pool_size_mb: int = 2048
    
    # 数据加载
    pin_memory: bool = True
    num_workers: int = 4
    prefetch_factor: int = 2
    
    # 清理策略
    gc_interval: int = 1000
    clear_cache_interval: int = 100
    
    def get_memory_limit_bytes(self) -> int:
        """获取内存限制（字节）"""
        if self.max_memory_gb == -1:
            import psutil
            return int(psutil.virtual_memory().total * 0.8)
        else:
            return int(self.max_memory_gb * 1024 * 1024 * 1024)

@dataclass
class ComputeConfig(BaseConfig):
    """计算资源配置"""
    # 并行设置
    data_parallel: bool = False
    model_parallel: bool = False
    pipeline_parallel: bool = False
    
    # 编译优化
    use_torch_compile: bool = True
    compile_mode: str = "default"  # default, reduce-overhead, max-autotune
    compile_backend: str = "inductor"
    
    # 算子优化
    use_flash_attention: bool = True
    use_fused_optimizer: bool = True
    use_channels_last: bool = True
    
    # 性能分析
    enable_profiling: bool = False
    profile_wait_steps: int = 10
    profile_warmup_steps: int = 10
    profile_active_steps: int = 20

@dataclass
class HardwareConfig(BaseConfig):
    """硬件配置"""
    gpu: GPUConfig = field(default_factory=GPUConfig)
    cpu: CPUConfig = field(default_factory=CPUConfig)
    memory: MemoryConfig = field(default_factory=MemoryConfig)
    compute: ComputeConfig = field(default_factory=ComputeConfig)
    
    # 自动优化
    auto_optimize: bool = True
    benchmark_on_start: bool = False
    
    def validate(self) -> bool:
        """验证配置有效性"""
        self.gpu.validate()
        self.cpu.validate()
        self.memory.validate()
        self.compute.validate()
        return True
        
    def optimize_for_hardware(self):
        """根据硬件自动优化配置"""
        if not self.auto_optimize:
            return
            
        import torch
        import psutil
        
        # GPU优化
        if torch.cuda.is_available():
            gpu_memory = torch.cuda.get_device_properties(0).total_memory
            
            # 根据显存调整批量大小和缓存
            if gpu_memory < 8 * 1024**3:  # 8GB
                self.memory.cache_size_mb = 512
                self.memory.pool_size_mb = 1024
            elif gpu_memory < 16 * 1024**3:  # 16GB
                self.memory.cache_size_mb = 1024
                self.memory.pool_size_mb = 2048
            else:  # 16GB+
                self.memory.cache_size_mb = 2048
                self.memory.pool_size_mb = 4096
                
        # CPU优化
        cpu_count = psutil.cpu_count(logical=False)
        if cpu_count >= 8:
            self.cpu.num_threads = cpu_count - 2  # 预留2个核心
        else:
            self.cpu.num_threads = max(1, cpu_count - 1)

# 注册配置类
ConfigRegistry.register(GPUConfig)
ConfigRegistry.register(CPUConfig)
ConfigRegistry.register(MemoryConfig)
ConfigRegistry.register(ComputeConfig)
ConfigRegistry.register(HardwareConfig)
