# 质量验证系统快速开始指南

## 1分钟快速体验

```bash
# 1. 激活环境
conda activate cardgame

# 2. 运行质量验证演示
python integrate_health_checker.py --demo

# 3. 查看结果
```

## 5分钟完整验证

### 步骤1：环境准备

```bash
# 确保在项目根目录
cd /mnt/e/youyou/kaifa/xiangmu/hid5/bqq

# 激活conda环境
conda activate cardgame

# 验证环境
python -c "import torch; print(f'PyTorch: {torch.__version__}')"
python -c "import torch; print(f'CUDA可用: {torch.cuda.is_available()}')"
```

### 步骤2：运行质量测试

```bash
# 运行核心质量测试
pytest tests/quality/test_training_quality.py::TestPrerequisites -v

# 如果通过，运行架构测试
pytest tests/quality/test_training_quality.py::TestArchitecture -v
```

### 步骤3：查看质量报告

```bash
# 生成质量报告
python -c "
from cardgame_ai.validation.training_quality_validator import TrainingQualityValidator
validator = TrainingQualityValidator()
report = validator.generate_report()
print(f'质量得分: {report.overall_score:.1f}/100')
"
```

## 10分钟训练验证

### 选项A：使用健康感知训练（推荐）

```bash
# 启动带完整质量保障的训练
python integrate_health_checker.py --train

# 训练会自动进行：
# - 前提条件验证
# - 架构一致性检查
# - 实时性能监控
# - 健康状态检查
# - 自动问题恢复
```

### 选项B：分步验证

```bash
# 1. 验证前提条件
python -m cardgame_ai.validation.training_quality_validator validate_prerequisites

# 2. 验证架构
python -m cardgame_ai.validation.training_quality_validator validate_architecture

# 3. 运行性能基准
python -m cardgame_ai.testing.performance_benchmark

# 4. 启动训练（带验证）
python cardgame_ai/zhuchengxu/auto_deploy.py --validate
```

## 常用命令速查

### 质量验证

```bash
# 完整质量测试套件
pytest tests/quality/ -v

# 快速烟雾测试
pytest -m smoke -v

# 关键测试
pytest -m critical -v

# 生成HTML报告
pytest tests/quality/ --html=quality_report.html --self-contained-html
```

### 性能监控

```bash
# 实时GPU监控
watch -n 1 nvidia-smi

# 查看训练日志
tail -f logs/training.log

# 监控质量指标
tail -f logs/training.log | grep -E "(质量|胜率|GPU|损失)"
```

### 健康检查

```bash
# 查看健康状态
python -c "
from cardgame_ai.monitoring.training_health_checker import create_health_checker_with_defaults
hc = create_health_checker_with_defaults()
print(hc.get_health_status())
"

# 查看恢复日志
tail -f checkpoints/recovery_logs/recovery_$(date +%Y%m%d).jsonl
```

## 配置模板

### 最小配置（快速测试）

```yaml
# quick_test_config.yaml
training:
  batch_size: 16
  num_actors: 4
  max_steps: 1000

quality_validation:
  enable_monitoring: true
  enable_health_check: false
```

### 生产配置（完整质量保障）

```yaml
# production_config.yaml
training:
  batch_size: 32
  num_actors: 8
  
quality_validation:
  enable_monitoring: true
  enable_health_check: true
  check_interval: 30
  
performance:
  target_gpu_utilization: 98
  target_win_rate: 90
  
health_check:
  max_recovery_attempts: 5
  max_stagnation_time: 600
```

## 期望输出示例

### 成功的质量验证

```
=============== 质量验证报告 ===============
前提条件验证: ✅ 通过 (100.0分)
架构一致性: ✅ 通过 (95.5分)
性能基准: ✅ 通过 (98.2分)
  - GPU利用率: 98.5%
  - 吞吐量: 1250 samples/s
  - 内存减少: 65.3%

总体质量得分: 96.8/100 (S级)
状态: 生产就绪
=========================================
```

### 健康检查输出

```
步骤 500:
  损失: 0.4523
  胜率: 78.5%
  健康状态: healthy
  恢复尝试: 0
  GPU利用率: 98.2%
  GPU内存: 82.5%
```

## 下一步

1. 阅读[完整使用指南](quality_validation_guide.md)了解所有功能
2. 查看[API参考](api_reference.md)了解详细接口
3. 参考[健康检查器指南](health_checker_usage.md)配置自动恢复
4. 运行 `pytest tests/quality/ -v` 验证您的配置

## 故障快速排查

| 问题 | 解决方案 |
|------|----------|
| CUDA不可用 | 检查GPU驱动，确保PyTorch GPU版本 |
| 维度错误 | 运行 `python comprehensive_dimension_test.py` |
| 性能不达标 | 增加batch_size，检查GPU利用率 |
| 测试失败 | 查看详细错误信息，按建议修复 |

需要帮助？运行 `python integrate_health_checker.py` 查看所有选项。