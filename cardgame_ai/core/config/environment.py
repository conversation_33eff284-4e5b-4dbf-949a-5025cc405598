"""
环境配置模块
游戏环境、规则和奖励相关配置
"""

from dataclasses import dataclass, field
from typing import Optional, Dict, Any, List
from enum import Enum
from .base import BaseConfig, ConfigRegistry

class PlayerRole(str, Enum):
    """玩家角色"""
    LANDLORD = "landlord"
    FARMER = "farmer"
    
class GamePhase(str, Enum):
    """游戏阶段"""
    BIDDING = "bidding"
    PLAYING = "playing"
    FINISHED = "finished"

@dataclass
class RewardConfig(BaseConfig):
    """奖励配置"""
    # 基础奖励
    win_reward: float = 1.0
    lose_reward: float = -1.0
    draw_reward: float = 0.0
    
    # 特殊奖励
    spring_multiplier: float = 2.0  # 春天倍数
    bomb_reward: float = 0.1  # 炸弹奖励
    
    # 农民合作奖励
    cooperation_bonus: float = 0.2
    synergy_threshold: float = 0.8
    
    # 中间奖励
    card_play_reward: float = 0.01
    pass_penalty: float = -0.005
    
    # 塑形奖励
    enable_shaping: bool = True
    shaping_gamma: float = 0.99
    
    def calculate_reward(self, outcome: str, role: PlayerRole, **kwargs) -> float:
        """计算奖励"""
        base_reward = 0.0
        
        if outcome == "win":
            base_reward = self.win_reward
        elif outcome == "lose":
            base_reward = self.lose_reward
        else:
            base_reward = self.draw_reward
            
        # 春天加成
        if kwargs.get("is_spring", False):
            base_reward *= self.spring_multiplier
            
        # 炸弹奖励
        bomb_count = kwargs.get("bomb_count", 0)
        base_reward += bomb_count * self.bomb_reward
        
        # 农民合作奖励
        if role == PlayerRole.FARMER and kwargs.get("cooperation_score", 0) > self.synergy_threshold:
            base_reward += self.cooperation_bonus
            
        return base_reward

@dataclass
class GameConfig(BaseConfig):
    """游戏配置"""
    # 基础设置
    num_players: int = 3
    max_steps: int = 100
    deck_size: int = 54
    
    # 规则设置
    allow_three_with_one: bool = True
    allow_three_with_two: bool = True
    allow_four_with_two: bool = True
    allow_consecutive_pairs: bool = True
    
    # 叫牌规则
    bidding_options: List[int] = field(default_factory=lambda: [1, 2, 3])
    must_bid: bool = False
    
    # 时间限制
    time_limit_per_step: float = 30.0
    total_time_limit: float = 600.0
    
@dataclass
class EnvironmentConfig(BaseConfig):
    """环境配置"""
    # 游戏配置
    game: GameConfig = field(default_factory=GameConfig)
    
    # 奖励配置
    reward: RewardConfig = field(default_factory=RewardConfig)
    
    # 观察空间
    observation_type: str = "dict"  # dict, array, image
    observation_shape: Optional[List[int]] = None
    normalize_observations: bool = True
    
    # 动作空间
    action_space_size: int = 2300
    filter_legal_actions: bool = True
    
    # 环境设置
    seed: Optional[int] = None
    render_mode: Optional[str] = None  # human, rgb_array
    
    # 对手设置
    opponent_type: str = "self_play"  # self_play, random, rule_based, human
    opponent_strength: float = 1.0
    
    def validate(self) -> bool:
        """验证配置有效性"""
        self.game.validate()
        self.reward.validate()
        
        if self.action_space_size <= 0:
            raise ValueError("action_space_size must be positive")
            
        return True

# 注册配置类
ConfigRegistry.register(RewardConfig)
ConfigRegistry.register(GameConfig)
ConfigRegistry.register(EnvironmentConfig)
