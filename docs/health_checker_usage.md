# 训练健康检查和自动恢复机制使用指南

## 概述

TrainingHealthChecker 是一个全面的训练健康监控和自动恢复系统，用于确保斗地主AI训练过程的稳定性和可靠性。它提供了进程监控、资源管理、自动恢复等功能。

## 功能特性

### 1. 健康检查功能
- **进程存活检查**: 监控训练进程是否正常运行
- **训练进度监控**: 检测训练是否停滞
- **资源使用检查**: 监控CPU、内存、GPU使用情况
- **检查点完整性验证**: 确保检查点文件有效
- **训练指标监控**: 检测NaN损失等异常

### 2. 自动恢复机制
- **清理缓存** (CLEAR_CACHE): 清理GPU缓存和临时文件
- **减小批次大小** (REDUCE_BATCH_SIZE): 自动调整批次大小缓解OOM
- **从检查点恢复** (RESTART_FROM_CHECKPOINT): 从最近的有效检查点恢复训练
- **重启进程** (RESTART_PROCESS): 重启死亡的训练进程
- **紧急保存** (EMERGENCY_SAVE): 在崩溃前保存训练状态

### 3. 健康状态
- **HEALTHY**: 系统运行正常
- **WARNING**: 存在潜在问题但可继续运行
- **CRITICAL**: 严重问题，需要干预
- **DEAD**: 进程已死亡
- **RECOVERING**: 正在执行恢复操作

## 安装和依赖

```bash
# 确保已安装项目依赖
pip install -r requirements.txt

# 额外依赖（用于进程管理）
pip install psutil
```

## 基本使用

### 1. 独立使用

```python
from cardgame_ai.monitoring.training_health_checker import TrainingHealthChecker

# 创建健康检查器
health_checker = TrainingHealthChecker({
    'check_interval': 30.0,        # 检查间隔（秒）
    'max_recovery_attempts': 3,    # 最大恢复尝试次数
    'checkpoint_dir': 'checkpoints'
})

# 启动健康检查
health_checker.start_checking()

# ... 运行训练 ...

# 停止健康检查
health_checker.stop_checking()
```

### 2. 与训练监控器集成

```python
from cardgame_ai.monitoring.training_health_checker import TrainingHealthChecker
from cardgame_ai.monitoring.realtime_training_monitor import RealtimeTrainingMonitor

# 创建监控器
monitor = RealtimeTrainingMonitor()
monitor.start_monitoring()

# 创建健康检查器并关联监控器
health_checker = TrainingHealthChecker()
health_checker.start_checking(monitor)

# 训练过程中更新指标
monitor.update_training_metrics(
    loss=0.5,
    win_rate=75.0,
    episodes=1000,
    samples=100000
)
```

### 3. 集成到训练流程

```python
from integrate_health_checker import HealthAwareTrainingManager

# 创建健康感知的训练管理器
manager = HealthAwareTrainingManager("config.yaml")

# 启动带健康检查的训练
manager.start_training_with_health_check()
```

## 配置选项

```python
config = {
    # 基础配置
    'check_interval': 30.0,           # 健康检查间隔（秒）
    'max_stagnation_time': 600,       # 最大停滞时间（秒）
    'max_recovery_attempts': 3,       # 最大恢复尝试次数
    'checkpoint_dir': 'checkpoints',  # 检查点目录
    
    # 健康阈值
    'min_progress_rate': 0.1,         # 最小进度率（episodes/分钟）
    'max_memory_usage': 90,           # 最大内存使用率（%）
    'max_gpu_memory': 95,             # 最大GPU内存使用率（%）
    'min_checkpoint_interval': 300,   # 最小检查点间隔（秒）
    'max_loss_nan_count': 5,          # 最大NaN损失次数
    'max_oom_count': 3                # 最大OOM次数
}
```

## 恢复回调

### 注册自定义恢复回调

```python
def my_recovery_callback(action: str, recovery_log: dict):
    """自定义恢复回调"""
    print(f"执行恢复动作: {action}")
    
    if recovery_log['success']:
        # 恢复成功，可以发送通知等
        send_notification(f"恢复成功: {action}")
    else:
        # 恢复失败，记录错误
        log_error(f"恢复失败: {action} - {recovery_log['error']}")

# 注册回调
health_checker.register_recovery_callback(my_recovery_callback)
```

## 手动干预

### 手动触发恢复

```python
from cardgame_ai.monitoring.training_health_checker import RecoveryAction

# 手动清理缓存
success = health_checker.manual_recovery(RecoveryAction.CLEAR_CACHE)

# 手动减小批次大小
success = health_checker.manual_recovery(RecoveryAction.REDUCE_BATCH_SIZE)

# 手动从检查点恢复
success = health_checker.manual_recovery(RecoveryAction.RESTART_FROM_CHECKPOINT)
```

### 获取健康状态

```python
# 获取当前健康状态
status = health_checker.get_health_status()
print(f"健康状态: {status['status']}")
print(f"恢复尝试: {status['recovery_attempts']}")
print(f"错误计数: {status['error_counts']}")
```

## 日志和报告

### 1. 恢复日志
恢复操作会记录到 `checkpoints/recovery_logs/` 目录：
```
recovery_20250705.jsonl  # 按日期分组的恢复日志
```

### 2. 健康报告
停止检查时会生成健康报告：
```
health_report_20250705_143025.json  # 包含健康统计和建议
```

### 3. 紧急保存
紧急情况下的数据保存在：
```
checkpoints/emergency/
├── emergency_checkpoint_20250705_143025.pth  # 紧急检查点
└── emergency_state_20250705_143025.json      # 紧急状态信息
```

## 最佳实践

### 1. 设置合理的检查间隔
```python
# 开发环境：频繁检查
config['check_interval'] = 10.0

# 生产环境：平衡性能
config['check_interval'] = 30.0
```

### 2. 配置恢复策略
```python
# 保守策略：少量恢复尝试
config['max_recovery_attempts'] = 2

# 积极策略：更多恢复尝试
config['max_recovery_attempts'] = 5
```

### 3. 监控关键指标
```python
# 注册警报回调
def alert_callback(alert_type, message):
    if alert_type == 'gpu_memory_high':
        # 提前干预，避免OOM
        health_checker.manual_recovery(RecoveryAction.CLEAR_CACHE)

monitor.register_alert_callback(alert_callback)
```

## 故障排除

### 常见问题

1. **恢复操作失败**
   - 检查是否有足够的权限
   - 确认检查点文件存在且有效
   - 查看恢复日志了解详细错误

2. **频繁触发恢复**
   - 调整健康阈值
   - 检查训练配置是否合理
   - 考虑增加资源（内存、GPU）

3. **进程无法重启**
   - 确认训练命令正确
   - 检查Python环境
   - 查看进程输出日志

### 调试模式

```python
# 启用详细日志
import logging
logging.getLogger('cardgame_ai.monitoring').setLevel(logging.DEBUG)

# 测试模式
python cardgame_ai/monitoring/training_health_checker.py --test
```

## 示例场景

### 场景1：长时间训练监控

```python
# 适合过夜训练的配置
config = {
    'check_interval': 60.0,           # 每分钟检查一次
    'max_stagnation_time': 1800,      # 30分钟无进展则恢复
    'max_recovery_attempts': 5,       # 允许更多恢复尝试
    'max_gpu_memory': 90              # 更保守的GPU内存阈值
}

health_checker = TrainingHealthChecker(config)
```

### 场景2：资源受限环境

```python
# 适合GPU内存有限的环境
config = {
    'max_gpu_memory': 85,             # 更早触发批次大小调整
    'max_oom_count': 2,               # 减少OOM容忍度
    'batch_size': 16                  # 初始小批次
}

# 启用自动批次大小调整
from cardgame_ai.core.monitoring.gpu_memory_manager import GPUMemoryManager
health_checker.gpu_memory_manager = GPUMemoryManager()
```

### 场景3：实验性训练

```python
# 适合快速迭代实验
config = {
    'check_interval': 5.0,            # 频繁检查
    'max_recovery_attempts': 1,       # 快速失败
    'max_loss_nan_count': 3           # 对NaN更敏感
}

# 添加实验记录
def experiment_callback(action, recovery_log):
    # 记录实验中的问题
    with open('experiment_issues.log', 'a') as f:
        f.write(f"{recovery_log['timestamp']}: {action}\n")

health_checker.register_recovery_callback(experiment_callback)
```

## 扩展和自定义

### 添加自定义健康检查

```python
class CustomHealthChecker(TrainingHealthChecker):
    def _perform_health_check(self):
        # 调用父类检查
        result = super()._perform_health_check()
        
        # 添加自定义检查
        custom_status = self._check_custom_metric()
        if custom_status != 'healthy':
            result['status'] = HealthStatus.WARNING
            result['issues'].append('自定义指标异常')
        
        return result
    
    def _check_custom_metric(self):
        # 实现自定义检查逻辑
        return 'healthy'
```

## 总结

TrainingHealthChecker 提供了一个强大的训练保障机制，通过自动化的健康检查和恢复，大大提高了长时间训练的稳定性。合理配置和使用该组件，可以有效减少人工干预，提高训练效率。