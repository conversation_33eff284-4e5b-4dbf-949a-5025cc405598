"""EfficientZero V2 主算法类

整合所有组件，提供完整的EfficientZero V2实现
"""

import os
import torch
import torch.nn as nn
import numpy as np
from typing import Dict, List, Tuple, Optional, Any, Union

from cardgame_ai.utils.logging import get_logger
# 注释掉循环导入，在类中实现基础功能
from cardgame_ai.core.config import EfficientZeroConfig
from cardgame_ai.core.base import State, Action

from .value_aggregator import SVEValueAggregator
from .self_supervised import SimSiamSelfSupervised
from .value_prefix import ValuePrefixPredictor
from .search_module import SearchModule

logger = get_logger(__name__)

# 导入观察空间压缩支持
try:
    from cardgame_ai.games.doudizhu.compact_observation import CompactObservationEncoder
    HAS_OBSERVATION_COMPRESSION = True
except ImportError:
    HAS_OBSERVATION_COMPRESSION = False
    logger.warning("无法导入CompactObservationEncoder，observation_compression功能将不可用")



class EfficientZeroV2Algorithm(nn.Module):
    """EfficientZero V2算法类 - 完整的PyTorch模块实现
    
    主要改进：
    1. Search-based Value Estimation (SVE)
    2. SimSiam自监督学习
    3. 增强的价值前缀预测
    4. 改进的搜索策略
    5. 完整的训练接口
    """
    
    def __init__(self, observation_shape: Tuple[int, ...], action_space_size: int, config: EfficientZeroConfig):
        """初始化EfficientZero V2算法
        
        Args:
            observation_shape: 观察空间形状
            action_space_size: 动作空间大小
            config: 算法配置
        """
        super().__init__()
        
        self.observation_shape = observation_shape
        self.action_space_size = action_space_size
        self.config = config
        
        # 设置设备
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 初始化核心网络（使用现有的EfficientZeroModel）
        # 检查是否启用了观察空间压缩
        use_observation_compression = False
        actual_observation_shape = observation_shape
        
        # 检查Phase4配置中是否启用了observation_compression
        if hasattr(config, 'phase4') and config.phase4:
            phase4_config = config.phase4
            # 检查features字典中的observation_compression是否为True
            if hasattr(phase4_config, 'features') and isinstance(phase4_config.features, dict):
                if phase4_config.features.get('observation_compression', False):
                    if HAS_OBSERVATION_COMPRESSION:
                        use_observation_compression = True
                        # 调整观察空间为压缩后的维度
                        if len(observation_shape) == 1 and observation_shape[0] == 656:
                            actual_observation_shape = (116,)
                            logger.info(f"检测到Phase 4配置中启用了observation_compression，调整观察空间: {observation_shape} -> {actual_observation_shape}")
                    else:
                        logger.warning("observation_compression已配置但相关模块不可用")
        
        from cardgame_ai.algorithms.efficient_zero_model import EfficientZeroModel
        
        # 确保维度一致性
        hidden_dim = getattr(config, 'hidden_size', 256)  # 使用config.hidden_size而不是config.model.hidden_dim
        state_dim = getattr(config, 'state_dim', hidden_dim)  # 默认使用hidden_dim确保一致性
        
        self.model = EfficientZeroModel(
            observation_shape=actual_observation_shape,
            action_shape=(action_space_size,),
            hidden_dim=hidden_dim,
            state_dim=state_dim,  # 确保与hidden_dim一致
            use_distributional_value=getattr(config, 'use_distributional_value', False),
            value_support_size=getattr(config, 'support_size', 601),
            device=self.device
        )
        
        logger.info(f"模型维度配置 - hidden_dim: {hidden_dim}, state_dim: {state_dim}")
        
        # V2特有组件
        self.sve_aggregator = SVEValueAggregator(
            aggregation_method=getattr(config, 'sve_aggregation', 'adaptive')
        )
        
        self.ssl_module = SimSiamSelfSupervised(
            representation_dim=hidden_dim,  # 使用统一的hidden_dim
            projection_dim=getattr(config, 'simsiam_proj_out_dim', 2048)  # 使用config中的simsiam配置，默认2048
        )
        
        self.value_prefix_predictor = ValuePrefixPredictor(
            hidden_size=hidden_dim,  # 使用统一的hidden_dim
            num_steps=getattr(config, 'value_prefix_steps', 5)
        )
        
        # 将配置转换为字典格式
        if hasattr(config, '__dict__'):
            config_dict = config.__dict__
        else:
            config_dict = {}
        self.search_module = SearchModule(config=config_dict)
        
        # 训练参数
        self.ssl_weight = getattr(config, 'ssl_weight', 0.1)
        self.value_prefix_weight = getattr(config, 'value_prefix_weight', 0.1)
        self.sve_update_frequency = getattr(config, 'sve_update_frequency', 100)
        
        # 损失权重
        self.policy_loss_weight = getattr(config, 'policy_loss_weight', 1.0)
        self.value_loss_weight = getattr(config, 'value_loss_weight', 0.25)
        self.reward_loss_weight = getattr(config, 'reward_loss_weight', 1.0)
        
        # 训练统计
        self.training_stats = {
            'sve_values': [],
            'ssl_losses': [],
            'value_prefix_losses': [],
            'search_improvements': []
        }
        
        # 动作映射器 - 处理565维到198维的映射
        self._setup_action_mapping()
        
        # 训练计数器
        self.training_step = 0
        
        # 确保所有子模块都在正确的设备上
        self.to(self.device)
        # 确保SSL模块的projector也在正确设备上
        if hasattr(self.ssl_module, 'projector'):
            self.ssl_module.projector = self.ssl_module.projector.to(self.device)
        
        logger.info(f"EfficientZero V2算法初始化完成: 观察空间{observation_shape}, 动作空间{action_space_size}, 设备: {self.device}")
    
    def _setup_action_mapping(self):
        """设置动作空间映射"""
        # 从cardgame_ai导入动作映射器
        try:
            from cardgame_ai.core.action_mapping import get_global_action_mapper
            self.action_mapper = get_global_action_mapper()
            # 尝试获取实际动作数量
            try:
                real_action_count = len(self.action_mapper.get_legal_action_ids()) if hasattr(self.action_mapper, 'get_legal_action_ids') else 'unknown'
            except:
                real_action_count = 'unknown'
            logger.info(f"动作映射器设置完成: 网络动作空间={self.action_space_size}, 实际动作空间={real_action_count}")
        except ImportError:
            logger.warning("无法导入动作映射器，使用直接映射")
            self.action_mapper = None
    
    def compute_loss(self, batch) -> Dict[str, torch.Tensor]:
        """计算损失函数 - 训练器的核心接口 [修复版]
        
        主要修复：
        1. 修复distributional value处理
        2. 优化异常处理机制
        3. 修复总损失累加逻辑
        
        Args:
            batch: 训练批次数据
            
        Returns:
            包含各种损失的字典
        """
        losses = {}
        
        try:
            # 1. 提取批次数据
            if hasattr(batch, 'observations'):
                observations = batch.observations
            elif hasattr(batch, '__dict__') and 'observations' in batch.__dict__:
                observations = batch.__dict__['observations']
            elif isinstance(batch, dict) and 'observations' in batch:
                observations = batch['observations']
            else:
                # 尝试从batch的属性中找到观察数据
                obs_keys = ['obs', 'observation', 'state', 'states']
                observations = None
                for key in obs_keys:
                    if hasattr(batch, key):
                        observations = getattr(batch, key)
                        break
                    elif isinstance(batch, dict) and key in batch:
                        observations = batch[key]
                        break
                
                if observations is None:
                    raise ValueError(f"无法从batch中找到观察数据，batch类型: {type(batch)}, 属性: {dir(batch) if hasattr(batch, '__dict__') else 'no __dict__'}")
            
            # 确保observations是tensor
            if not torch.is_tensor(observations):
                observations = torch.tensor(observations, dtype=torch.float32)
            observations = observations.to(self.device)
            
            batch_size = observations.size(0)
            
            # 2. 获取目标数据
            targets = self._extract_targets_from_batch(batch)
            
            # 3. 前向传播
            # 初始推理
            initial_outputs = self.model.initial_inference(observations)
            
            # 计算策略损失
            if 'policy' in targets:
                target_policy = targets['policy'].to(self.device)
                predicted_policy = initial_outputs['policy']
                
                # 确保维度匹配
                if predicted_policy.size(-1) != target_policy.size(-1):
                    # 如果存在动作映射，进行维度调整
                    if self.action_mapper:
                        # 映射到实际动作空间
                        target_policy = self._map_policy_to_network_space(target_policy)
                    else:
                        # 简单截断或填充
                        min_dim = min(predicted_policy.size(-1), target_policy.size(-1))
                        predicted_policy = predicted_policy[:, :min_dim]
                        target_policy = target_policy[:, :min_dim]
                
                policy_loss = torch.nn.functional.cross_entropy(
                    predicted_policy, target_policy
                )
                losses['policy_loss'] = policy_loss
            else:
                # 如果没有策略目标，创建dummy损失
                policy_loss = torch.tensor(0.0, device=self.device, requires_grad=True)
                losses['policy_loss'] = policy_loss
            
            # 计算价值损失 - 修复distributional value处理
            if 'value' in targets:
                target_value = targets['value'].to(self.device)
                predicted_value = initial_outputs['value']
                
                # 关键修复：正确处理distributional value head
                if predicted_value.dim() > 1 and predicted_value.size(-1) == 601:
                    # Distributional value - 使用交叉熵损失
                    value_support = torch.linspace(-300, 300, 601, device=self.device)
                    
                    # 将目标值转换为分布
                    if target_value.dim() == 1:
                        target_value = target_value.unsqueeze(-1)
                    
                    # 计算目标分布
                    target_indices = torch.clamp(
                        ((target_value - (-300)) / (300 - (-300)) * 600).long(),
                        0, 600
                    ).squeeze(-1)
                    
                    # 使用交叉熵损失（保留梯度）
                    value_loss = torch.nn.functional.cross_entropy(
                        predicted_value, target_indices
                    )
                else:
                    # 标量值输出
                    if predicted_value.dim() > target_value.dim():
                        predicted_value = predicted_value.squeeze(-1)
                    elif predicted_value.dim() < target_value.dim():
                        target_value = target_value.squeeze(-1)
                    
                    value_loss = torch.nn.functional.mse_loss(predicted_value, target_value)
                
                losses['value_loss'] = value_loss
            else:
                # 创建有梯度的占位损失
                value_loss = torch.tensor(0.0, device=self.device, requires_grad=True)
                losses['value_loss'] = value_loss
            
            # 计算奖励损失（如果有动作序列）
            if 'actions' in targets and 'rewards' in targets:
                reward_loss = self._compute_reward_loss(batch, targets)
                losses['reward_loss'] = reward_loss
            else:
                reward_loss = torch.tensor(0.0, device=self.device, requires_grad=True)
                losses['reward_loss'] = reward_loss
            
            # 4. V2特有损失
            # 自监督学习损失
            representations = self.model.represent(observations)
            if representations.size(0) >= 2:  # 需要至少2个样本进行对比学习
                ssl_loss = self._compute_ssl_loss(representations)
                losses['ssl_loss'] = ssl_loss
            else:
                ssl_loss = torch.tensor(0.0, device=self.device, requires_grad=True)
                losses['ssl_loss'] = ssl_loss
            
            # 价值前缀损失
            if 'future_values' in targets:
                value_prefix_loss = self._compute_value_prefix_loss(representations, targets['future_values'])
                losses['value_prefix_loss'] = value_prefix_loss
            else:
                value_prefix_loss = torch.tensor(0.0, device=self.device, requires_grad=True)
                losses['value_prefix_loss'] = value_prefix_loss
            
            # 修复总损失计算 - 确保所有损失都是tensor
            loss_components = []
            
            # 辅助函数：安全检查是否是有效的tensor损失
            def is_valid_loss(loss):
                try:
                    # 检查是否是tensor且是标量
                    return torch.is_tensor(loss) and (loss.numel() == 1) and loss.requires_grad
                except Exception:
                    # 如果检查失败，说明不是有效损失
                    return False
            
            if 'policy_loss' in losses and is_valid_loss(losses['policy_loss']):
                loss_components.append(losses['policy_loss'] * self.policy_loss_weight)
            if 'value_loss' in losses and is_valid_loss(losses['value_loss']):
                loss_components.append(losses['value_loss'] * self.value_loss_weight)
            if 'reward_loss' in losses and is_valid_loss(losses['reward_loss']):
                loss_components.append(losses['reward_loss'] * self.reward_loss_weight)
            if 'ssl_loss' in losses and is_valid_loss(losses['ssl_loss']):
                loss_components.append(losses['ssl_loss'] * self.ssl_weight)
            if 'value_prefix_loss' in losses and is_valid_loss(losses['value_prefix_loss']):
                loss_components.append(losses['value_prefix_loss'] * self.value_prefix_weight)
            
            # 直接求和，不从零开始累加
            if loss_components:
                total_loss = sum(loss_components)
            else:
                total_loss = torch.tensor(1e-6, device=self.device, requires_grad=True)
                
            losses['total_loss'] = total_loss
            
            # 更新训练统计
            self.training_step += 1
            self._update_training_stats(losses)
            
            # 梯度调试
            if self.training_step % 100 == 0:
                for key, loss in losses.items():
                    if torch.is_tensor(loss):
                        if hasattr(loss, 'grad_fn') and loss.grad_fn is not None:
                            logger.debug(f"{key}梯度函数: {loss.grad_fn}")
                        else:
                            logger.warning(f"{key}没有梯度函数")
            
            return losses
            
        except Exception as e:
            logger.error(f"计算损失时出错: {e}")
            # 限制异常处理 - 返回最小有效损失而不是零损失
            if "size mismatch" in str(e) or "dimension" in str(e):
                logger.warning("维度不匹配错误，返回最小有效损失")
                return {
                    'total_loss': torch.tensor(1e-4, device=self.device, requires_grad=True),
                    'policy_loss': torch.tensor(1e-4, device=self.device, requires_grad=True),
                    'value_loss': torch.tensor(1e-4, device=self.device, requires_grad=True),
                    'reward_loss': torch.tensor(0.0, device=self.device, requires_grad=True),
                    'ssl_loss': torch.tensor(0.0, device=self.device, requires_grad=True),
                    'value_prefix_loss': torch.tensor(0.0, device=self.device, requires_grad=True)
                }
            else:
                # 对于其他未知错误，重新抛出以便调试
                raise e
    
    def _extract_targets_from_batch(self, batch) -> Dict[str, torch.Tensor]:
        """从批次中提取目标数据 - 确保所有数据都是Tensor类型"""
        targets = {}
        
        def ensure_tensor(data, key_name: str):
            """确保数据是tensor类型"""
            if data is None:
                return None
                
            if torch.is_tensor(data):
                return data
            elif isinstance(data, (list, tuple, np.ndarray)):
                try:
                    return torch.tensor(data, dtype=torch.float32)
                except Exception as e:
                    logger.warning(f"转换{key_name}到tensor失败: {e}")
                    return None
            elif isinstance(data, (int, float)):
                # 单个标量转换为1维tensor
                return torch.tensor([data], dtype=torch.float32)
            else:
                logger.warning(f"{key_name}的数据类型不支持: {type(data)}")
                return None
        
        # 策略目标
        policy_keys = ['policy', 'policies', 'target_policy', 'mcts_policy', 'action_probs']
        for key in policy_keys:
            if hasattr(batch, key):
                data = getattr(batch, key)
                tensor_data = ensure_tensor(data, f'policy/{key}')
                if tensor_data is not None:
                    targets['policy'] = tensor_data
                    break
            elif isinstance(batch, dict) and key in batch:
                data = batch[key]
                tensor_data = ensure_tensor(data, f'policy/{key}')
                if tensor_data is not None:
                    targets['policy'] = tensor_data
                    break
        
        # 价值目标
        value_keys = ['value', 'values', 'target_value', 'returns', 'rewards']
        for key in value_keys:
            if hasattr(batch, key):
                data = getattr(batch, key)
                tensor_data = ensure_tensor(data, f'value/{key}')
                if tensor_data is not None:
                    targets['value'] = tensor_data
                    break
            elif isinstance(batch, dict) and key in batch:
                data = batch[key]
                tensor_data = ensure_tensor(data, f'value/{key}')
                if tensor_data is not None:
                    targets['value'] = tensor_data
                    break
        
        # 动作序列
        action_keys = ['actions', 'action', 'action_sequence']
        for key in action_keys:
            if hasattr(batch, key):
                data = getattr(batch, key)
                tensor_data = ensure_tensor(data, f'action/{key}')
                if tensor_data is not None:
                    targets['actions'] = tensor_data
                    break
            elif isinstance(batch, dict) and key in batch:
                data = batch[key]
                tensor_data = ensure_tensor(data, f'action/{key}')
                if tensor_data is not None:
                    targets['actions'] = tensor_data
                    break
        
        # 奖励序列
        reward_keys = ['rewards', 'reward', 'reward_sequence']
        for key in reward_keys:
            if hasattr(batch, key):
                data = getattr(batch, key)
                tensor_data = ensure_tensor(data, f'reward/{key}')
                if tensor_data is not None:
                    targets['rewards'] = tensor_data
                    break
            elif isinstance(batch, dict) and key in batch:
                data = batch[key]
                tensor_data = ensure_tensor(data, f'reward/{key}')
                if tensor_data is not None:
                    targets['rewards'] = tensor_data
                    break
        
        # 未来价值（用于价值前缀）
        future_value_keys = ['future_values', 'value_prefix', 'bootstrap_values']
        for key in future_value_keys:
            if hasattr(batch, key):
                data = getattr(batch, key)
                tensor_data = ensure_tensor(data, f'future_value/{key}')
                if tensor_data is not None:
                    targets['future_values'] = tensor_data
                    break
            elif isinstance(batch, dict) and key in batch:
                data = batch[key]
                tensor_data = ensure_tensor(data, f'future_value/{key}')
                if tensor_data is not None:
                    targets['future_values'] = tensor_data
                    break
        
        return targets
    
    def _map_policy_to_network_space(self, policy: torch.Tensor) -> torch.Tensor:
        """将策略映射到网络动作空间"""
        if self.action_mapper is None:
            return policy
        
        # 获取映射关系
        try:
            # 这里需要实现具体的映射逻辑
            # 暂时返回截断的策略
            network_dim = self.action_space_size
            real_dim = policy.size(-1)
            
            if network_dim == real_dim:
                return policy
            elif network_dim > real_dim:
                # 填充零
                padding = torch.zeros(policy.size(0), network_dim - real_dim, device=policy.device)
                return torch.cat([policy, padding], dim=-1)
            else:
                # 截断
                return policy[:, :network_dim]
        except Exception as e:
            logger.warning(f"动作映射失败: {e}，使用截断策略")
            min_dim = min(self.action_space_size, policy.size(-1))
            return policy[:, :min_dim]
    
    def _compute_reward_loss(self, batch, targets: Dict[str, torch.Tensor]) -> torch.Tensor:
        """计算奖励损失"""
        try:
            actions = targets['actions'].to(self.device)
            rewards = targets['rewards'].to(self.device)
            
            # 提取观察
            if hasattr(batch, 'observations'):
                observations = batch.observations.to(self.device)
            else:
                return torch.tensor(0.0, device=self.device, requires_grad=True)
            
            # 获取初始状态
            initial_outputs = self.model.initial_inference(observations)
            hidden_state = initial_outputs['hidden_state']
            
            # 计算递归推理的奖励损失
            total_reward_loss = torch.tensor(0.0, device=self.device, requires_grad=True)
            
            # 限制序列长度以避免内存问题
            max_steps = min(5, actions.size(1) if actions.dim() > 1 else 1)
            
            for step in range(max_steps):
                if actions.dim() > 1:
                    action = actions[:, step]
                    target_reward = rewards[:, step] if rewards.dim() > 1 else rewards
                else:
                    action = actions
                    target_reward = rewards
                    
                # 递归推理
                recurrent_outputs = self.model.recurrent_inference(hidden_state, action)
                predicted_reward = recurrent_outputs['reward']
                
                # 计算奖励损失
                if predicted_reward.dim() > target_reward.dim():
                    predicted_reward = predicted_reward.squeeze(-1)
                
                step_reward_loss = torch.nn.functional.mse_loss(predicted_reward, target_reward)
                total_reward_loss = total_reward_loss + step_reward_loss
                
                # 更新隐藏状态
                hidden_state = recurrent_outputs['hidden_state']
                
                if actions.dim() == 1:  # 只有一个时间步
                    break
            
            return total_reward_loss / max_steps
            
        except Exception as e:
            logger.warning(f"计算奖励损失失败: {e}")
            return torch.tensor(0.0, device=self.device, requires_grad=True)
    
    def _compute_ssl_loss(self, representations: torch.Tensor) -> torch.Tensor:
        """计算自监督学习损失"""
        try:
            # 确保representations在正确的设备上
            representations = representations.to(self.device)
            
            # 如果SSL模块存在且在正确设备上
            if hasattr(self, 'ssl_module') and self.ssl_module is not None:
                # 确保SSL模块的projector也在正确设备上
                if hasattr(self.ssl_module, 'projector'):
                    self.ssl_module.projector = self.ssl_module.projector.to(self.device)
                
                # 使用SSL模块的标准接口计算损失
                ssl_stats = self.ssl_module.train_step(representations, augmentation_type='noise')
                
                # 从返回的字典中获取tensor形式的损失
                # SSL模块返回: {'ssl_loss': tensor, 'ssl_loss_value': float, ...}
                if isinstance(ssl_stats, dict) and 'ssl_loss' in ssl_stats:
                    ssl_loss = ssl_stats['ssl_loss']
                    # 确保返回的是tensor
                    if torch.is_tensor(ssl_loss) and ssl_loss.requires_grad:
                        return ssl_loss
                    else:
                        # 如果返回的不是带梯度的tensor，创建一个
                        logger.warning(f"SSL损失不是带梯度的tensor: {type(ssl_loss)}")
                        return torch.tensor(0.0, device=self.device, requires_grad=True)
                else:
                    # 如果没有返回ssl_loss，创建一个带梯度的零损失
                    logger.warning("SSL模块没有返回ssl_loss")
                    return torch.tensor(0.0, device=self.device, requires_grad=True)
            
            # 如果SSL模块不可用，返回零损失
            return torch.tensor(0.0, device=self.device, requires_grad=True)
            
        except Exception as e:
            logger.warning(f"计算SSL损失失败: {e}")
            # 确保返回的tensor在正确设备上
            return torch.tensor(0.0, device=self.device, requires_grad=True)
    
    def _compute_value_prefix_loss(self, representations: torch.Tensor, future_values: torch.Tensor) -> torch.Tensor:
        """计算价值前缀损失"""
        try:
            future_values = future_values.to(self.device)
            
            # 使用价值前缀预测器
            predicted_values = self.value_prefix_predictor(representations)
            
            # 确保维度匹配
            if predicted_values.dim() != future_values.dim():
                if predicted_values.dim() > future_values.dim():
                    predicted_values = predicted_values.squeeze(-1)
                else:
                    future_values = future_values.squeeze(-1)
            
            # 限制序列长度
            min_len = min(predicted_values.size(-1), future_values.size(-1))
            predicted_values = predicted_values[..., :min_len]
            future_values = future_values[..., :min_len]
            
            return torch.nn.functional.mse_loss(predicted_values, future_values)
            
        except Exception as e:
            logger.warning(f"计算价值前缀损失失败: {e}")
            return torch.tensor(0.0, device=self.device, requires_grad=True)
    
    def initial_inference(self, observation: torch.Tensor) -> Dict[str, torch.Tensor]:
        """初始推理 - 训练器需要的接口"""
        return self.model.initial_inference(observation)
    
    def recurrent_inference(self, hidden_state: torch.Tensor, action: torch.Tensor) -> Dict[str, torch.Tensor]:
        """递归推理 - 训练器需要的接口"""
        return self.model.recurrent_inference(hidden_state, action)
    
    def update(self, batch) -> Dict[str, float]:
        """更新模型参数 - 训练器需要的接口
        
        Args:
            batch: 训练批次
            
        Returns:
            损失字典
        """
        # 这个方法通常由训练器调用，我们这里只计算损失
        # 实际的参数更新由训练器的优化器完成
        losses = self.compute_loss(batch)
        
        # 转换为float类型 - 安全处理非tensor值
        float_losses = {}
        for key, value in losses.items():
            if torch.is_tensor(value):
                # tensor转float
                try:
                    float_losses[key] = value.item()
                except Exception as e:
                    logger.warning(f"无法转换{key}的tensor值: {e}")
                    float_losses[key] = 0.0
            elif isinstance(value, (int, float)):
                # 已经是数值类型
                float_losses[key] = float(value)
            else:
                # 其他类型，记录警告
                logger.warning(f"{key}的值类型异常: {type(value)}")
                float_losses[key] = 0.0
        
        return float_losses
        
    def _update_sve_estimation(self, batch: Dict[str, torch.Tensor]) -> Dict[str, float]:
        """更新搜索价值估计
        
        Args:
            batch: 训练批次
            
        Returns:
            SVE统计信息
        """
        observations = batch['observations']
        true_values = batch.get('values', None)
        
        if true_values is None:
            return {}
            
        # 获取不同来源的价值估计
        with torch.no_grad():
            # 网络价值
            representations = self.model.represent(observations)
            _, v_net = self.model.predict(representations)
            v_net = v_net.squeeze(-1).cpu().numpy()
            
            # 搜索价值（批量执行搜索）
            v_search = self._batch_search_values(observations)
            
            # Bootstrap价值
            if 'bootstrap_values' in batch:
                v_bootstrap = batch['bootstrap_values'].cpu().numpy()
            else:
                v_bootstrap = v_net  # 退化到网络价值
                
        # 聚合价值
        aggregated_values = []
        for i in range(len(observations)):
            confidence = self._estimate_confidence(observations[i])
            agg_value = self.sve_aggregator.aggregate_values(
                v_net[i], v_search[i], v_bootstrap[i], confidence
            )
            aggregated_values.append(agg_value)
            
        aggregated_values = np.array(aggregated_values)
        
        # 计算改进
        true_values_np = true_values.cpu().numpy()
        net_error = np.mean(np.abs(v_net - true_values_np))
        sve_error = np.mean(np.abs(aggregated_values - true_values_np))
        improvement = (net_error - sve_error) / (net_error + 1e-8)
        
        # 更新聚合器
        self.sve_aggregator.update_weights_from_performance(sve_error)
        
        return {
            'sve_error': sve_error,
            'net_error': net_error,
            'sve_improvement': improvement,
            'avg_search_value': np.mean(v_search),
            'avg_aggregated_value': np.mean(aggregated_values)
        }
        
    def _batch_search_values(self, observations: torch.Tensor) -> np.ndarray:
        """批量计算搜索价值
        
        Args:
            observations: 观察批次
            
        Returns:
            搜索价值数组
        """
        search_values = []
        
        for obs in observations:
            # 执行轻量级搜索
            root = self.search_module.light_search(obs, num_simulations=50)
            search_value = root.value() if root else 0.0
            search_values.append(search_value)
            
        return np.array(search_values)
        
    def _estimate_confidence(self, observation: torch.Tensor) -> Dict[str, float]:
        """估计各价值源的置信度
        
        Args:
            observation: 观察
            
        Returns:
            置信度字典
        """
        # 简化实现：基于固定规则
        confidence = {
            'net': 0.7,      # 网络通常比较稳定
            'search': 0.8,   # 搜索通常更准确
            'bootstrap': 0.6 # Bootstrap可能有偏差
        }
        
        # 可以基于具体情况调整置信度
        # 例如：游戏早期搜索更准确，后期网络更准确
        
        return confidence
        
    def select_action(self, state: State, legal_actions: List[Action],
                     temperature: float = 1.0) -> Action:
        """选择动作（使用增强的搜索）
        
        Args:
            state: 当前状态
            legal_actions: 合法动作列表
            temperature: 温度参数
            
        Returns:
            选择的动作
        """
        # 使用增强的搜索模块
        # 从配置中获取num_simulations，支持多种配置格式
        num_simulations = getattr(self.config, 'num_simulations', None)
        if num_simulations is None:
            # 尝试从search_simulations获取
            num_simulations = getattr(self.config, 'search_simulations', 800)
        
        search_results = self.search_module.search(
            state, 
            legal_actions,
            num_simulations=num_simulations
        )
        
        # 基于搜索结果选择动作
        if search_results and isinstance(search_results, dict):
            # 搜索模块返回字典格式的结果
            action_probs = search_results.get('action_probs', None)
            if action_probs is None:
                # 如果没有action_probs，使用policy
                action_probs = search_results.get('policy', None)
            
            if action_probs is not None:
                # 只考虑合法动作的概率
                legal_action_probs = []
                for action in legal_actions:
                    if action < len(action_probs):
                        legal_action_probs.append(action_probs[action])
                    else:
                        legal_action_probs.append(0.0)
                
                legal_action_probs = np.array(legal_action_probs)
                
                # 确保有非零概率
                if legal_action_probs.sum() == 0:
                    legal_action_probs = np.ones(len(legal_actions)) / len(legal_actions)
                else:
                    legal_action_probs = legal_action_probs / legal_action_probs.sum()
                
                if temperature == 0:
                    # 贪心选择
                    action_idx = np.argmax(legal_action_probs)
                else:
                    # 基于概率选择
                    legal_action_probs = legal_action_probs ** (1 / temperature)
                    legal_action_probs /= legal_action_probs.sum()
                    action_idx = np.random.choice(len(legal_actions), p=legal_action_probs)
                    
                return legal_actions[action_idx]
            else:
                # 如果没有action_probs，随机选择
                return np.random.choice(legal_actions)
        else:
            # 如果搜索失败，随机选择
            return np.random.choice(legal_actions)
            
    def _update_training_stats(self, losses: Dict[str, Any]):
        """更新训练统计信息
        
        Args:
            losses: 损失字典（可能包含tensor和float）
        """
        # 辅助函数：安全转换为float
        def safe_to_float(value):
            try:
                if torch.is_tensor(value):
                    return value.detach().cpu().item()
                elif isinstance(value, (int, float)):
                    return float(value)
                else:
                    return 0.0
            except Exception:
                return 0.0
        
        if 'sve_error' in losses:
            self.training_stats['sve_values'].append(safe_to_float(losses['sve_error']))
        if 'ssl_loss' in losses:
            # 安全转换为float用于统计
            self.training_stats['ssl_losses'].append(safe_to_float(losses['ssl_loss']))
        if 'value_prefix_loss' in losses:
            # 安全转换为float用于统计
            self.training_stats['value_prefix_losses'].append(safe_to_float(losses['value_prefix_loss']))
        if 'sve_improvement' in losses:
            self.training_stats['search_improvements'].append(losses['sve_improvement'])
            
        # 限制历史长度
        max_history = 10000
        for key in self.training_stats:
            if len(self.training_stats[key]) > max_history:
                self.training_stats[key] = self.training_stats[key][-max_history:]
                
    def get_statistics(self) -> Dict[str, Any]:
        """获取算法统计信息
        
        Returns:
            统计信息字典
        """
        # 由于我们没有继承基类的get_statistics，直接返回V2的统计信息
        v2_stats = {
            'training_step': self.training_step,
            'action_space_size': self.action_space_size,
            'observation_shape': self.observation_shape
        }
        
        # 添加V2特有的统计信息
        try:
            v2_stats['sve_weights'] = self.sve_aggregator.get_current_weights()
        except:
            v2_stats['sve_weights'] = {}
            
        try:
            v2_stats['ssl_stats'] = self.ssl_module.get_statistics()
        except:
            v2_stats['ssl_stats'] = {}
            
        try:
            v2_stats['value_prefix_stats'] = self.value_prefix_predictor.get_statistics()
        except:
            v2_stats['value_prefix_stats'] = {}
        
        # 添加训练统计
        if self.training_stats['sve_values']:
            v2_stats['avg_sve_error'] = np.mean(self.training_stats['sve_values'][-100:])
        if self.training_stats['search_improvements']:
            v2_stats['avg_search_improvement'] = np.mean(
                self.training_stats['search_improvements'][-100:]
            )
            
        return v2_stats
        
    def save_checkpoint(self, path: str):
        """保存检查点
        
        Args:
            path: 保存路径
        """
        try:
            # 保存主要模型
            torch.save(self.model.state_dict(), path)
            
            # 保存V2特有组件
            v2_checkpoint = {
                'training_step': self.training_step,
                'training_stats': self.training_stats,
                'config': self.config.__dict__ if hasattr(self.config, '__dict__') else {}
            }
            
            # 尝试保存各组件的状态
            try:
                v2_checkpoint['ssl_module'] = self.ssl_module.state_dict() if hasattr(self.ssl_module, 'state_dict') else {}
            except:
                pass
                
            try:
                v2_checkpoint['value_prefix_predictor'] = self.value_prefix_predictor.state_dict()
            except:
                pass
                
            try:
                v2_checkpoint['sve_weights'] = self.sve_aggregator.get_current_weights()
            except:
                pass
            
            v2_path = path.replace('.pt', '_v2.pt')
            torch.save(v2_checkpoint, v2_path)
            logger.info(f"EfficientZero V2 检查点已保存: {path}, {v2_path}")
        except Exception as e:
            logger.error(f"保存检查点失败: {e}")
        
    def load_checkpoint(self, path: str):
        """加载检查点
        
        Args:
            path: 检查点路径
        """
        try:
            # 加载主要模型
            if os.path.exists(path):
                state_dict = torch.load(path, map_location=self.device)
                self.model.load_state_dict(state_dict)
                logger.info(f"主模型检查点已加载: {path}")
            
            # 加载V2特有组件
            v2_path = path.replace('.pt', '_v2.pt')
            if os.path.exists(v2_path):
                v2_checkpoint = torch.load(v2_path, map_location=self.device)
                
                self.training_step = v2_checkpoint.get('training_step', 0)
                self.training_stats = v2_checkpoint.get('training_stats', self.training_stats)
                
                # 尝试恢复各组件的状态
                try:
                    if 'ssl_module' in v2_checkpoint and hasattr(self.ssl_module, 'load_state_dict'):
                        self.ssl_module.load_state_dict(v2_checkpoint['ssl_module'])
                except Exception as e:
                    logger.warning(f"加载SSL模块失败: {e}")
                
                try:
                    if 'value_prefix_predictor' in v2_checkpoint:
                        self.value_prefix_predictor.load_state_dict(v2_checkpoint['value_prefix_predictor'])
                except Exception as e:
                    logger.warning(f"加载价值前缀预测器失败: {e}")
                
                try:
                    if 'sve_weights' in v2_checkpoint:
                        sve_weights = v2_checkpoint['sve_weights']
                        if hasattr(self.sve_aggregator, 'alpha_net'):
                            self.sve_aggregator.alpha_net = sve_weights.get('net', 0.7)
                            self.sve_aggregator.alpha_search = sve_weights.get('search', 0.8)
                            self.sve_aggregator.alpha_bootstrap = sve_weights.get('bootstrap', 0.6)
                except Exception as e:
                    logger.warning(f"加载SVE权重失败: {e}")
                
                logger.info(f"EfficientZero V2 检查点已加载: {v2_path}")
            else:
                logger.warning(f"未找到V2检查点: {v2_path}")
                
        except Exception as e:
            logger.error(f"加载检查点失败: {e}")
    
    def get_checkpoint_data(self) -> Dict[str, Any]:
        """
        获取算法的完整检查点数据
        
        Returns:
            包含所有需要保存的状态的字典
        """
        # 确保CUDA同步，避免保存时的竞态条件
        if torch.cuda.is_available():
            torch.cuda.synchronize()
        
        # 将config转换为可序列化的字典格式，避免保存整个对象
        config_dict = {}
        if hasattr(self.config, 'to_dict'):
            config_dict = self.config.to_dict()
        elif hasattr(self.config, '__dict__'):
            config_dict = {k: v for k, v in self.config.__dict__.items() 
                          if not k.startswith('_') and not callable(v)}
        else:
            # 如果无法转换，保存基本信息
            config_dict = {
                'observation_shape': getattr(self.config, 'observation_shape', (656,)),
                'action_space_size': getattr(self.config, 'action_space_size', 2300),
                'device': getattr(self.config, 'device', 'cuda'),
                'hidden_dim': getattr(self.config, 'hidden_dim', 512),
                'num_layers': getattr(self.config, 'num_layers', 4),
                'lr': getattr(self.config, 'lr', 0.0003),
                'batch_size': getattr(self.config, 'batch_size', 256),
                'enable_sve': getattr(self.config, 'enable_sve', True),
                'num_simulations': getattr(self.config, 'num_simulations', 800),
            }
        
        # 获取模型状态并确保所有张量都在CPU上
        model_state = {}
        if hasattr(self, 'model') and self.model is not None:
            try:
                # 获取模型状态字典
                raw_state = self.model.state_dict()
                # 将所有CUDA张量移到CPU
                for key, value in raw_state.items():
                    if isinstance(value, torch.Tensor):
                        model_state[key] = value.detach().cpu()
                    else:
                        model_state[key] = value
                        
                # 记录模型参数信息用于调试
                total_params = sum(p.numel() for p in self.model.parameters())
                logger.info(f"模型参数总数: {total_params:,}, 状态字典键数: {len(model_state)}")
            except Exception as e:
                logger.error(f"获取模型状态失败: {e}")
                model_state = {}
        else:
            logger.warning("模型未初始化或为None")
        
        checkpoint_data = {
            'model_state_dict': model_state,
            'training_step': self.training_step,
            'training_stats': self.training_stats,
            'config_dict': config_dict,  # 保存字典而不是对象
            'algorithm_name': 'efficient_zero_v2',
            'version': '2.0'
        }
        
        # 保存V2特有组件（同样移到CPU）
        if hasattr(self, 'ssl_module') and hasattr(self.ssl_module, 'state_dict'):
            ssl_state = {}
            for key, value in self.ssl_module.state_dict().items():
                if isinstance(value, torch.Tensor):
                    ssl_state[key] = value.detach().cpu()
                else:
                    ssl_state[key] = value
            checkpoint_data['ssl_module_state_dict'] = ssl_state
        
        if hasattr(self, 'value_prefix_predictor') and hasattr(self.value_prefix_predictor, 'state_dict'):
            vpp_state = {}
            for key, value in self.value_prefix_predictor.state_dict().items():
                if isinstance(value, torch.Tensor):
                    vpp_state[key] = value.detach().cpu()
                else:
                    vpp_state[key] = value
            checkpoint_data['value_prefix_predictor_state_dict'] = vpp_state
        
        if hasattr(self, 'value_aggregator'):
            checkpoint_data['value_aggregator_config'] = {
                'method': self.value_aggregator.method,
                'weights': self.value_aggregator.weights if hasattr(self.value_aggregator, 'weights') else None
            }
        
        # 添加优化器状态（如果存在）
        if hasattr(self, 'optimizer') and self.optimizer is not None:
            try:
                opt_state = {}
                for key, value in self.optimizer.state_dict().items():
                    if isinstance(value, dict):
                        opt_state[key] = {}
                        for k2, v2 in value.items():
                            if isinstance(v2, torch.Tensor):
                                opt_state[key][k2] = v2.detach().cpu()
                            else:
                                opt_state[key][k2] = v2
                    elif isinstance(value, torch.Tensor):
                        opt_state[key] = value.detach().cpu()
                    else:
                        opt_state[key] = value
                checkpoint_data['optimizer_state_dict'] = opt_state
            except Exception as e:
                logger.warning(f"保存优化器状态失败: {e}")
        
        return checkpoint_data
    
    def to(self, device):
        """移动模型到指定设备"""
        super().to(device)
        self.device = device
        if hasattr(self, 'model'):
            self.model.to(device)
        return self