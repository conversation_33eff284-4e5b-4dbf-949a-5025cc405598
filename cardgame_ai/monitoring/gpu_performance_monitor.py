"""
GPU性能和资源深度监控系统
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

专门针对WSL环境下的GPU性能监控，提供：
- 实时GPU利用率追踪
- 显存使用分析
- 温度和功率监控
- WSL vs Windows差异分析
- 性能瓶颈检测
- 智能性能优化建议

作者: Claude AI
版本: 2.0
日期: 2025-07-06
"""

import os
import time
import threading
import subprocess
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, field
from collections import deque
import statistics
import psutil

# GPU监控相关
try:
    import pynvml as nvml
    NVML_AVAILABLE = True
    nvml.nvmlInit()
except (ImportError, Exception) as e:
    NVML_AVAILABLE = False

from cardgame_ai.utils.logging import get_logger

logger = get_logger(__name__)


@dataclass
class GPUMetrics:
    """GPU性能指标"""
    timestamp: datetime
    utilization: float  # GPU利用率 (%)
    memory_used: float  # 显存使用 (MB)
    memory_total: float  # 总显存 (MB)
    memory_percent: float  # 显存使用率 (%)
    temperature: float  # 温度 (°C)
    power_draw: float  # 功耗 (W)
    power_limit: float  # 功耗限制 (W)
    clock_graphics: int  # 图形时钟 (MHz)
    clock_memory: int  # 显存时钟 (MHz)
    processes: List[Dict[str, Any]]  # GPU进程列表


@dataclass
class PerformanceAnalysis:
    """性能分析结果"""
    timestamp: datetime
    
    # 利用率分析
    avg_utilization: float
    peak_utilization: float
    low_util_percentage: float  # 低利用率时间占比
    
    # 显存分析
    avg_memory_usage: float
    peak_memory_usage: float
    memory_efficiency: float
    
    # 温度和功耗
    avg_temperature: float
    max_temperature: float
    avg_power: float
    max_power: float
    
    # 性能问题
    bottlenecks: List[str]
    recommendations: List[str]
    
    # WSL特定
    wsl_adaptation_active: bool
    estimated_real_utilization: float


class WSLGPUAdapter:
    """WSL环境GPU监控适配器"""
    
    def __init__(self):
        self.is_wsl = self._detect_wsl()
        self.wsl_utilization_factor = 2.5  # WSL显示利用率的修正因子
        self.windows_gpu_cmd = None
        
        if self.is_wsl:
            logger.info("🔧 检测到WSL环境，启用GPU监控适配")
            self._setup_windows_monitoring()
            
    def _detect_wsl(self) -> bool:
        """检测是否运行在WSL环境"""
        try:
            with open('/proc/version', 'r') as f:
                return 'microsoft' in f.read().lower() or 'wsl' in f.read().lower()
        except:
            return False
            
    def _setup_windows_monitoring(self):
        """设置Windows端GPU监控"""
        # 尝试设置Windows侧GPU监控命令
        possible_commands = [
            "powershell.exe -Command \"Get-Counter '\\GPU Engine(*)\\Utilization Percentage'\"",
            "wmic path win32_videocontroller get name,driverversion,pnpdeviceid",
            "nvidia-smi.exe"
        ]
        
        for cmd in possible_commands:
            try:
                result = subprocess.run(cmd.split()[0], capture_output=True, timeout=2)
                if result.returncode == 0:
                    self.windows_gpu_cmd = cmd
                    logger.info(f"✅ Windows GPU监控命令可用: {cmd.split()[0]}")
                    break
            except:
                continue
                
    def adapt_gpu_metrics(self, wsl_metrics: GPUMetrics) -> GPUMetrics:
        """适配WSL GPU指标"""
        if not self.is_wsl:
            return wsl_metrics
            
        # 修正WSL显示的GPU利用率
        estimated_real_util = min(wsl_metrics.utilization * self.wsl_utilization_factor, 100.0)
        
        # 创建修正后的指标
        adapted_metrics = GPUMetrics(
            timestamp=wsl_metrics.timestamp,
            utilization=estimated_real_util,
            memory_used=wsl_metrics.memory_used,
            memory_total=wsl_metrics.memory_total,
            memory_percent=wsl_metrics.memory_percent,
            temperature=wsl_metrics.temperature,
            power_draw=wsl_metrics.power_draw,
            power_limit=wsl_metrics.power_limit,
            clock_graphics=wsl_metrics.clock_graphics,
            clock_memory=wsl_metrics.clock_memory,
            processes=wsl_metrics.processes
        )
        
        return adapted_metrics
        
    def get_windows_gpu_info(self) -> Optional[Dict[str, Any]]:
        """获取Windows侧GPU信息"""
        if not self.is_wsl or not self.windows_gpu_cmd:
            return None
            
        try:
            if "nvidia-smi" in self.windows_gpu_cmd:
                result = subprocess.run(
                    ["nvidia-smi.exe", "--query-gpu=utilization.gpu,memory.used,memory.total,temperature.gpu,power.draw", 
                     "--format=csv,noheader,nounits"],
                    capture_output=True, text=True, timeout=5
                )
                
                if result.returncode == 0:
                    values = result.stdout.strip().split(', ')
                    if len(values) >= 5:
                        return {
                            "utilization": float(values[0]),
                            "memory_used": float(values[1]),
                            "memory_total": float(values[2]),
                            "temperature": float(values[3]),
                            "power_draw": float(values[4])
                        }
            return None
        except Exception as e:
            logger.debug(f"Windows GPU查询失败: {e}")
            return None


class GPUPerformanceMonitor:
    """GPU性能监控系统"""
    
    def __init__(self, sample_interval: float = 2.0, history_size: int = 1000):
        self.sample_interval = sample_interval
        self.history_size = history_size
        
        # 历史数据
        self.metrics_history: deque = deque(maxlen=history_size)
        self.analysis_history: deque = deque(maxlen=100)
        
        # WSL适配器
        self.wsl_adapter = WSLGPUAdapter()
        
        # 监控状态
        self.is_monitoring = False
        self.monitor_thread = None
        
        # 性能阈值
        self.thresholds = {
            "low_utilization": 30.0,  # WSL适配后的阈值
            "high_memory": 90.0,
            "high_temperature": 85.0,
            "low_efficiency": 50.0
        }
        
        # 锁
        self._lock = threading.Lock()
        
        logger.info("🚀 GPU性能监控系统初始化完成")
        
    def start_monitoring(self):
        """启动GPU监控"""
        if self.is_monitoring:
            logger.warning("GPU监控已在运行")
            return
            
        if not NVML_AVAILABLE:
            logger.error("❌ NVML不可用，无法启动GPU监控")
            return
            
        self.is_monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self.monitor_thread.start()
        
        logger.info("✅ GPU性能监控已启动")
        
    def stop_monitoring(self):
        """停止GPU监控"""
        self.is_monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        logger.info("🛑 GPU监控已停止")
        
    def _monitoring_loop(self):
        """监控循环"""
        while self.is_monitoring:
            try:
                metrics = self._collect_gpu_metrics()
                if metrics:
                    # WSL适配
                    adapted_metrics = self.wsl_adapter.adapt_gpu_metrics(metrics)
                    
                    with self._lock:
                        self.metrics_history.append(adapted_metrics)
                    
                    # 记录监控日志
                    self._log_gpu_status(adapted_metrics)
                    
                    # 性能分析（每分钟一次）
                    if len(self.metrics_history) % 30 == 0:  # 30 * 2s = 60s
                        self._perform_analysis()
                        
                time.sleep(self.sample_interval)
                
            except Exception as e:
                logger.error(f"GPU监控异常: {e}")
                time.sleep(self.sample_interval)
                
    def _collect_gpu_metrics(self) -> Optional[GPUMetrics]:
        """收集GPU指标"""
        try:
            # 获取设备句柄
            device_count = nvml.nvmlDeviceGetCount()
            if device_count == 0:
                return None
                
            handle = nvml.nvmlDeviceGetHandleByIndex(0)  # 使用第一个GPU
            
            # 收集基础指标
            util = nvml.nvmlDeviceGetUtilizationRates(handle)
            memory_info = nvml.nvmlDeviceGetMemoryInfo(handle)
            temperature = nvml.nvmlDeviceGetTemperature(handle, nvml.NVML_TEMPERATURE_GPU)
            
            # 功耗信息
            try:
                power_draw = nvml.nvmlDeviceGetPowerUsage(handle) / 1000.0  # mW to W
                power_limit = nvml.nvmlDeviceGetPowerManagementLimitConstraints(handle)[1] / 1000.0
            except:
                power_draw = 0.0
                power_limit = 0.0
                
            # 时钟频率
            try:
                clock_graphics = nvml.nvmlDeviceGetClockInfo(handle, nvml.NVML_CLOCK_GRAPHICS)
                clock_memory = nvml.nvmlDeviceGetClockInfo(handle, nvml.NVML_CLOCK_MEM)
            except:
                clock_graphics = 0
                clock_memory = 0
                
            # GPU进程
            try:
                processes_info = nvml.nvmlDeviceGetComputeRunningProcesses(handle)
                processes = []
                for proc in processes_info:
                    try:
                        proc_info = {
                            "pid": proc.pid,
                            "memory": proc.usedGpuMemory / 1024 / 1024,  # Bytes to MB
                            "name": psutil.Process(proc.pid).name() if proc.pid else "Unknown"
                        }
                        processes.append(proc_info)
                    except:
                        continue
            except:
                processes = []
                
            return GPUMetrics(
                timestamp=datetime.now(),
                utilization=float(util.gpu),
                memory_used=memory_info.used / 1024 / 1024,  # Bytes to MB
                memory_total=memory_info.total / 1024 / 1024,
                memory_percent=memory_info.used / memory_info.total * 100,
                temperature=float(temperature),
                power_draw=power_draw,
                power_limit=power_limit,
                clock_graphics=clock_graphics,
                clock_memory=clock_memory,
                processes=processes
            )
            
        except Exception as e:
            logger.error(f"收集GPU指标失败: {e}")
            return None
            
    def _log_gpu_status(self, metrics: GPUMetrics):
        """记录GPU状态日志"""
        # 检查WSL适配
        wsl_note = " [WSL环境适配]" if self.wsl_adapter.is_wsl else ""
        
        logger.info(
            f"GPU状态 - 利用率: {metrics.utilization:.0f}%, "
            f"显存: {metrics.memory_percent:.1f}%, "
            f"温度: {metrics.temperature:.0f}°C, "
            f"功率: {metrics.power_draw:.1f}W{wsl_note}"
        )
        
        # 检查性能警告
        if metrics.utilization < self.thresholds["low_utilization"]:
            logger.warning(
                f"⚠️ GPU利用率较低: {metrics.utilization:.0f}% "
                f"(目标: >{self.thresholds['low_utilization']:.0f}%){wsl_note}"
            )
            
        if metrics.memory_percent > self.thresholds["high_memory"]:
            logger.warning(
                f"⚠️ GPU显存使用过高: {metrics.memory_percent:.1f}% "
                f"({metrics.memory_used:.0f}MB / {metrics.memory_total:.0f}MB)"
            )
            
        if metrics.temperature > self.thresholds["high_temperature"]:
            logger.warning(
                f"⚠️ GPU温度过高: {metrics.temperature:.0f}°C"
            )
            
    def _perform_analysis(self) -> PerformanceAnalysis:
        """执行性能分析"""
        with self._lock:
            if len(self.metrics_history) < 10:
                return None
                
            recent_metrics = list(self.metrics_history)[-60:]  # 最近2分钟数据
            
        # 计算统计指标
        utilizations = [m.utilization for m in recent_metrics]
        memory_usages = [m.memory_percent for m in recent_metrics]
        temperatures = [m.temperature for m in recent_metrics]
        powers = [m.power_draw for m in recent_metrics]
        
        avg_util = statistics.mean(utilizations)
        peak_util = max(utilizations)
        low_util_count = sum(1 for u in utilizations if u < self.thresholds["low_utilization"])
        low_util_percentage = low_util_count / len(utilizations) * 100
        
        avg_memory = statistics.mean(memory_usages)
        peak_memory = max(memory_usages)
        
        avg_temp = statistics.mean(temperatures)
        max_temp = max(temperatures)
        
        avg_power = statistics.mean(powers)
        max_power = max(powers)
        
        # 性能效率评估
        memory_efficiency = 100 - abs(avg_memory - 85)  # 85%为最佳显存使用率
        memory_efficiency = max(0, min(100, memory_efficiency))
        
        # 瓶颈检测
        bottlenecks = []
        recommendations = []
        
        if avg_util < 50:
            bottlenecks.append("GPU利用率不足")
            recommendations.append("增加批次大小或并行度")
            
        if low_util_percentage > 30:
            bottlenecks.append("GPU利用率波动大")
            recommendations.append("检查数据加载管道，避免GPU空等")
            
        if avg_memory < 30:
            bottlenecks.append("显存使用不足")
            recommendations.append("增加模型大小或批次大小")
        elif avg_memory > 95:
            bottlenecks.append("显存使用过高")
            recommendations.append("减少批次大小或启用梯度检查点")
            
        if max_temp > 80:
            bottlenecks.append("温度过高")
            recommendations.append("检查散热系统，考虑降低功耗限制")
            
        # WSL特定分析
        wsl_adaptation = self.wsl_adapter.is_wsl
        estimated_real_util = avg_util if not wsl_adaptation else avg_util / self.wsl_adapter.wsl_utilization_factor
        
        analysis = PerformanceAnalysis(
            timestamp=datetime.now(),
            avg_utilization=avg_util,
            peak_utilization=peak_util,
            low_util_percentage=low_util_percentage,
            avg_memory_usage=avg_memory,
            peak_memory_usage=peak_memory,
            memory_efficiency=memory_efficiency,
            avg_temperature=avg_temp,
            max_temperature=max_temp,
            avg_power=avg_power,
            max_power=max_power,
            bottlenecks=bottlenecks,
            recommendations=recommendations,
            wsl_adaptation_active=wsl_adaptation,
            estimated_real_utilization=estimated_real_util
        )
        
        with self._lock:
            self.analysis_history.append(analysis)
            
        # 记录分析结果
        self._log_performance_analysis(analysis)
        
        return analysis
        
    def _log_performance_analysis(self, analysis: PerformanceAnalysis):
        """记录性能分析日志"""
        logger.info("📊 GPU性能分析报告:")
        logger.info(f"   平均利用率: {analysis.avg_utilization:.1f}% (峰值: {analysis.peak_utilization:.1f}%)")
        logger.info(f"   平均显存: {analysis.avg_memory_usage:.1f}% (峰值: {analysis.peak_memory_usage:.1f}%)")
        logger.info(f"   平均温度: {analysis.avg_temperature:.1f}°C (最高: {analysis.max_temperature:.1f}°C)")
        logger.info(f"   显存效率: {analysis.memory_efficiency:.1f}%")
        
        if analysis.wsl_adaptation_active:
            logger.info(f"   WSL修正: 估计真实利用率 {analysis.estimated_real_utilization:.1f}%")
            
        if analysis.bottlenecks:
            logger.warning(f"   🚨 性能瓶颈: {', '.join(analysis.bottlenecks)}")
            
        if analysis.recommendations:
            logger.info(f"   💡 优化建议: {'; '.join(analysis.recommendations)}")
            
    def get_current_metrics(self) -> Optional[GPUMetrics]:
        """获取当前GPU指标"""
        with self._lock:
            return self.metrics_history[-1] if self.metrics_history else None
            
    def get_performance_summary(self, hours: int = 1) -> Dict[str, Any]:
        """获取性能摘要"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        with self._lock:
            recent_metrics = [m for m in self.metrics_history if m.timestamp >= cutoff_time]
            recent_analyses = [a for a in self.analysis_history if a.timestamp >= cutoff_time]
            
        if not recent_metrics:
            return {"error": "没有足够的数据"}
            
        utilizations = [m.utilization for m in recent_metrics]
        
        summary = {
            "time_range_hours": hours,
            "sample_count": len(recent_metrics),
            "utilization": {
                "current": recent_metrics[-1].utilization,
                "average": statistics.mean(utilizations),
                "min": min(utilizations),
                "max": max(utilizations),
                "std": statistics.stdev(utilizations) if len(utilizations) > 1 else 0
            },
            "memory": {
                "current_mb": recent_metrics[-1].memory_used,
                "current_percent": recent_metrics[-1].memory_percent,
                "total_mb": recent_metrics[-1].memory_total
            },
            "temperature": {
                "current": recent_metrics[-1].temperature,
                "average": statistics.mean([m.temperature for m in recent_metrics]),
                "max": max([m.temperature for m in recent_metrics])
            },
            "power": {
                "current": recent_metrics[-1].power_draw,
                "average": statistics.mean([m.power_draw for m in recent_metrics]),
                "max": max([m.power_draw for m in recent_metrics])
            },
            "wsl_info": {
                "is_wsl": self.wsl_adapter.is_wsl,
                "adaptation_factor": self.wsl_adapter.wsl_utilization_factor if self.wsl_adapter.is_wsl else None
            }
        }
        
        if recent_analyses:
            latest_analysis = recent_analyses[-1]
            summary["latest_analysis"] = {
                "bottlenecks": latest_analysis.bottlenecks,
                "recommendations": latest_analysis.recommendations,
                "memory_efficiency": latest_analysis.memory_efficiency
            }
            
        return summary


# 全局实例
_gpu_monitor: Optional[GPUPerformanceMonitor] = None
_monitor_lock = threading.Lock()


def get_gpu_monitor() -> GPUPerformanceMonitor:
    """获取GPU监控器单例"""
    global _gpu_monitor
    
    if _gpu_monitor is None:
        with _monitor_lock:
            if _gpu_monitor is None:
                _gpu_monitor = GPUPerformanceMonitor()
                
    return _gpu_monitor


def start_gpu_monitoring() -> GPUPerformanceMonitor:
    """启动GPU性能监控"""
    monitor = get_gpu_monitor()
    monitor.start_monitoring()
    return monitor


if __name__ == "__main__":
    # 测试运行
    print("🚀 启动GPU性能监控测试...")
    monitor = start_gpu_monitoring()
    
    try:
        time.sleep(30)  # 运行30秒
        summary = monitor.get_performance_summary()
        print("\n📊 GPU性能摘要:")
        print(json.dumps(summary, indent=2, ensure_ascii=False))
        
    except KeyboardInterrupt:
        print("\n🛑 停止测试")
    finally:
        monitor.stop_monitoring()