"""
统一配置加载器
提供统一的配置加载接口，支持多种格式和验证
"""

import os
import json
import yaml
from pathlib import Path
from typing import Dict, Any, Optional, Type, TypeVar, Union
from .base import BaseConfig, ConfigError, ConfigLoadError, ConfigValidationError
from .algorithm import AlgorithmConfig
from .training import TrainingConfig
from .mcts import MCTSConfig
from .environment import EnvironmentConfig
from .hardware import HardwareConfig
from .logging import LoggingConfig
from .phase4 import Phase4Config

T = TypeVar('T', bound=BaseConfig)

class ConfigLoader:
    """统一的配置加载器"""
    
    # 配置类映射
    CONFIG_CLASSES = {
        'algorithm': AlgorithmConfig,
        'training': TrainingConfig,
        'mcts': MCTSConfig,
        'environment': EnvironmentConfig,
        'hardware': HardwareConfig,
        'logging': LoggingConfig,
        'phase4': Phase4Config,
    }
    
    def __init__(self, config_path: Optional[Union[str, Path]] = None):
        """
        初始化配置加载器
        
        Args:
            config_path: 配置文件路径
        """
        self.config_path = Path(config_path) if config_path else None
        self._config_cache: Dict[str, BaseConfig] = {}
        self._raw_config: Dict[str, Any] = {}
        
        if self.config_path and self.config_path.exists():
            self.load()
            
    def load(self, path: Optional[Union[str, Path]] = None) -> Dict[str, BaseConfig]:
        """
        加载配置文件
        
        Args:
            path: 配置文件路径
            
        Returns:
            配置字典
        """
        config_path = Path(path) if path else self.config_path
        
        if not config_path:
            raise ConfigLoadError("No config path provided")
            
        if not config_path.exists():
            raise ConfigLoadError(f"Config file not found: {config_path}")
            
        # 加载原始配置
        with open(config_path, 'r', encoding='utf-8') as f:
            if config_path.suffix in ['.yaml', '.yml']:
                self._raw_config = yaml.safe_load(f)
            elif config_path.suffix == '.json':
                self._raw_config = json.load(f)
            else:
                raise ConfigLoadError(f"Unsupported file format: {config_path.suffix}")
                
        # 解析配置
        self._parse_config()
        
        # 验证配置
        self._validate_all()
        
        return self._config_cache
        
    def _parse_config(self):
        """解析配置"""
        for key, config_class in self.CONFIG_CLASSES.items():
            if key in self._raw_config:
                try:
                    config_data = self._raw_config[key]
                    config_instance = config_class.from_dict(config_data)
                    self._config_cache[key] = config_instance
                except Exception as e:
                    raise ConfigLoadError(f"Failed to parse {key} config: {e}")
                    
    def _validate_all(self):
        """验证所有配置"""
        for key, config in self._config_cache.items():
            try:
                config.validate()
            except Exception as e:
                raise ConfigValidationError(f"Validation failed for {key}: {e}")
                
    def get(self, key: str, default: Optional[Any] = None) -> Optional[BaseConfig]:
        """
        获取配置
        
        Args:
            key: 配置键
            default: 默认值
            
        Returns:
            配置实例
        """
        return self._config_cache.get(key, default)
        
    def get_algorithm_config(self) -> AlgorithmConfig:
        """获取算法配置"""
        config = self.get('algorithm')
        if not config:
            config = AlgorithmConfig()
            self._config_cache['algorithm'] = config
        return config
        
    def get_training_config(self) -> TrainingConfig:
        """获取训练配置"""
        config = self.get('training')
        if not config:
            config = TrainingConfig()
            self._config_cache['training'] = config
        return config
        
    def get_mcts_config(self) -> MCTSConfig:
        """获取MCTS配置"""
        config = self.get('mcts')
        if not config:
            config = MCTSConfig()
            self._config_cache['mcts'] = config
        return config
        
    def get_environment_config(self) -> EnvironmentConfig:
        """获取环境配置"""
        config = self.get('environment')
        if not config:
            config = EnvironmentConfig()
            self._config_cache['environment'] = config
        return config
        
    def get_hardware_config(self) -> HardwareConfig:
        """获取硬件配置"""
        config = self.get('hardware')
        if not config:
            config = HardwareConfig()
            self._config_cache['hardware'] = config
        return config
        
    def get_logging_config(self) -> LoggingConfig:
        """获取日志配置"""
        config = self.get('logging')
        if not config:
            config = LoggingConfig()
            self._config_cache['logging'] = config
        return config
        
    def get_phase4_config(self) -> Phase4Config:
        """获取Phase 4配置"""
        config = self.get('phase4')
        if not config:
            config = Phase4Config()
            self._config_cache['phase4'] = config
        return config
        
    def save(self, path: Optional[Union[str, Path]] = None, format: str = 'yaml'):
        """
        保存配置
        
        Args:
            path: 保存路径
            format: 保存格式
        """
        save_path = Path(path) if path else self.config_path
        
        if not save_path:
            raise ConfigError("No save path provided")
            
        # 构建配置字典
        config_dict = {}
        for key, config in self._config_cache.items():
            config_dict[key] = config.to_dict()
            
        # 保存文件
        with open(save_path, 'w', encoding='utf-8') as f:
            if format == 'yaml':
                yaml.dump(config_dict, f, default_flow_style=False, allow_unicode=True)
            elif format == 'json':
                json.dump(config_dict, f, indent=2, ensure_ascii=False)
            else:
                raise ConfigError(f"Unsupported save format: {format}")
                
    def validate_no_auto_params(self) -> bool:
        """验证没有自动参数"""
        def check_value(value: Any, path: str = "") -> bool:
            if isinstance(value, str) and value.lower() == "auto":
                raise ConfigValidationError(f"Found 'auto' parameter at: {path}")
            elif isinstance(value, dict):
                for k, v in value.items():
                    check_value(v, f"{path}.{k}" if path else k)
            elif isinstance(value, list):
                for i, v in enumerate(value):
                    check_value(v, f"{path}[{i}]")
            return True
            
        # 检查原始配置
        check_value(self._raw_config)
        
        return True

# 全局配置加载器实例
_global_config_loader: Optional[ConfigLoader] = None

def get_config_loader(config_path: Optional[Union[str, Path]] = None) -> ConfigLoader:
    """
    获取全局配置加载器
    
    Args:
        config_path: 配置文件路径
        
    Returns:
        配置加载器实例
    """
    global _global_config_loader
    
    if _global_config_loader is None:
        # 默认配置路径
        if config_path is None:
            config_path = Path(__file__).parent / "defaults" / "optimized_fixed_config.yaml"
            
        _global_config_loader = ConfigLoader(config_path)
        
    return _global_config_loader

# 便捷函数
def load_config(path: Union[str, Path]) -> ConfigLoader:
    """加载配置文件"""
    return ConfigLoader(path)

def get_algorithm_config() -> AlgorithmConfig:
    """获取算法配置"""
    return get_config_loader().get_algorithm_config()

def get_training_config() -> TrainingConfig:
    """获取训练配置"""
    return get_config_loader().get_training_config()

def get_mcts_config() -> MCTSConfig:
    """获取MCTS配置"""
    return get_config_loader().get_mcts_config()

def get_environment_config() -> EnvironmentConfig:
    """获取环境配置"""
    return get_config_loader().get_environment_config()

def get_hardware_config() -> HardwareConfig:
    """获取硬件配置"""
    return get_config_loader().get_hardware_config()

def get_logging_config() -> LoggingConfig:
    """获取日志配置"""
    return get_config_loader().get_logging_config()

def get_phase4_config() -> Phase4Config:
    """获取Phase 4配置"""
    return get_config_loader().get_phase4_config()
