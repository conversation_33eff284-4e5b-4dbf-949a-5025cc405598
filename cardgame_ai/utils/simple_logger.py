"""
简单日志器模块 - 核心日志功能实现

提供统一的日志接口，集成5层日志架构、异步处理、GPU监控等功能。
这是解决日志系统依赖链断裂问题的核心模块。

Author: <PERSON> Code Assistant
Date: 2025-07-06
"""

import os
import sys
import time
import threading
import logging
from pathlib import Path
from typing import Dict, Any, Optional, Union, List
from datetime import datetime
import json
import traceback

# 导入现有的日志组件
try:
    from cardgame_ai.utils.logging.enhanced_async_logger import EnhancedAsyncLogger, get_async_logger, initialize_async_logger
    from cardgame_ai.utils.logging.log_router import LogRouter, get_log_router, initialize_log_router
    from cardgame_ai.utils.logging.structured_logger import StructuredLogger, get_structured_logger, LogLevel
    from cardgame_ai.utils.logging.logger_adapter import LoggerAdapter as BaseLoggerAdapter
except ImportError as e:
    # 如果导入失败，创建基础实现
    print(f"Warning: Failed to import logging components: {e}")
    
    class EnhancedAsyncLogger:
        def __init__(self, **kwargs):
            pass
        def log(self, level, message, category="main", **kwargs):
            print(f"[{level}] [{category}] {message}")
        def start(self):
            pass
        def stop(self):
            pass
        def get_stats(self):
            return {}
    
    class LogRouter:
        def __init__(self, **kwargs):
            pass
        def route(self, level, message, category="main", **kwargs):
            pass
    
    class StructuredLogger:
        def __init__(self, **kwargs):
            pass
        def log(self, level, message, **kwargs):
            pass
    
    class BaseLoggerAdapter:
        def __init__(self, logger, category="main"):
            self.logger = logger
            self.category = category
        def debug(self, message, **kwargs):
            print(f"[DEBUG] [{self.category}] {message}")
        def info(self, message, **kwargs):
            print(f"[INFO] [{self.category}] {message}")
        def warning(self, message, **kwargs):
            print(f"[WARNING] [{self.category}] {message}")
        def error(self, message, **kwargs):
            print(f"[ERROR] [{self.category}] {message}")
        def critical(self, message, **kwargs):
            print(f"[CRITICAL] [{self.category}] {message}")

    # 创建空函数以保持兼容性
    def get_async_logger():
        return EnhancedAsyncLogger()
    def initialize_async_logger(**kwargs):
        return EnhancedAsyncLogger(**kwargs)
    def get_log_router():
        return LogRouter()
    def initialize_log_router(**kwargs):
        return LogRouter(**kwargs)
    def get_structured_logger():
        return StructuredLogger()

# GPU监控支持
try:
    import torch
    import psutil
    GPU_AVAILABLE = torch.cuda.is_available() if torch else False
except ImportError:
    GPU_AVAILABLE = False

# 导入健康检查器
try:
    from cardgame_ai.utils.log_health_checker import LogHealthChecker, create_log_health_checker
except ImportError:
    LogHealthChecker = None
    create_log_health_checker = None


class ConfigurableLogger:
    """
    可配置的日志器类
    
    支持多级别日志配置、异步写入机制、GPU监控集成等功能。
    与现有的5层日志架构完全兼容。
    """
    
    def __init__(
        self,
        base_dir: str = "logs",
        buffer_size: int = 10000,
        batch_size: int = 100,
        flush_interval: float = 1.0,
        max_file_size: int = 500 * 1024 * 1024,
        enable_gpu_monitoring: bool = True,
        enable_async: bool = True,
        console_output: bool = True,
        level: str = "INFO"
    ):
        """
        初始化可配置日志器
        
        Args:
            base_dir: 日志基础目录
            buffer_size: 缓冲队列大小  
            batch_size: 批量写入大小
            flush_interval: 刷新间隔（秒）
            max_file_size: 单个日志文件最大大小
            enable_gpu_monitoring: 启用GPU监控
            enable_async: 启用异步写入
            console_output: 启用控制台输出
            level: 日志级别
        """
        self.base_dir = Path(base_dir)
        self.buffer_size = buffer_size
        self.batch_size = batch_size
        self.flush_interval = flush_interval
        self.max_file_size = max_file_size
        self.enable_gpu_monitoring = enable_gpu_monitoring and GPU_AVAILABLE
        self.enable_async = enable_async
        self.console_output = console_output
        self.level = level.upper()
        
        # 确保目录存在
        self.base_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建5层架构目录
        self._setup_directories()
        
        # 初始化日志组件
        self._async_logger = None
        self._log_router = None
        self._structured_logger = None
        self._console_logger = None
        
        # 启动标志
        self._initialized = False
        self._lock = threading.Lock()
        
        # GPU监控数据
        self._gpu_stats = {}
        
        # 统计信息
        self._stats = {
            'total_logs': 0,
            'debug_logs': 0,
            'info_logs': 0,
            'warning_logs': 0,
            'error_logs': 0,
            'critical_logs': 0,
            'game_logs': 0,
            'training_logs': 0,
            'summary_logs': 0
        }
        
        # 健康检查器
        self._health_checker = None
        self._enable_health_check = True
    
    def _setup_directories(self):
        """设置5层日志架构目录"""
        directories = [
            "debug",      # DEBUG层 - 详细调试信息
            "training",   # TRAINING层 - 训练相关日志  
            "game",       # GAME层 - 游戏状态记录
            "summary",    # SUMMARY层 - 统计摘要
            "games"       # GAMES目录 - 详细游戏文件
        ]
        
        for dir_name in directories:
            dir_path = self.base_dir / dir_name
            dir_path.mkdir(parents=True, exist_ok=True)
    
    def initialize(self):
        """初始化日志系统"""
        if self._initialized:
            return
        
        with self._lock:
            if self._initialized:
                return
            
            try:
                # 初始化异步日志器
                if self.enable_async:
                    self._async_logger = initialize_async_logger(
                        base_dir=str(self.base_dir),
                        buffer_size=self.buffer_size,
                        batch_size=self.batch_size,
                        flush_interval=self.flush_interval,
                        max_file_size=self.max_file_size
                    )
                
                # 初始化日志路由器
                self._log_router = initialize_log_router(
                    base_dir=str(self.base_dir),
                    filename_template='daily'
                )
                
                # 初始化结构化日志器
                self._structured_logger = get_structured_logger()
                
                # 设置控制台日志
                if self.console_output:
                    self._setup_console_logger()
                
                self._initialized = True
                
                # 记录初始化成功
                self.info("ConfigurableLogger initialized successfully", category="system")
                
                # 如果启用了GPU监控，记录GPU信息
                if self.enable_gpu_monitoring:
                    self._log_gpu_info()
                
                # 初始化健康检查器
                if self._enable_health_check:
                    self._initialize_health_checker()
                
            except Exception as e:
                # 初始化失败时提供后备方案
                print(f"Warning: Logger initialization failed: {e}")
                print("Falling back to basic logging")
                self._setup_fallback_logging()
                self._initialized = True
    
    def _setup_console_logger(self):
        """设置控制台日志器"""
        self._console_logger = logging.getLogger('console')
        self._console_logger.setLevel(getattr(logging, self.level, logging.INFO))
        
        # 避免重复添加处理器
        if not self._console_logger.handlers:
            handler = logging.StreamHandler(sys.stdout)
            formatter = logging.Formatter(
                '[%(asctime)s] [%(levelname)s] [%(name)s] %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            )
            handler.setFormatter(formatter)
            self._console_logger.addHandler(handler)
    
    def _setup_fallback_logging(self):
        """设置后备日志方案"""
        # 创建简单的文件日志器
        log_file = self.base_dir / "fallback.log"
        self._fallback_handler = logging.FileHandler(str(log_file), encoding='utf-8')
        formatter = logging.Formatter(
            '[%(asctime)s] [%(levelname)s] [%(name)s] %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        self._fallback_handler.setFormatter(formatter)
        
        # 配置根日志器
        root_logger = logging.getLogger()
        root_logger.setLevel(getattr(logging, self.level, logging.INFO))
        if self._fallback_handler not in root_logger.handlers:
            root_logger.addHandler(self._fallback_handler)
    
    def _log_gpu_info(self):
        """记录GPU信息"""
        if not self.enable_gpu_monitoring:
            return
        
        try:
            if torch.cuda.is_available():
                gpu_count = torch.cuda.device_count()
                for i in range(gpu_count):
                    gpu_name = torch.cuda.get_device_name(i)
                    gpu_memory = torch.cuda.get_device_properties(i).total_memory / 1024**3
                    self.info(f"GPU {i}: {gpu_name}, Memory: {gpu_memory:.1f}GB", category="system")
            
            # 记录CPU信息
            cpu_count = psutil.cpu_count()
            memory_total = psutil.virtual_memory().total / 1024**3
            self.info(f"CPU Cores: {cpu_count}, Memory: {memory_total:.1f}GB", category="system")
            
        except Exception as e:
            self.warning(f"Failed to get hardware info: {e}", category="system")
    
    def _get_gpu_stats(self) -> Dict[str, Any]:
        """获取GPU统计信息"""
        if not self.enable_gpu_monitoring:
            return {}
        
        try:
            stats = {}
            if torch.cuda.is_available():
                device_count = torch.cuda.device_count()
                for i in range(device_count):
                    memory_allocated = torch.cuda.memory_allocated(i) / 1024**3
                    memory_reserved = torch.cuda.memory_reserved(i) / 1024**3
                    memory_total = torch.cuda.get_device_properties(i).total_memory / 1024**3
                    
                    stats[f'gpu_{i}'] = {
                        'memory_allocated_gb': round(memory_allocated, 2),
                        'memory_reserved_gb': round(memory_reserved, 2),
                        'memory_total_gb': round(memory_total, 2),
                        'memory_usage_percent': round((memory_allocated / memory_total) * 100, 2)
                    }
            
            # 添加CPU和内存使用情况
            stats['cpu_percent'] = psutil.cpu_percent()
            memory = psutil.virtual_memory()
            stats['memory_percent'] = memory.percent
            stats['memory_available_gb'] = round(memory.available / 1024**3, 2)
            
            return stats
        except Exception:
            return {}
    
    def log(
        self,
        level: str,
        message: str,
        category: str = "main",
        include_gpu_stats: bool = False,
        **kwargs
    ):
        """
        通用日志记录方法
        
        Args:
            level: 日志级别
            message: 日志消息
            category: 日志类别 (debug/training/game/summary等)
            include_gpu_stats: 是否包含GPU统计信息
            **kwargs: 额外数据
        """
        if not self._initialized:
            self.initialize()
        
        level = level.upper()
        
        # 更新统计信息
        self._stats['total_logs'] += 1
        if level == 'DEBUG':
            self._stats['debug_logs'] += 1
        elif level == 'INFO':
            self._stats['info_logs'] += 1
        elif level == 'WARNING':
            self._stats['warning_logs'] += 1
        elif level == 'ERROR':
            self._stats['error_logs'] += 1
        elif level == 'CRITICAL':
            self._stats['critical_logs'] += 1
        
        # 根据类别更新统计
        if category == 'game':
            self._stats['game_logs'] += 1
        elif category == 'training':
            self._stats['training_logs'] += 1
        elif category == 'summary':
            self._stats['summary_logs'] += 1
        
        # 添加GPU统计信息（如果需要）
        if include_gpu_stats and self.enable_gpu_monitoring:
            kwargs.update(self._get_gpu_stats())
        
        # 添加时间戳和其他元数据
        kwargs.update({
            'timestamp': time.time(),
            'thread_id': threading.get_ident(),
            'process_id': os.getpid()
        })
        
        try:
            # 使用异步日志器（如果可用）
            if self._async_logger:
                self._async_logger.log(level, message, category, **kwargs)
            
            # 使用日志路由器
            if self._log_router:
                self._log_router.route(level, message, category, **kwargs)
            
            # 控制台输出
            if self.console_output and self._console_logger:
                self._console_logger.log(
                    getattr(logging, level, logging.INFO),
                    f"[{category}] {message}"
                )
            
        except Exception as e:
            # 后备日志方案
            try:
                fallback_message = f"[{level}] [{category}] {message}"
                if hasattr(self, '_fallback_handler'):
                    logging.getLogger().log(
                        getattr(logging, level, logging.INFO),
                        fallback_message
                    )
                else:
                    print(f"{datetime.now().isoformat()} {fallback_message}")
            except:
                print(f"CRITICAL: Failed to log message: {message}")
    
    def debug(self, message: str, category: str = "debug", **kwargs):
        """记录DEBUG级别日志"""
        self.log("DEBUG", message, category, **kwargs)
    
    def info(self, message: str, category: str = "main", **kwargs):
        """记录INFO级别日志"""
        self.log("INFO", message, category, **kwargs)
    
    def warning(self, message: str, category: str = "main", **kwargs):
        """记录WARNING级别日志"""
        self.log("WARNING", message, category, **kwargs)
    
    def error(self, message: str, category: str = "main", **kwargs):
        """记录ERROR级别日志"""
        self.log("ERROR", message, category, **kwargs)
    
    def critical(self, message: str, category: str = "main", **kwargs):
        """记录CRITICAL级别日志"""
        self.log("CRITICAL", message, category, **kwargs)
    
    def log_training_step(
        self,
        step: int,
        loss: float,
        reward: float = None,
        **kwargs
    ):
        """记录训练步骤"""
        data = {
            'step': step,
            'loss': loss,
            'reward': reward,
            **kwargs
        }
        self.info(f"Training step {step}: loss={loss:.6f}", 
                 category="training", 
                 include_gpu_stats=True,
                 **data)
    
    def log_game_action(
        self,
        player_id: int,
        action: str,
        game_state: Dict[str, Any] = None,
        **kwargs
    ):
        """记录游戏动作"""
        # 从kwargs中提取category，避免重复
        category = kwargs.pop('category', 'game')
        data = {
            'player_id': player_id,
            'action': action,
            'game_state': game_state,
            **kwargs
        }
        self.info(f"Player {player_id} action: {action}", 
                 category=category, 
                 **data)
    
    def log_metric(
        self,
        metric_name: str,
        value: Union[float, int],
        step: int = None,
        **kwargs
    ):
        """记录指标数据"""
        data = {
            'metric_name': metric_name,
            'value': value,
            'step': step,
            **kwargs
        }
        self.info(f"Metric {metric_name}: {value}", 
                 category="training", 
                 **data)
    
    def log_game_summary(
        self,
        game_id: str,
        winner: int,
        scores: List[int],
        duration: float,
        **kwargs
    ):
        """记录游戏摘要"""
        data = {
            'game_id': game_id,
            'winner': winner,
            'scores': scores,
            'duration': duration,
            **kwargs
        }
        self.info(f"Game {game_id} finished: winner={winner}, duration={duration:.2f}s", 
                 category="summary", 
                 **data)
    
    def log_detailed_game_state(
        self,
        game_id: str,
        step: int,
        current_player: int,
        game_phase: str,
        player_cards: Dict[int, List[str]],
        last_action: Dict[str, Any] = None,
        game_state: Dict[str, Any] = None,
        **kwargs
    ):
        """
        记录详细的游戏状态（GAME层）
        
        Args:
            game_id: 游戏ID
            step: 游戏步数
            current_player: 当前玩家
            game_phase: 游戏阶段（bidding/playing/finished）
            player_cards: 玩家手牌
            last_action: 最后执行的动作
            game_state: 游戏状态信息
            **kwargs: 额外数据
        """
        data = {
            'game_id': game_id,
            'step': step,
            'current_player': current_player,
            'game_phase': game_phase,
            'player_cards': player_cards,
            'last_action': last_action,
            'game_state': game_state,
            'timestamp': time.time(),
            **kwargs
        }
        
        # 创建详细的游戏状态消息
        message = f"Game {game_id} Step {step}: Player {current_player} in {game_phase} phase"
        if last_action:
            action_type = last_action.get('type', 'unknown')
            message += f", last action: {action_type}"
        
        self.log("INFO", message, category="game", **data)
        
        # 同时写入到专门的游戏文件
        self._write_game_file(game_id, step, data)
    
    def log_player_action_detailed(
        self,
        game_id: str,
        step: int,
        player_id: int,
        action_type: str,
        action_data: Dict[str, Any],
        game_context: Dict[str, Any] = None,
        decision_time: float = None,
        **kwargs
    ):
        """
        记录详细的玩家动作（GAME层）
        
        Args:
            game_id: 游戏ID
            step: 游戏步数
            player_id: 玩家ID
            action_type: 动作类型（bid/grab/pass/play_cards）
            action_data: 动作具体数据
            game_context: 游戏上下文
            decision_time: 决策时间
            **kwargs: 额外数据
        """
        data = {
            'game_id': game_id,
            'step': step,
            'player_id': player_id,
            'action_type': action_type,
            'action_data': action_data,
            'game_context': game_context,
            'decision_time': decision_time,
            'timestamp': time.time(),
            **kwargs
        }
        
        message = f"Game {game_id} Step {step}: Player {player_id} -> {action_type}"
        if decision_time:
            message += f" (took {decision_time:.3f}s)"
        
        self.log("INFO", message, category="game", **data)
    
    def log_game_statistics(
        self,
        period: str,
        total_games: int,
        win_rates: Dict[str, float],
        avg_game_duration: float,
        avg_scores: Dict[str, float],
        bomb_usage_stats: Dict[str, Any] = None,
        cooperation_stats: Dict[str, Any] = None,
        **kwargs
    ):
        """
        记录游戏统计信息（SUMMARY层）
        
        Args:
            period: 统计周期（hourly/daily/session）
            total_games: 总游戏数
            win_rates: 胜率统计
            avg_game_duration: 平均游戏时长
            avg_scores: 平均分数
            bomb_usage_stats: 炸弹使用统计
            cooperation_stats: 协作统计
            **kwargs: 额外数据
        """
        data = {
            'period': period,
            'total_games': total_games,
            'win_rates': win_rates,
            'avg_game_duration': avg_game_duration,
            'avg_scores': avg_scores,
            'bomb_usage_stats': bomb_usage_stats,
            'cooperation_stats': cooperation_stats,
            'timestamp': time.time(),
            **kwargs
        }
        
        # 计算总体胜率
        overall_win_rate = sum(win_rates.values()) / len(win_rates) if win_rates else 0
        
        message = f"Statistics {period}: {total_games} games, {overall_win_rate:.1%} win rate, {avg_game_duration:.1f}s avg duration"
        
        self.log("INFO", message, category="summary", **data)
        
        # 写入专门的统计文件
        self._write_summary_file(period, data)
    
    def log_training_session_summary(
        self,
        session_id: str,
        episodes_completed: int,
        training_time: float,
        final_loss: float,
        final_win_rate: float,
        model_performance: Dict[str, float],
        **kwargs
    ):
        """
        记录训练会话摘要（SUMMARY层）
        
        Args:
            session_id: 训练会话ID
            episodes_completed: 完成的回合数
            training_time: 训练时间
            final_loss: 最终损失
            final_win_rate: 最终胜率
            model_performance: 模型性能指标
            **kwargs: 额外数据
        """
        data = {
            'session_id': session_id,
            'episodes_completed': episodes_completed,
            'training_time': training_time,
            'final_loss': final_loss,
            'final_win_rate': final_win_rate,
            'model_performance': model_performance,
            'timestamp': time.time(),
            **kwargs
        }
        
        message = f"Training session {session_id} completed: {episodes_completed} episodes, {final_win_rate:.1%} win rate, loss={final_loss:.6f}"
        
        self.log("INFO", message, category="summary", **data)
    
    def _write_game_file(self, game_id: str, step: int, data: Dict[str, Any]):
        """写入专门的游戏文件"""
        try:
            games_dir = self.base_dir / "games"
            games_dir.mkdir(parents=True, exist_ok=True)
            
            # 按日期组织文件
            date_str = datetime.now().strftime('%Y%m%d')
            game_file = games_dir / f"game_{date_str}_{game_id}.jsonl"
            
            # 写入JSON Lines格式
            with open(game_file, 'a', encoding='utf-8') as f:
                json_line = json.dumps(data, ensure_ascii=False, separators=(',', ':'))
                f.write(json_line + '\n')
                
        except Exception as e:
            self.warning(f"Failed to write game file: {e}", category="system")
    
    def _write_summary_file(self, period: str, data: Dict[str, Any]):
        """写入专门的统计文件"""
        try:
            summary_dir = self.base_dir / "summary"
            summary_dir.mkdir(parents=True, exist_ok=True)
            
            # 按周期和日期组织文件
            date_str = datetime.now().strftime('%Y%m%d_%H%M%S')
            summary_file = summary_dir / f"summary_{period}_{date_str}.json"
            
            # 写入格式化的JSON
            with open(summary_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2, separators=(',', ': '))
                
        except Exception as e:
            self.warning(f"Failed to write summary file: {e}", category="system")
    
    def create_game_archive(self, game_id: str, compress: bool = True):
        """
        创建游戏归档文件
        
        Args:
            game_id: 游戏ID
            compress: 是否压缩
        """
        try:
            games_dir = self.base_dir / "games"
            if not games_dir.exists():
                return
            
            # 查找该游戏的所有文件
            game_files = list(games_dir.glob(f"*{game_id}*"))
            
            if not game_files:
                return
            
            archive_dir = games_dir / "archived"
            archive_dir.mkdir(parents=True, exist_ok=True)
            
            if compress:
                import gzip
                for game_file in game_files:
                    compressed_file = archive_dir / f"{game_file.name}.gz"
                    with open(game_file, 'rb') as f_in:
                        with gzip.open(compressed_file, 'wb') as f_out:
                            f_out.writelines(f_in)
                    game_file.unlink()  # 删除原文件
            else:
                import shutil
                for game_file in game_files:
                    shutil.move(str(game_file), str(archive_dir / game_file.name))
            
            self.info(f"Game {game_id} archived with compression={compress}", category="system")
            
        except Exception as e:
            self.warning(f"Failed to archive game {game_id}: {e}", category="system")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取日志统计信息"""
        stats = self._stats.copy()
        
        # 添加异步日志器统计（如果可用）
        if self._async_logger:
            async_stats = self._async_logger.get_stats()
            stats.update({f'async_{k}': v for k, v in async_stats.items()})
        
        # 添加GPU统计信息
        if self.enable_gpu_monitoring:
            stats.update(self._get_gpu_stats())
        
        return stats
    
    def flush(self):
        """刷新所有日志缓冲"""
        try:
            if self._async_logger:
                # 触发立即写入
                self._async_logger._flush_all()
            
            # 刷新控制台日志
            if self._console_logger:
                for handler in self._console_logger.handlers:
                    if hasattr(handler, 'flush'):
                        handler.flush()
        
        except Exception as e:
            print(f"Warning: Failed to flush logs: {e}")
    
    def _initialize_health_checker(self):
        """初始化健康检查器"""
        if not create_log_health_checker:
            self._health_checker = None
            return
            
        try:
            self._health_checker = create_log_health_checker(
                log_dir=str(self.base_dir),
                config_file=None  # 可以传入配置文件路径
            )
            self._health_checker.start()
            self.info("日志健康检查器已启动", category="system")
        except Exception as e:
            self.warning(f"启动健康检查器失败: {e}", category="system")
            self._health_checker = None
    
    def get_health_status(self) -> Dict[str, Any]:
        """获取健康状态"""
        if self._health_checker:
            return self._health_checker.get_health_report()
        return {'status': 'unknown', 'message': '健康检查器未启用'}
    
    def force_health_check(self) -> Dict[str, Any]:
        """强制执行健康检查"""
        if self._health_checker:
            health = self._health_checker.force_health_check()
            return health.to_dict()
        return {'status': 'unknown', 'message': '健康检查器未启用'}
    
    def shutdown(self):
        """关闭日志系统"""
        try:
            # 刷新所有日志
            self.flush()
            
            # 停止健康检查器
            if self._health_checker:
                self._health_checker.stop()
            
            # 停止异步日志器
            if self._async_logger:
                self._async_logger.stop()
            
            # 记录关闭信息
            self.info("ConfigurableLogger shutdown", category="system")
            
        except Exception as e:
            print(f"Warning: Error during logger shutdown: {e}")


# 单例实例管理
_logger_instance: Optional[ConfigurableLogger] = None
_logger_lock = threading.Lock()


def get_logger(name: str = None) -> ConfigurableLogger:
    """
    获取日志器单例实例
    
    Args:
        name: 日志器名称（保留参数，保持兼容性）
        
    Returns:
        ConfigurableLogger实例
    """
    global _logger_instance
    
    if _logger_instance is None:
        with _logger_lock:
            if _logger_instance is None:
                _logger_instance = ConfigurableLogger()
                _logger_instance.initialize()
    
    return _logger_instance


def init_logger(
    log_dir: str = None,
    base_dir: str = None,
    level: str = "INFO",
    console_output: bool = True,
    async_write: bool = None,
    enable_async: bool = None,
    buffer_size: int = 10000,
    **kwargs
) -> ConfigurableLogger:
    """
    初始化日志器
    
    Args:
        log_dir: 日志目录（向后兼容参数）
        base_dir: 日志基础目录
        level: 日志级别
        console_output: 启用控制台输出
        async_write: 启用异步写入（向后兼容参数）
        enable_async: 启用异步写入
        buffer_size: 缓冲区大小
        **kwargs: 其他参数
        
    Returns:
        ConfigurableLogger实例
    """
    global _logger_instance
    
    # 处理base_dir参数的兼容性
    final_base_dir = base_dir or log_dir or "logs"
    
    # 处理enable_async参数的兼容性
    final_enable_async = enable_async if enable_async is not None else (async_write if async_write is not None else True)
    
    # 从kwargs中移除可能重复的参数
    filtered_kwargs = {k: v for k, v in kwargs.items() 
                      if k not in ['base_dir', 'level', 'console_output', 'enable_async', 'buffer_size']}
    
    with _logger_lock:
        if _logger_instance is not None:
            _logger_instance.shutdown()
        
        _logger_instance = ConfigurableLogger(
            base_dir=final_base_dir,
            level=level,
            console_output=console_output,
            enable_async=final_enable_async,
            buffer_size=buffer_size,
            **filtered_kwargs
        )
        _logger_instance.initialize()
    
    return _logger_instance


# 便捷的全局函数
def debug(message: str, **kwargs):
    """全局DEBUG日志函数"""
    get_logger().debug(message, **kwargs)


def info(message: str, **kwargs):
    """全局INFO日志函数"""
    get_logger().info(message, **kwargs)


def warning(message: str, **kwargs):
    """全局WARNING日志函数"""
    get_logger().warning(message, **kwargs)


def error(message: str, **kwargs):
    """全局ERROR日志函数"""
    get_logger().error(message, **kwargs)


def critical(message: str, **kwargs):
    """全局CRITICAL日志函数"""
    get_logger().critical(message, **kwargs)


def log_training_step(step: int, loss: float, reward: float = None, **kwargs):
    """全局训练步骤日志函数"""
    get_logger().log_training_step(step, loss, reward, **kwargs)


def log_game_action(player_id: int, action: str, game_state: Dict[str, Any] = None, **kwargs):
    """全局游戏动作日志函数"""
    get_logger().log_game_action(player_id, action, game_state, **kwargs)


def log_metric(metric_name: str, value: Union[float, int], step: int = None, **kwargs):
    """全局指标日志函数"""
    get_logger().log_metric(metric_name, value, step, **kwargs)


# 导出所有公共接口
__all__ = [
    'ConfigurableLogger',
    'get_logger',
    'init_logger',
    'debug',
    'info', 
    'warning',
    'error',
    'critical',
    'log_training_step',
    'log_game_action',
    'log_metric'
]