"""
训练指标深度分析系统
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

深度分析训练过程中的关键指标：
- MCTS搜索性能
- 价值估计准确性  
- 策略熵趋势
- 学习率自适应
- 模型收敛性
- 训练稳定性

作者: Claude AI
版本: 1.0
日期: 2025-07-06
"""

import re
import time
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, field
from collections import deque, defaultdict
import statistics
import json
import numpy as np

from cardgame_ai.utils.logging import get_logger

logger = get_logger(__name__)


@dataclass
class MCTSMetrics:
    """MCTS搜索指标"""
    timestamp: datetime
    simulation_count: int
    search_depth: int
    value: float
    action_index: int
    probability: float
    policy_entropy: float
    
    # 派生指标
    confidence: float = field(init=False)
    diversity: float = field(init=False)
    
    def __post_init__(self):
        self.confidence = self.probability  # 动作置信度
        self.diversity = max(0, self.policy_entropy)  # 策略多样性


@dataclass
class TrainingEpisodeMetrics:
    """训练轮次指标"""
    episode: int
    timestamp: datetime
    
    # MCTS指标统计
    mcts_searches: int = 0
    avg_value: float = 0.0
    avg_entropy: float = 0.0
    avg_confidence: float = 0.0
    value_variance: float = 0.0
    
    # 性能指标
    search_speed: float = 0.0  # 搜索/秒
    value_stability: float = 0.0  # 价值稳定性
    policy_convergence: float = 0.0  # 策略收敛度
    
    # 问题检测
    anomalies: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)


@dataclass
class ModelPerformanceMetrics:
    """模型性能指标"""
    timestamp: datetime
    
    # 收敛性指标
    value_mean: float
    value_std: float
    entropy_trend: float  # 熵的趋势
    policy_stability: float
    
    # 多样性指标
    action_diversity: float
    exploration_ratio: float
    
    # 学习效率
    learning_rate: float = 0.0
    gradient_norm: float = 0.0
    loss_value: float = 0.0
    
    # 质量评估
    prediction_accuracy: float = 0.0
    value_alignment: float = 0.0


class TrainingMetricsAnalyzer:
    """训练指标深度分析器"""
    
    def __init__(self, analysis_window: int = 300):
        self.analysis_window = analysis_window  # 分析窗口大小
        
        # 数据存储
        self.mcts_history: deque = deque(maxlen=10000)
        self.episode_metrics: Dict[int, TrainingEpisodeMetrics] = {}
        self.performance_history: deque = deque(maxlen=1000)
        
        # 实时统计
        self.current_episode = 0
        self.session_start = datetime.now()
        self.last_analysis = datetime.now()
        
        # 分析阈值
        self.thresholds = {
            "low_value": 0.1,
            "high_value": 0.9,
            "low_entropy": 0.1,
            "high_entropy": 3.0,
            "value_variance_limit": 0.1,
            "convergence_threshold": 0.05
        }
        
        # 锁
        self._lock = threading.Lock()
        
        logger.info("🚀 训练指标深度分析器初始化完成")
        
    def process_mcts_log(self, log_line: str) -> Optional[MCTSMetrics]:
        """处理MCTS日志行"""
        # MCTS搜索完成日志解析
        pattern = r"EfficientZero MCTS搜索完成.*价值: ([\d\.-]+).*最佳动作索引: (\d+).*概率: ([\d\.-]+).*策略熵: ([\d\.-]+)"
        match = re.search(pattern, log_line)
        
        if match:
            try:
                value = float(match.group(1))
                action_index = int(match.group(2))
                probability = float(match.group(3))
                policy_entropy = float(match.group(4))
                
                # 从开始日志中提取模拟次数和深度
                start_pattern = r"EfficientZero MCTS搜索开始.*模拟次数: (\d+).*搜索深度: (\d+)"
                start_match = re.search(start_pattern, log_line)
                
                simulation_count = int(start_match.group(1)) if start_match else 1200
                search_depth = int(start_match.group(2)) if start_match else 10
                
                metrics = MCTSMetrics(
                    timestamp=datetime.now(),
                    simulation_count=simulation_count,
                    search_depth=search_depth,
                    value=value,
                    action_index=action_index,
                    probability=probability,
                    policy_entropy=policy_entropy
                )
                
                with self._lock:
                    self.mcts_history.append(metrics)
                    
                return metrics
                
            except (ValueError, AttributeError) as e:
                logger.debug(f"MCTS日志解析失败: {e}")
                
        return None
        
    def analyze_episode_performance(self, episode: int) -> TrainingEpisodeMetrics:
        """分析特定轮次的性能"""
        with self._lock:
            # 获取该轮次的MCTS数据
            episode_data = [m for m in self.mcts_history 
                          if abs((m.timestamp - datetime.now()).total_seconds()) < 300]  # 最近5分钟
            
        if not episode_data:
            return TrainingEpisodeMetrics(episode=episode, timestamp=datetime.now())
            
        # 统计指标
        values = [m.value for m in episode_data]
        entropies = [m.policy_entropy for m in episode_data]
        confidences = [m.confidence for m in episode_data]
        
        avg_value = statistics.mean(values)
        avg_entropy = statistics.mean(entropies) 
        avg_confidence = statistics.mean(confidences)
        value_variance = statistics.variance(values) if len(values) > 1 else 0.0
        
        # 计算性能指标
        time_span = (episode_data[-1].timestamp - episode_data[0].timestamp).total_seconds()
        search_speed = len(episode_data) / max(time_span, 1)
        
        # 价值稳定性 (低方差 = 高稳定性)
        value_stability = max(0, 1 - value_variance / 0.1)
        
        # 策略收敛度 (低熵变化 = 高收敛)
        entropy_changes = [abs(entropies[i] - entropies[i-1]) for i in range(1, len(entropies))]
        policy_convergence = 1 - (statistics.mean(entropy_changes) if entropy_changes else 0) / 3.0
        policy_convergence = max(0, min(1, policy_convergence))
        
        # 异常检测
        anomalies = []
        warnings = []
        
        if avg_value < self.thresholds["low_value"]:
            anomalies.append(f"价值估计过低: {avg_value:.3f}")
        elif avg_value > self.thresholds["high_value"]:
            anomalies.append(f"价值估计过高: {avg_value:.3f}")
            
        if avg_entropy < self.thresholds["low_entropy"]:
            warnings.append(f"策略熵过低，可能过拟合: {avg_entropy:.3f}")
        elif avg_entropy > self.thresholds["high_entropy"]:
            warnings.append(f"策略熵过高，探索过度: {avg_entropy:.3f}")
            
        if value_variance > self.thresholds["value_variance_limit"]:
            warnings.append(f"价值方差过大，训练不稳定: {value_variance:.3f}")
            
        metrics = TrainingEpisodeMetrics(
            episode=episode,
            timestamp=datetime.now(),
            mcts_searches=len(episode_data),
            avg_value=avg_value,
            avg_entropy=avg_entropy,
            avg_confidence=avg_confidence,
            value_variance=value_variance,
            search_speed=search_speed,
            value_stability=value_stability,
            policy_convergence=policy_convergence,
            anomalies=anomalies,
            warnings=warnings
        )
        
        with self._lock:
            self.episode_metrics[episode] = metrics
            
        return metrics
        
    def analyze_long_term_trends(self) -> ModelPerformanceMetrics:
        """分析长期趋势"""
        with self._lock:
            recent_data = list(self.mcts_history)[-self.analysis_window:]
            
        if len(recent_data) < 10:
            return ModelPerformanceMetrics(
                timestamp=datetime.now(),
                value_mean=0.0,
                value_std=0.0,
                entropy_trend=0.0,
                policy_stability=0.0,
                action_diversity=0.0,
                exploration_ratio=0.0
            )
            
        # 基础统计
        values = [m.value for m in recent_data]
        entropies = [m.policy_entropy for m in recent_data]
        actions = [m.action_index for m in recent_data]
        
        value_mean = statistics.mean(values)
        value_std = statistics.stdev(values)
        
        # 熵趋势 (线性回归斜率)
        entropy_trend = self._calculate_trend(entropies)
        
        # 策略稳定性
        entropy_variance = statistics.variance(entropies) if len(entropies) > 1 else 0
        policy_stability = max(0, 1 - entropy_variance / 2.0)
        
        # 动作多样性
        unique_actions = len(set(actions))
        total_actions = len(actions)
        action_diversity = unique_actions / total_actions if total_actions > 0 else 0
        
        # 探索比率 (高熵动作的比例)
        high_entropy_count = sum(1 for e in entropies if e > 1.5)
        exploration_ratio = high_entropy_count / len(entropies) if entropies else 0
        
        # 价值对齐度 (价值与结果的一致性)
        value_alignment = self._calculate_value_alignment(recent_data)
        
        performance = ModelPerformanceMetrics(
            timestamp=datetime.now(),
            value_mean=value_mean,
            value_std=value_std,
            entropy_trend=entropy_trend,
            policy_stability=policy_stability,
            action_diversity=action_diversity,
            exploration_ratio=exploration_ratio,
            value_alignment=value_alignment
        )
        
        with self._lock:
            self.performance_history.append(performance)
            
        return performance
        
    def _calculate_trend(self, data: List[float]) -> float:
        """计算数据趋势 (简化线性回归)"""
        if len(data) < 2:
            return 0.0
            
        n = len(data)
        x = list(range(n))
        y = data
        
        # 简化线性回归
        x_mean = statistics.mean(x)
        y_mean = statistics.mean(y)
        
        numerator = sum((x[i] - x_mean) * (y[i] - y_mean) for i in range(n))
        denominator = sum((x[i] - x_mean) ** 2 for i in range(n))
        
        if denominator == 0:
            return 0.0
            
        slope = numerator / denominator
        return slope
        
    def _calculate_value_alignment(self, data: List[MCTSMetrics]) -> float:
        """计算价值对齐度"""
        if len(data) < 5:
            return 0.5
            
        # 简化计算：高置信度动作的价值一致性
        high_conf_data = [m for m in data if m.confidence > 0.7]
        if len(high_conf_data) < 3:
            return 0.5
            
        values = [m.value for m in high_conf_data]
        value_consistency = 1 - (statistics.stdev(values) / max(statistics.mean(values), 0.1))
        return max(0, min(1, value_consistency))
        
    def get_realtime_summary(self) -> Dict[str, Any]:
        """获取实时分析摘要"""
        with self._lock:
            recent_mcts = list(self.mcts_history)[-50:] if self.mcts_history else []
            recent_episodes = list(self.episode_metrics.values())[-10:]
            latest_performance = self.performance_history[-1] if self.performance_history else None
            
        summary = {
            "timestamp": datetime.now().isoformat(),
            "session_duration": (datetime.now() - self.session_start).total_seconds(),
            "total_mcts_searches": len(self.mcts_history),
            "analyzed_episodes": len(self.episode_metrics),
            "current_status": "正常",
            "recent_metrics": {},
            "trends": {},
            "alerts": []
        }
        
        if recent_mcts:
            recent_values = [m.value for m in recent_mcts]
            recent_entropies = [m.policy_entropy for m in recent_mcts]
            
            summary["recent_metrics"] = {
                "avg_value": statistics.mean(recent_values),
                "value_range": [min(recent_values), max(recent_values)],
                "avg_entropy": statistics.mean(recent_entropies),
                "search_rate": len(recent_mcts) / 60,  # 假设最近1分钟
                "unique_actions": len(set(m.action_index for m in recent_mcts))
            }
            
        if latest_performance:
            summary["trends"] = {
                "value_stability": latest_performance.policy_stability,
                "exploration_rate": latest_performance.exploration_ratio,
                "action_diversity": latest_performance.action_diversity,
                "entropy_trend": latest_performance.entropy_trend
            }
            
        # 收集告警
        alerts = []
        for episode_metrics in recent_episodes[-3:]:  # 最近3轮
            alerts.extend(episode_metrics.anomalies)
            alerts.extend(episode_metrics.warnings)
            
        summary["alerts"] = list(set(alerts))  # 去重
        
        # 状态评估
        if alerts:
            if any("异常" in alert or "过高" in alert or "过低" in alert for alert in alerts):
                summary["current_status"] = "异常"
            else:
                summary["current_status"] = "警告"
                
        return summary
        
    def generate_training_report(self) -> str:
        """生成训练报告"""
        summary = self.get_realtime_summary()
        
        report = f"""
🎯 训练指标深度分析报告
{'='*50}

📊 基础统计:
   会话时长: {summary['session_duration']/3600:.1f} 小时
   MCTS搜索: {summary['total_mcts_searches']} 次
   分析轮次: {summary['analyzed_episodes']} 轮
   当前状态: {summary['current_status']}

"""
        
        if summary['recent_metrics']:
            metrics = summary['recent_metrics']
            report += f"""📈 近期指标:
   平均价值: {metrics['avg_value']:.3f}
   价值区间: {metrics['value_range'][0]:.3f} - {metrics['value_range'][1]:.3f}
   平均熵: {metrics['avg_entropy']:.3f}
   搜索速率: {metrics['search_rate']:.1f} 次/分钟
   动作多样性: {metrics['unique_actions']} 种

"""
            
        if summary['trends']:
            trends = summary['trends']
            report += f"""📊 趋势分析:
   策略稳定性: {trends['value_stability']:.1%}
   探索率: {trends['exploration_rate']:.1%}
   动作多样性: {trends['action_diversity']:.1%}
   熵趋势: {'↗️' if trends['entropy_trend'] > 0 else '↘️' if trends['entropy_trend'] < 0 else '→'} {trends['entropy_trend']:.4f}

"""
            
        if summary['alerts']:
            report += f"""🚨 告警信息:
"""
            for alert in summary['alerts'][:5]:  # 最多显示5个
                report += f"   • {alert}\n"
                
        return report


# 全局实例
_analyzer: Optional[TrainingMetricsAnalyzer] = None
_analyzer_lock = threading.Lock()


def get_training_analyzer() -> TrainingMetricsAnalyzer:
    """获取训练指标分析器单例"""
    global _analyzer
    
    if _analyzer is None:
        with _analyzer_lock:
            if _analyzer is None:
                _analyzer = TrainingMetricsAnalyzer()
                
    return _analyzer


def analyze_training_log_line(log_line: str):
    """分析单行训练日志"""
    analyzer = get_training_analyzer()
    
    # 处理MCTS日志
    mcts_metrics = analyzer.process_mcts_log(log_line)
    if mcts_metrics:
        logger.debug(f"MCTS指标: 价值={mcts_metrics.value:.3f}, 熵={mcts_metrics.policy_entropy:.3f}")
        

def start_training_analysis() -> TrainingMetricsAnalyzer:
    """启动训练指标分析"""
    analyzer = get_training_analyzer()
    logger.info("✅ 训练指标深度分析系统已启动")
    return analyzer


if __name__ == "__main__":
    # 测试
    print("🚀 启动训练指标分析测试...")
    analyzer = start_training_analysis()
    
    # 模拟一些日志
    test_logs = [
        "EfficientZero MCTS搜索完成 - 价值: 0.253, 最佳动作索引: 0, 概率: 0.059, 策略熵: 2.833",
        "EfficientZero MCTS搜索完成 - 价值: 0.255, 最佳动作索引: 23, 概率: 0.250, 策略熵: 1.386",
        "EfficientZero MCTS搜索完成 - 价值: 0.249, 最佳动作索引: 22, 概率: 0.200, 策略熵: 1.609"
    ]
    
    for log in test_logs:
        analyze_training_log_line(log)
        
    time.sleep(1)
    
    # 生成报告
    report = analyzer.generate_training_report()
    print(report)