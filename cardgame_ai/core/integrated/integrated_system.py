"""
集成AI系统基础导入
"""

import torch
import torch.nn as nn
import numpy as np
from typing import Dict, List, Tuple, Optional, Any, Union
from pathlib import Path
import logging
from cardgame_ai.utils.logging import get_logger

logger = get_logger(__name__)

from .system_core import IntegratedSystemCore
from .algorithm_manager import AlgorithmManager
from .training_coordinator import TrainingCoordinator
from .evaluation_manager import EvaluationManager

class IntegratedAISystem:
    """集成AI系统主类"""
    
    def __init__(self, config: Dict[str, Any]):
        """初始化集成AI系统"""
        self.config = config
        
        # 初始化各个组件
        self.core = IntegratedSystemCore(config)
        self.algorithm_manager = AlgorithmManager(config)
        self.training_coordinator = TrainingCoordinator(config)
        self.evaluation_manager = EvaluationManager(config)
        
        logger.info("集成AI系统初始化完成")
    
    def initialize(self):
        """初始化系统"""
        self.core.initialize()
        logger.info("系统初始化完成")
    
    def train_model(self, algorithm_name: str):
        """训练模型"""
        if not self.algorithm_manager.select_algorithm(algorithm_name):
            return False
        
        algorithm = self.algorithm_manager.get_current_algorithm()
        return self.training_coordinator.start_training(algorithm, None)
    
    def evaluate_model(self, model):
        """评估模型"""
        return self.evaluation_manager.evaluate_model(model, None)
    
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        return {
            "core_initialized": self.core.is_initialized,
            "training_state": self.training_coordinator.get_training_state(),
            "available_algorithms": self.algorithm_manager.list_algorithms(),
            "latest_evaluation": self.evaluation_manager.get_latest_results()
        }
