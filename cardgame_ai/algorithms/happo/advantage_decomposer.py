"""
HAPPO模块基础导入
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
from cardgame_ai.utils.logging import get_logger
from ..mappo import MAPPO, MAPPOConfig
from ..role_specific_mappo import RoleSpecificMAPPO
from ...core.base import State, Action
from ...games.doudizhu.state_modules import DoudizhuState

logger = get_logger(__name__)


class HAPPOAdvantageDecomposer:
    """HAPPO优势分解器 - 将联合优势分解为个体贡献"""
    
    def __init__(self, method: str = "credit", temperature: float = 1.0):
        """初始化优势分解器"""
        self.method = method
        self.temperature = temperature
        logger.info(f"优势分解器初始化: method={method}")
    
    def decompose_advantage(self, joint_advantage: torch.Tensor, 
                          individual_values: List[torch.Tensor],
                          agent_ids: List[str]) -> Dict[str, torch.Tensor]:
        """分解联合优势函数"""
        if self.method == "credit":
            return self._credit_assignment(joint_advantage, individual_values, agent_ids)
        elif self.method == "shapley":
            return self._shapley_value(joint_advantage, individual_values, agent_ids)
        else:
            return self._equal_split(joint_advantage, agent_ids)
    
    def _credit_assignment(self, joint_advantage, individual_values, agent_ids):
        """信用分配方法"""
        decomposed = {}
        total_value = sum(v.mean() for v in individual_values)
        
        for i, agent_id in enumerate(agent_ids):
            if total_value > 0:
                weight = individual_values[i].mean() / total_value
                decomposed[agent_id] = joint_advantage * weight
            else:
                decomposed[agent_id] = joint_advantage / len(agent_ids)
        
        return decomposed
    
    def _shapley_value(self, joint_advantage, individual_values, agent_ids):
        """Shapley值方法（简化版）"""
        # 简化的Shapley值计算
        decomposed = {}
        n = len(agent_ids)
        
        for i, agent_id in enumerate(agent_ids):
            # 计算边际贡献
            marginal_contrib = individual_values[i] / n
            decomposed[agent_id] = joint_advantage * marginal_contrib / joint_advantage.mean()
        
        return decomposed
    
    def _equal_split(self, joint_advantage, agent_ids):
        """平均分配"""
        return {agent_id: joint_advantage / len(agent_ids) for agent_id in agent_ids}
