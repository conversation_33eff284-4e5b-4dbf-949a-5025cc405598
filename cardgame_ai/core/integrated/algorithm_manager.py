"""
集成AI系统基础导入
"""

import torch
import torch.nn as nn
import numpy as np
from typing import Dict, List, Tuple, Optional, Any, Union
from pathlib import Path
import logging
from cardgame_ai.utils.logging import get_logger

logger = get_logger(__name__)


class AlgorithmManager:
    """算法管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        """初始化算法管理器"""
        self.config = config
        self.algorithms = {}
        self.current_algorithm = None
        logger.info("算法管理器初始化")
    
    def register_algorithm(self, name: str, algorithm):
        """注册算法"""
        self.algorithms[name] = algorithm
        logger.info(f"注册算法: {name}")
    
    def select_algorithm(self, name: str):
        """选择算法"""
        if name in self.algorithms:
            self.current_algorithm = self.algorithms[name]
            logger.info(f"选择算法: {name}")
            return True
        else:
            logger.warning(f"算法不存在: {name}")
            return False
    
    def get_current_algorithm(self):
        """获取当前算法"""
        return self.current_algorithm
    
    def list_algorithms(self) -> List[str]:
        """列出所有算法"""
        return list(self.algorithms.keys())
