# 质量验证系统 API 参考文档

## 目录

1. [TrainingQualityValidator](#trainingqualityvalidator)
2. [DimensionConsistencyTester](#dimensionconsistencytester)
3. [PerformanceBenchmark](#performancebenchmark)
4. [RealtimeTrainingMonitor](#realtimetrainingmonitor)
5. [ModelQualityEvaluator](#modelqualityevaluator)
6. [Training<PERSON>ealthChecker](#traininghealthchecker)
7. [数据类型定义](#数据类型定义)

---

## TrainingQualityValidator

训练质量验证器，提供全面的训练质量检查功能。

### 类定义

```python
class TrainingQualityValidator:
    """训练质量验证器"""
    
    def __init__(self, config_path: str = "optimized_fixed_config.yaml"):
        """
        初始化质量验证器
        
        Args:
            config_path: 配置文件路径
        """
```

### 方法

#### validate_prerequisites()

```python
def validate_prerequisites(self) -> <PERSON><PERSON>[bool, List[ValidationResult]]:
    """
    验证训练前提条件
    
    Returns:
        Tuple[bool, List[ValidationResult]]: 
            - bool: 是否所有前提条件都满足
            - List[ValidationResult]: 每个检查项的结果列表
    
    检查项包括:
        - Python版本 (3.8-3.11)
        - PyTorch安装和版本
        - CUDA可用性
        - 项目结构完整性
        - 配置文件有效性
        - 依赖包安装
    """
```

#### validate_architecture()

```python
def validate_architecture(self) -> Tuple[bool, ValidationResult]:
    """
    验证架构一致性
    
    Returns:
        Tuple[bool, ValidationResult]:
            - bool: 架构是否一致
            - ValidationResult: 架构验证结果
    
    验证内容:
        - 116维观察空间一致性
        - 组件兼容性
        - 算法实现正确性
    """
```

#### validate_training_start()

```python
def validate_training_start(self) -> Tuple[bool, ValidationResult]:
    """
    验证训练启动状态
    
    Returns:
        Tuple[bool, ValidationResult]:
            - bool: 训练是否成功启动
            - ValidationResult: 启动验证结果
    
    检查内容:
        - 训练进程存活
        - 初始化成功
        - 资源分配正常
    """
```

#### validate_performance()

```python
def validate_performance(self, metrics: Dict[str, float]) -> Tuple[bool, ValidationResult]:
    """
    验证性能指标
    
    Args:
        metrics: 性能指标字典，包含:
            - gpu_utilization: GPU利用率 (%)
            - throughput: 吞吐量 (samples/s)
            - memory_usage: 内存使用 (%)
    
    Returns:
        Tuple[bool, ValidationResult]:
            - bool: 性能是否达标
            - ValidationResult: 性能验证结果
    """
```

#### generate_report()

```python
def generate_report(self) -> QualityReport:
    """
    生成质量报告
    
    Returns:
        QualityReport: 包含所有验证结果的综合报告
    """
```

---

## DimensionConsistencyTester

维度一致性测试器，验证116维观察空间的一致性。

### 类定义

```python
class DimensionConsistencyTester:
    """维度一致性测试器"""
    
    def __init__(self):
        """初始化测试器"""
```

### 方法

#### run_all_tests()

```python
def run_all_tests(self) -> Dict[str, Any]:
    """
    运行所有维度一致性测试
    
    Returns:
        Dict[str, Any]: 测试结果，包含:
            - overall_score: 总体得分 (0-100)
            - passed_tests: 通过的测试数
            - total_tests: 总测试数
            - failed_components: 失败的组件列表
            - test_results: 详细测试结果
    """
```

#### test_component_dimension()

```python
def test_component_dimension(self, component_name: str) -> Dict[str, Any]:
    """
    测试特定组件的维度
    
    Args:
        component_name: 组件名称，可选值:
            - "efficient_zero_model"
            - "happo_policy"
            - "ppb_mcts"
            - "observation_encoder"
    
    Returns:
        Dict[str, Any]: 组件测试结果
    """
```

#### validate_observation_shape()

```python
def validate_observation_shape(self, shape: List[int]) -> bool:
    """
    验证观察空间形状
    
    Args:
        shape: 观察空间形状
    
    Returns:
        bool: 是否为正确的116维
    """
```

---

## PerformanceBenchmark

性能基准测试器，测量训练性能指标。

### 类定义

```python
class PerformanceBenchmark:
    """性能基准测试器"""
    
    def __init__(self, device: str = "cuda:0", model_type: str = "efficient_zero"):
        """
        初始化基准测试器
        
        Args:
            device: 计算设备
            model_type: 模型类型
        """
```

### 方法

#### run_benchmark()

```python
def run_benchmark(
    self,
    batch_size: int = 32,
    num_iterations: int = 100,
    warmup_iterations: int = 10,
    mixed_precision: bool = True
) -> Dict[str, float]:
    """
    运行性能基准测试
    
    Args:
        batch_size: 批次大小
        num_iterations: 测试迭代次数
        warmup_iterations: 预热迭代次数
        mixed_precision: 是否使用混合精度
    
    Returns:
        Dict[str, float]: 性能指标
            - throughput: 吞吐量 (samples/s)
            - gpu_utilization: GPU利用率 (%)
            - memory_usage: 内存使用 (MB)
            - forward_time: 前向传播时间 (ms)
            - backward_time: 反向传播时间 (ms)
            - optimization_time: 优化器时间 (ms)
    """
```

#### profile_memory()

```python
def profile_memory(self) -> Dict[str, float]:
    """
    分析内存使用
    
    Returns:
        Dict[str, float]: 内存使用统计
            - allocated: 已分配内存 (MB)
            - reserved: 预留内存 (MB)
            - peak: 峰值使用 (MB)
    """
```

---

## RealtimeTrainingMonitor

实时训练监控器，提供训练过程的实时监控。

### 类定义

```python
class RealtimeTrainingMonitor:
    """实时训练监控器"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化监控器
        
        Args:
            config: 配置字典，可选参数:
                - monitor_interval: 监控间隔 (秒)
                - enable_gpu_monitoring: 是否启用GPU监控
                - alert_thresholds: 警报阈值
        """
```

### 方法

#### start_monitoring()

```python
def start_monitoring(self):
    """启动监控线程"""
```

#### stop_monitoring()

```python
def stop_monitoring(self):
    """停止监控"""
```

#### update_training_metrics()

```python
def update_training_metrics(self, **metrics):
    """
    更新训练指标
    
    Args:
        **metrics: 可变关键字参数，支持:
            - loss: 训练损失
            - win_rate: 胜率
            - episodes: 总回合数
            - samples: 总样本数
            - learning_rate: 学习率
            - gradient_norm: 梯度范数
    """
```

#### get_current_stats()

```python
def get_current_stats(self) -> Dict[str, Any]:
    """
    获取当前统计信息
    
    Returns:
        Dict[str, Any]: 当前状态，包含:
            - total_episodes: 总回合数
            - total_samples: 总样本数
            - metrics: 各项指标的当前值和趋势
            - alerts: 活跃的警报列表
    """
```

#### register_alert_callback()

```python
def register_alert_callback(self, callback: Callable[[str, str], None]):
    """
    注册警报回调函数
    
    Args:
        callback: 回调函数，签名为 (alert_type: str, message: str) -> None
    """
```

---

## ModelQualityEvaluator

模型质量评估器，评估训练后的模型性能。

### 类定义

```python
class ModelQualityEvaluator:
    """模型质量评估器"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化评估器
        
        Args:
            config: 配置字典，可选参数:
                - num_evaluation_games: 评估游戏数
                - parallel_games: 并行游戏数
                - opponent_types: 对手类型列表
        """
```

### 方法

#### evaluate_model()

```python
def evaluate_model(
    self,
    model_path: str,
    model_type: str = "efficient_zero",
    device: str = None
) -> EvaluationResult:
    """
    评估模型质量
    
    Args:
        model_path: 模型文件路径
        model_type: 模型类型
        device: 计算设备
    
    Returns:
        EvaluationResult: 评估结果对象
    """
```

#### evaluate_against_opponent()

```python
def evaluate_against_opponent(
    self,
    model: Any,
    opponent_type: str,
    num_games: int = 100
) -> Dict[str, float]:
    """
    对特定对手评估
    
    Args:
        model: 模型实例
        opponent_type: 对手类型 ("random", "rule_based", "self_play", "human_like")
        num_games: 评估游戏数
    
    Returns:
        Dict[str, float]: 评估指标
    """
```

---

## TrainingHealthChecker

训练健康检查器，提供自动问题检测和恢复。

### 类定义

```python
class TrainingHealthChecker:
    """训练健康检查器"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化健康检查器
        
        Args:
            config: 配置字典，可选参数:
                - check_interval: 检查间隔 (秒)
                - max_recovery_attempts: 最大恢复尝试次数
                - checkpoint_dir: 检查点目录
                - thresholds: 健康阈值字典
        """
```

### 方法

#### start_checking()

```python
def start_checking(self, monitor: Optional[RealtimeTrainingMonitor] = None):
    """
    启动健康检查
    
    Args:
        monitor: 可选的训练监控器实例
    """
```

#### stop_checking()

```python
def stop_checking(self):
    """停止健康检查，生成健康报告"""
```

#### manual_recovery()

```python
def manual_recovery(self, action: str) -> bool:
    """
    手动触发恢复动作
    
    Args:
        action: 恢复动作，可选值:
            - RecoveryAction.CLEAR_CACHE: 清理缓存
            - RecoveryAction.REDUCE_BATCH_SIZE: 减小批次大小
            - RecoveryAction.RESTART_FROM_CHECKPOINT: 从检查点恢复
            - RecoveryAction.RESTART_PROCESS: 重启进程
            - RecoveryAction.EMERGENCY_SAVE: 紧急保存
    
    Returns:
        bool: 恢复是否成功
    """
```

#### get_health_status()

```python
def get_health_status(self) -> Dict[str, Any]:
    """
    获取当前健康状态
    
    Returns:
        Dict[str, Any]: 健康状态信息
            - status: 状态 (healthy/warning/critical/dead/recovering)
            - recovery_attempts: 恢复尝试次数
            - error_counts: 错误计数字典
            - last_recovery: 最后恢复信息
    """
```

#### register_recovery_callback()

```python
def register_recovery_callback(self, callback: Callable[[str, Dict[str, Any]], None]):
    """
    注册恢复回调函数
    
    Args:
        callback: 回调函数，签名为 (action: str, recovery_log: dict) -> None
    """
```

#### set_training_command()

```python
def set_training_command(self, command: List[str]):
    """
    设置训练命令（用于进程重启）
    
    Args:
        command: 训练命令列表，如 ["python", "train.py", "--config", "config.yaml"]
    """
```

---

## 数据类型定义

### ValidationResult

```python
@dataclass
class ValidationResult:
    """验证结果"""
    check_name: str          # 检查项名称
    passed: bool            # 是否通过
    score: float           # 得分 (0-100)
    details: str           # 详细信息
    error_message: str = None  # 错误信息
    suggestions: List[str] = None  # 改进建议
```

### QualityReport

```python
@dataclass
class QualityReport:
    """质量报告"""
    timestamp: str              # 报告时间戳
    overall_score: float       # 总体得分 (0-100)
    prerequisites_score: float  # 前提条件得分
    architecture_score: float   # 架构得分
    performance_score: float    # 性能得分
    model_quality_score: float  # 模型质量得分
    stability_score: float      # 稳定性得分
    validation_results: List[ValidationResult]  # 所有验证结果
    recommendations: List[str]  # 改进建议
    
    def save(self, filepath: str):
        """保存报告到文件"""
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
```

### EvaluationResult

```python
@dataclass
class EvaluationResult:
    """模型评估结果"""
    model_path: str           # 模型路径
    timestamp: str            # 评估时间戳
    win_rate: float          # 总胜率
    win_rates_by_role: Dict[str, float]  # 各角色胜率
    win_rates_by_opponent: Dict[str, float]  # 对各对手胜率
    cooperation_score: float  # 合作得分
    bomb_strategy_score: float  # 炸弹策略得分
    spring_detection_accuracy: float  # 春天检测准确率
    decision_time_ms: float  # 平均决策时间
    
    def meets_requirements(self) -> bool:
        """是否满足质量要求"""
```

### HealthStatus

```python
class HealthStatus:
    """健康状态枚举"""
    HEALTHY = "healthy"      # 健康
    WARNING = "warning"      # 警告
    CRITICAL = "critical"    # 危急
    DEAD = "dead"           # 死亡
    RECOVERING = "recovering"  # 恢复中
```

### RecoveryAction

```python
class RecoveryAction:
    """恢复动作枚举"""
    NONE = "none"                    # 无动作
    CLEAR_CACHE = "clear_cache"      # 清理缓存
    REDUCE_BATCH_SIZE = "reduce_batch_size"  # 减小批次大小
    RESTART_FROM_CHECKPOINT = "restart_from_checkpoint"  # 从检查点恢复
    RESTART_PROCESS = "restart_process"  # 重启进程
    EMERGENCY_SAVE = "emergency_save"    # 紧急保存
```