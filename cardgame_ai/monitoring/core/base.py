"""
基础监控模块
提供所有监控组件的基类和通用功能
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime
import threading
import time
from dataclasses import dataclass
from enum import Enum

class MonitoringLevel(Enum):
    """监控级别"""
    DEBUG = "debug"
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"

class MetricType(Enum):
    """指标类型"""
    COUNTER = "counter"
    GAUGE = "gauge"
    HISTOGRAM = "histogram"
    SUMMARY = "summary"
    
@dataclass
class MonitoringEvent:
    """监控事件"""
    timestamp: datetime
    level: MonitoringLevel
    source: str
    metric_name: str
    value: Any
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}

class MonitoringBase(ABC):
    """监控基类"""
    
    def __init__(self, name: str, config: Optional[Dict[str, Any]] = None):
        self.name = name
        self.config = config or {}
        self.is_running = False
        self.last_update = None
        self.metrics = {}
        self.callbacks = []
        self._lock = threading.Lock()
        
    @abstractmethod
    def start(self):
        """启动监控"""
        pass
        
    @abstractmethod
    def stop(self):
        """停止监控"""
        pass
        
    @abstractmethod
    def collect_metrics(self) -> Dict[str, Any]:
        """收集指标"""
        pass
        
    def add_callback(self, callback: Callable[[MonitoringEvent], None]):
        """添加回调函数"""
        self.callbacks.append(callback)
        
    def remove_callback(self, callback: Callable[[MonitoringEvent], None]):
        """移除回调函数"""
        if callback in self.callbacks:
            self.callbacks.remove(callback)
            
    def emit_event(self, level: MonitoringLevel, metric_name: str, 
                   value: Any, metadata: Dict[str, Any] = None):
        """发出监控事件"""
        event = MonitoringEvent(
            timestamp=datetime.now(),
            level=level,
            source=self.name,
            metric_name=metric_name,
            value=value,
            metadata=metadata or {}
        )
        
        # 调用所有回调函数
        for callback in self.callbacks:
            try:
                callback(event)
            except Exception as e:
                print(f"Callback error: {e}")
                
    def record_metric(self, name: str, value: Any, metric_type: MetricType = MetricType.GAUGE):
        """记录指标"""
        with self._lock:
            if name not in self.metrics:
                self.metrics[name] = {
                    'type': metric_type,
                    'values': [],
                    'timestamps': []
                }
                
            self.metrics[name]['values'].append(value)
            self.metrics[name]['timestamps'].append(datetime.now())
            
            # 保持最近1000个数据点
            if len(self.metrics[name]['values']) > 1000:
                self.metrics[name]['values'].pop(0)
                self.metrics[name]['timestamps'].pop(0)
                
        # 发出事件
        self.emit_event(MonitoringLevel.INFO, name, value)
        
    def get_metric(self, name: str) -> Optional[Dict[str, Any]]:
        """获取指标"""
        with self._lock:
            return self.metrics.get(name)
            
    def get_latest_value(self, name: str) -> Any:
        """获取最新值"""
        metric = self.get_metric(name)
        if metric and metric['values']:
            return metric['values'][-1]
        return None
        
    def clear_metrics(self):
        """清空指标"""
        with self._lock:
            self.metrics.clear()

class PeriodicMonitor(MonitoringBase):
    """定期监控基类"""
    
    def __init__(self, name: str, interval: float = 1.0, config: Optional[Dict[str, Any]] = None):
        super().__init__(name, config)
        self.interval = interval
        self._thread = None
        self._stop_event = threading.Event()
        
    def start(self):
        """启动监控"""
        if self.is_running:
            return
            
        self.is_running = True
        self._stop_event.clear()
        self._thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self._thread.start()
        
    def stop(self):
        """停止监控"""
        if not self.is_running:
            return
            
        self.is_running = False
        self._stop_event.set()
        
        if self._thread and self._thread.is_alive():
            self._thread.join(timeout=5.0)
            
    def _monitor_loop(self):
        """监控循环"""
        while not self._stop_event.is_set():
            try:
                metrics = self.collect_metrics()
                for name, value in metrics.items():
                    self.record_metric(name, value)
                    
                self.last_update = datetime.now()
                
            except Exception as e:
                self.emit_event(
                    MonitoringLevel.ERROR,
                    "monitor_error",
                    str(e),
                    {"exception_type": type(e).__name__}
                )
                
            self._stop_event.wait(self.interval)

class MonitoringManager:
    """监控管理器"""
    
    def __init__(self):
        self.monitors: Dict[str, MonitoringBase] = {}
        self.global_callbacks = []
        
    def register_monitor(self, monitor: MonitoringBase):
        """注册监控器"""
        self.monitors[monitor.name] = monitor
        
        # 添加全局回调
        for callback in self.global_callbacks:
            monitor.add_callback(callback)
            
    def unregister_monitor(self, name: str):
        """注销监控器"""
        if name in self.monitors:
            monitor = self.monitors[name]
            monitor.stop()
            del self.monitors[name]
            
    def start_all(self):
        """启动所有监控"""
        for monitor in self.monitors.values():
            monitor.start()
            
    def stop_all(self):
        """停止所有监控"""
        for monitor in self.monitors.values():
            monitor.stop()
            
    def add_global_callback(self, callback: Callable[[MonitoringEvent], None]):
        """添加全局回调"""
        self.global_callbacks.append(callback)
        
        # 为所有现有监控器添加回调
        for monitor in self.monitors.values():
            monitor.add_callback(callback)
            
    def get_monitor(self, name: str) -> Optional[MonitoringBase]:
        """获取监控器"""
        return self.monitors.get(name)
        
    def get_all_metrics(self) -> Dict[str, Dict[str, Any]]:
        """获取所有指标"""
        all_metrics = {}
        for name, monitor in self.monitors.items():
            all_metrics[name] = monitor.collect_metrics()
        return all_metrics

# 全局管理器实例
_global_manager = MonitoringManager()

def get_monitoring_manager() -> MonitoringManager:
    """获取全局监控管理器"""
    return _global_manager
