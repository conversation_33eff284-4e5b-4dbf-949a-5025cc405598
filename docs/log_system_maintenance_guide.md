# 斗地主AI日志系统维护和使用指南

版本：1.0  
更新时间：2025-07-06  
维护团队：AI系统运维组  

## 目录

1. [快速开始](#1-快速开始)
2. [系统架构](#2-系统架构)
3. [配置指南](#3-配置指南)
4. [使用说明](#4-使用说明)
5. [故障排除](#5-故障排除)
6. [性能调优](#6-性能调优)
7. [监控和告警](#7-监控和告警)
8. [常见问题](#8-常见问题)
9. [维护任务](#9-维护任务)
10. [API参考](#10-api参考)

## 1. 快速开始

### 1.1 基本使用

```python
from cardgame_ai.utils.simple_logger import get_logger

# 获取日志器实例
logger = get_logger()

# 记录不同级别的日志
logger.info("训练开始")
logger.debug("详细调试信息", category="debug")
logger.warning("内存使用率过高", include_gpu_stats=True)
logger.error("模型加载失败", error_code=500)
```

### 1.2 初始化配置

```python
from cardgame_ai.utils.simple_logger import init_logger

# 自定义初始化
logger = init_logger(
    base_dir="logs",
    level="INFO",
    enable_async=True,
    buffer_size=10000,
    console_output=True
)
```

### 1.3 训练日志

```python
# 记录训练步骤
logger.log_training_step(
    step=1000,
    loss=0.532,
    reward=1.2,
    win_rate=0.85
)

# 记录游戏动作
logger.log_game_action(
    player_id=0,
    action="play_cards",
    game_state={"phase": "playing", "round": 5}
)
```

## 2. 系统架构

### 2.1 5层日志架构

```
日志系统架构图：

┌─────────────────────────────────────────────┐
│            应用层（训练/游戏）               │
├─────────────────────────────────────────────┤
│           ConfigurableLogger                │
│  ┌──────────┬──────────┬──────────────┐   │
│  │异步写入器 │ 路由器   │ GPU监控器    │   │
│  └──────────┴──────────┴──────────────┘   │
├─────────────────────────────────────────────┤
│              5层日志架构                    │
│  ┌──────┬──────┬──────┬──────┬──────┐    │
│  │DEBUG │TRAIN │GAME  │SUMM  │GAMES │    │
│  └──────┴──────┴──────┴──────┴──────┘    │
├─────────────────────────────────────────────┤
│           文件系统 / 存储层                 │
└─────────────────────────────────────────────┘
```

### 2.2 各层职责

| 层级 | 目录 | 用途 | 日志级别 | 保留时间 |
|------|------|------|----------|----------|
| DEBUG | logs/debug/ | 详细调试信息、变量值、函数调用 | DEBUG | 24小时 |
| TRAINING | logs/training/ | 训练损失、奖励、模型更新 | INFO | 7天 |
| GAME | logs/game/ | 游戏状态、玩家动作、决策过程 | INFO | 30天 |
| SUMMARY | logs/summary/ | 统计摘要、性能报告、胜率分析 | INFO | 永久 |
| GAMES | logs/games/ | 完整游戏记录、回放数据 | INFO | 90天 |

### 2.3 数据流向

```
用户代码 → logger.info() → ConfigurableLogger
                              ↓
                         日志消息增强
                    (时间戳、线程ID、GPU状态)
                              ↓
                    ┌─────────┴─────────┐
                    │                   │
              异步缓冲队列          控制台输出
                    │                   │
              批量写入器            格式化显示
                    │
              文件系统(5层)
```

## 3. 配置指南

### 3.1 YAML配置文件

在`optimized_fixed_config.yaml`中配置：

```yaml
logging:
  # 基础配置
  base_dir: "logs"              # 日志根目录
  level: "INFO"                 # 全局日志级别
  console_output: true          # 控制台输出
  
  # 异步日志配置
  async_write: true             # 启用异步写入
  buffer_size: 10000           # 缓冲队列大小
  batch_size: 100              # 批量写入大小
  flush_interval: 1.0          # 刷新间隔(秒)
  
  # 文件管理
  max_file_size_mb: 500        # 单文件最大大小
  rotation: "daily"            # 轮转策略: daily/size/none
  compression: true            # 启用压缩归档
  
  # GPU监控
  gpu_monitoring: true         # 启用GPU监控
  gpu_log_interval: 60        # GPU日志间隔(秒)
  
  # 5层架构配置
  layers:
    debug:
      enabled: true
      level: "DEBUG"
      max_size_mb: 500
      retention_hours: 24
      
    training:
      enabled: true
      level: "INFO"
      retention_days: 7
      include_gpu_stats: true
      
    game:
      enabled: true
      level: "INFO"
      retention_days: 30
      detailed_logging: true
      
    summary:
      enabled: true
      level: "INFO"
      retention_days: -1    # 永久保留
      interval_minutes: 60  # 统计间隔
      
    games:
      enabled: true
      format: "jsonl"      # json/jsonl/msgpack
      compression: "gzip"  # none/gzip/lz4
      retention_days: 90
      
  # 健康检查
  health_check:
    enabled: true
    interval: 60           # 检查间隔(秒)
    auto_repair: true      # 自动修复
    
  # 告警配置
  alerts:
    enabled: true
    channels: ["log", "console"]
    rules:
      - name: "high_error_rate"
        threshold: 10      # 每秒错误数
        severity: "warning"
      - name: "disk_space_low"
        threshold: 90      # 磁盘使用率%
        severity: "critical"
```

### 3.2 环境变量配置

支持通过环境变量覆盖配置：

```bash
# 设置日志级别
export LOG_LEVEL=DEBUG

# 设置日志目录
export LOG_BASE_DIR=/var/log/cardgame_ai

# 禁用GPU监控
export LOG_GPU_MONITORING=false

# 设置缓冲区大小
export LOG_BUFFER_SIZE=20000
```

### 3.3 代码级配置

```python
from cardgame_ai.utils.simple_logger import init_logger

# 完整配置示例
logger = init_logger(
    # 基础配置
    base_dir="logs",
    level="INFO",
    console_output=True,
    
    # 异步配置
    enable_async=True,
    buffer_size=10000,
    batch_size=100,
    flush_interval=1.0,
    
    # 文件配置
    max_file_size=500 * 1024 * 1024,  # 500MB
    
    # GPU监控
    enable_gpu_monitoring=True,
    
    # 健康检查
    enable_health_check=True
)
```

## 4. 使用说明

### 4.1 基础日志功能

```python
# 获取日志器
logger = get_logger()

# 基础日志方法
logger.debug("调试信息")
logger.info("一般信息")
logger.warning("警告信息")
logger.error("错误信息")
logger.critical("严重错误")

# 带分类的日志
logger.info("模型更新完成", category="training")
logger.debug("内存分配详情", category="debug")

# 带额外数据的日志
logger.info("训练进度", 
    category="training",
    epoch=10,
    total_epochs=100,
    progress=0.1
)

# 包含GPU统计
logger.info("训练批次完成",
    category="training",
    include_gpu_stats=True
)
```

### 4.2 训练专用功能

```python
# 记录训练步骤
logger.log_training_step(
    step=1000,
    loss=0.532,
    reward=1.2,
    learning_rate=0.001,
    batch_size=32,
    win_rate=0.85,
    avg_game_length=120
)

# 记录训练会话摘要
logger.log_training_session_summary(
    session_id="train_20250706_001",
    episodes_completed=10000,
    training_time=3600.5,  # 秒
    final_loss=0.125,
    final_win_rate=0.92,
    model_performance={
        "accuracy": 0.95,
        "f1_score": 0.93,
        "inference_time_ms": 2.5
    }
)

# 记录指标
logger.log_metric("learning_rate", 0.001, step=1000)
logger.log_metric("gpu_memory_gb", 8.5, step=1000)
```

### 4.3 游戏日志功能

```python
# 记录游戏动作
logger.log_game_action(
    player_id=0,
    action="play_cards",
    cards=["3", "3", "3"],
    action_type="triple",
    decision_time=0.125
)

# 记录详细游戏状态
logger.log_detailed_game_state(
    game_id="game_001",
    step=50,
    current_player=0,
    game_phase="playing",
    player_cards={
        0: ["A", "K", "Q", "J"],
        1: ["2", "2", "A", "K"],
        2: ["大王", "小王", "2", "2"]
    },
    last_action={
        "player": 2,
        "type": "pass",
        "cards": []
    },
    game_state={
        "landlord": 0,
        "multiplier": 2,
        "bombs_used": 1,
        "remaining_cards": 15
    }
)

# 记录玩家详细动作
logger.log_player_action_detailed(
    game_id="game_001",
    step=51,
    player_id=0,
    action_type="play_cards",
    action_data={
        "cards": ["A", "A", "A"],
        "card_type": "triple",
        "is_bomb": False
    },
    game_context={
        "last_played": ["K", "K", "K"],
        "passes": 2,
        "hand_size": 4
    },
    decision_time=0.089,
    mcts_simulations=1000,
    confidence=0.95
)

# 记录游戏摘要
logger.log_game_summary(
    game_id="game_001",
    winner=0,  # 地主获胜
    scores=[300, -100, -200],
    duration=185.5,  # 秒
    total_rounds=25,
    bombs_played=2,
    spring=False,
    anti_spring=False
)
```

### 4.4 统计日志功能

```python
# 记录游戏统计
logger.log_game_statistics(
    period="hourly",
    total_games=157,
    win_rates={
        "landlord": 0.58,
        "farmer": 0.42,
        "overall": 0.58
    },
    avg_game_duration=180.5,
    avg_scores={
        "landlord": 125.3,
        "farmer1": -62.6,
        "farmer2": -62.7
    },
    bomb_usage_stats={
        "total_bombs": 89,
        "avg_per_game": 0.57,
        "bomb_win_rate": 0.75
    },
    cooperation_stats={
        "successful_cooperations": 45,
        "cooperation_rate": 0.68,
        "implicit_signals": 120
    }
)

# 记录性能统计
logger.info("系统性能统计",
    category="summary",
    metrics={
        "fps": 156.8,
        "cpu_usage": 45.2,
        "memory_usage_gb": 12.5,
        "gpu_usage": 87.3,
        "disk_io_mb_s": 125.6
    }
)
```

### 4.5 高级功能

```python
# 创建游戏归档
logger.create_game_archive(
    game_id="game_001",
    compress=True  # 使用gzip压缩
)

# 强制刷新日志
logger.flush()

# 获取日志统计
stats = logger.get_stats()
print(f"总日志数: {stats['total_logs']}")
print(f"错误日志: {stats['error_logs']}")
print(f"GPU使用率: {stats['gpu_0']['memory_usage_percent']}%")

# 获取健康状态
health = logger.get_health_status()
print(f"健康状态: {health['current_status']}")
print(f"问题数量: {len(health.get('issues', []))}")

# 强制健康检查
health_report = logger.force_health_check()
for layer, status in health_report['layer_health'].items():
    print(f"{layer}: {'健康' if status['is_healthy'] else '异常'}")
```

## 5. 故障排除

### 5.1 常见问题诊断

#### 问题1：日志文件未生成

**症状**：
- logs目录为空
- 控制台有输出但文件没有

**诊断步骤**：
```bash
# 1. 检查目录权限
ls -la logs/

# 2. 检查磁盘空间
df -h

# 3. 查看进程是否有写权限
lsof | grep logs

# 4. 检查Python日志
python -c "import logging; logging.basicConfig(level=logging.DEBUG)"
```

**解决方案**：
```python
# 强制创建目录
import os
os.makedirs("logs", exist_ok=True)

# 检查日志器状态
logger = get_logger()
print(logger._initialized)  # 应该为True
print(logger.base_dir)       # 检查路径
```

#### 问题2：日志内容不完整

**症状**：
- 部分日志丢失
- 日志顺序混乱
- 文件突然截断

**诊断步骤**：
```python
# 1. 检查缓冲区状态
stats = logger.get_stats()
print(f"缓冲区使用: {stats.get('async_buffer_usage', 0)}")

# 2. 强制刷新
logger.flush()

# 3. 检查健康状态
health = logger.force_health_check()
print(health['performance_metrics'])
```

**解决方案**：
```python
# 增加缓冲区大小
logger = init_logger(
    buffer_size=50000,  # 增加5倍
    flush_interval=0.5  # 更频繁刷新
)

# 在关键位置手动刷新
logger.critical("重要操作完成")
logger.flush()
```

#### 问题3：性能问题

**症状**：
- 日志写入延迟高
- CPU占用率高
- 训练速度变慢

**诊断步骤**：
```python
# 1. 性能分析
import cProfile
import pstats

profiler = cProfile.Profile()
profiler.enable()

# 执行日志操作
for i in range(1000):
    logger.info(f"Test log {i}")

profiler.disable()
stats = pstats.Stats(profiler)
stats.sort_stats('cumtime')
stats.print_stats(10)

# 2. 检查I/O性能
health = logger.get_health_status()
print(f"I/O延迟: {health['performance_metrics']['io_latency_ms']}ms")
```

**解决方案**：
```python
# 优化配置
logger = init_logger(
    enable_async=True,      # 确保异步启用
    batch_size=500,        # 增加批量大小
    max_file_size=1024 * 1024 * 1024,  # 1GB，减少轮转
    console_output=False   # 关闭控制台输出
)

# 减少日志频率
if step % 100 == 0:  # 每100步记录一次
    logger.log_training_step(step, loss, reward)
```

### 5.2 错误代码参考

| 错误代码 | 描述 | 解决方案 |
|----------|------|----------|
| LOG001 | 日志目录创建失败 | 检查权限，手动创建目录 |
| LOG002 | 配置文件解析错误 | 验证YAML语法，检查缩进 |
| LOG003 | 异步队列溢出 | 增加buffer_size，减少日志频率 |
| LOG004 | 文件轮转失败 | 检查磁盘空间，清理旧日志 |
| LOG005 | GPU监控初始化失败 | 更新CUDA驱动，禁用GPU监控 |
| LOG006 | 健康检查超时 | 增加检查间隔，优化I/O性能 |
| LOG007 | 告警发送失败 | 检查网络连接，验证告警配置 |

### 5.3 日志恢复

#### 从损坏的日志文件恢复

```python
import json
import gzip

def recover_logs(corrupted_file, output_file):
    """恢复损坏的日志文件"""
    recovered_lines = []
    
    with open(corrupted_file, 'rb') as f:
        content = f.read()
        
    # 尝试按行分割
    lines = content.split(b'\n')
    
    for line in lines:
        try:
            # 尝试解析JSON
            if line.strip():
                data = json.loads(line)
                recovered_lines.append(data)
        except:
            # 记录无法恢复的行
            print(f"无法恢复: {line[:50]}...")
    
    # 保存恢复的数据
    with open(output_file, 'w') as f:
        for data in recovered_lines:
            f.write(json.dumps(data) + '\n')
    
    print(f"恢复了 {len(recovered_lines)} 行日志")

# 使用示例
recover_logs('logs/game/corrupted.log', 'logs/game/recovered.log')
```

## 6. 性能调优

### 6.1 性能基准

| 配置 | 吞吐量 | 延迟 | CPU | 内存 |
|------|--------|------|-----|------|
| 默认配置 | 10K/s | 2.5ms | 5% | 100MB |
| 高性能配置 | 50K/s | 0.5ms | 15% | 500MB |
| 低资源配置 | 5K/s | 5ms | 2% | 50MB |

### 6.2 优化建议

#### 高吞吐量场景

```python
# 配置示例
high_throughput_config = {
    "buffer_size": 100000,      # 大缓冲区
    "batch_size": 1000,         # 大批量
    "flush_interval": 5.0,      # 延迟刷新
    "console_output": False,    # 关闭控制台
    "compression": False,       # 关闭压缩
    "max_file_size": 5 * 1024 * 1024 * 1024  # 5GB
}

logger = init_logger(**high_throughput_config)
```

#### 低延迟场景

```python
# 配置示例
low_latency_config = {
    "buffer_size": 1000,        # 小缓冲区
    "batch_size": 10,           # 小批量
    "flush_interval": 0.1,      # 快速刷新
    "enable_async": False,      # 同步模式
    "console_output": True      # 实时查看
}

logger = init_logger(**low_latency_config)
```

#### 资源受限场景

```python
# 配置示例
low_resource_config = {
    "buffer_size": 5000,
    "batch_size": 50,
    "max_file_size": 100 * 1024 * 1024,  # 100MB
    "compression": True,         # 启用压缩
    "enable_gpu_monitoring": False,  # 关闭GPU监控
    "layers": {
        "debug": {"enabled": False},  # 关闭DEBUG层
        "games": {"enabled": False}   # 关闭详细游戏记录
    }
}

logger = init_logger(**low_resource_config)
```

### 6.3 性能监控

```python
# 实时性能监控
import time
import threading

class LoggerPerformanceMonitor:
    def __init__(self, logger, interval=60):
        self.logger = logger
        self.interval = interval
        self.running = True
        self.thread = threading.Thread(target=self._monitor)
        self.thread.daemon = True
        
    def start(self):
        self.thread.start()
        
    def stop(self):
        self.running = False
        
    def _monitor(self):
        last_count = 0
        while self.running:
            stats = self.logger.get_stats()
            current_count = stats['total_logs']
            
            # 计算吞吐量
            throughput = (current_count - last_count) / self.interval
            last_count = current_count
            
            # 记录性能指标
            self.logger.log_metric("logger_throughput", throughput)
            self.logger.log_metric("logger_buffer_usage", 
                                 stats.get('async_buffer_usage', 0))
            
            time.sleep(self.interval)

# 使用示例
monitor = LoggerPerformanceMonitor(logger)
monitor.start()
```

## 7. 监控和告警

### 7.1 健康检查

```python
# 定期健康检查
def perform_health_check():
    health = logger.force_health_check()
    
    # 检查整体状态
    if health['overall_status'] != 'healthy':
        print(f"警告: 日志系统状态异常 - {health['overall_status']}")
        
    # 检查各层健康
    for layer, status in health['layer_health'].items():
        if not status['is_healthy']:
            print(f"层 {layer} 异常: {status['issues']}")
            
    # 检查性能指标
    perf = health['performance_metrics']
    if perf['io_latency_ms'] > 10:
        print(f"I/O延迟过高: {perf['io_latency_ms']}ms")
        
    if perf['disk_usage_percent'] > 80:
        print(f"磁盘使用率过高: {perf['disk_usage_percent']}%")

# 设置定期检查
import schedule

schedule.every(5).minutes.do(perform_health_check)
```

### 7.2 告警规则

```python
from cardgame_ai.core.error_handling.monitoring import ErrorMonitor, AlertRule

# 获取错误监控器
monitor = ErrorMonitor()

# 添加自定义告警规则
monitor.add_rule(AlertRule(
    name="log_write_failure",
    description="日志写入失败率过高",
    condition=lambda data: data.get("log_error_rate", 0) > 0.01,
    channels=["log", "email"],
    severity="high",
    cooldown=300
))

# 配置告警通道
monitor.configure({
    "email": {
        "smtp_host": "smtp.gmail.com",
        "smtp_port": 587,
        "username": "<EMAIL>",
        "password": "your_password",
        "recipients": ["<EMAIL>"]
    }
})

monitor.start()
```

### 7.3 指标采集

```python
# Prometheus格式指标导出
def export_metrics():
    stats = logger.get_stats()
    health = logger.get_health_status()
    
    metrics = []
    
    # 日志统计
    metrics.append(f'log_total_count{{}} {stats["total_logs"]}')
    metrics.append(f'log_error_count{{}} {stats["error_logs"]}')
    
    # 性能指标
    if 'performance_metrics' in health:
        perf = health['performance_metrics']
        metrics.append(f'log_io_latency_ms{{}} {perf.get("io_latency_ms", 0)}')
        metrics.append(f'log_disk_usage_percent{{}} {perf.get("disk_usage_percent", 0)}')
    
    # GPU指标
    if 'gpu_0' in stats:
        gpu = stats['gpu_0']
        metrics.append(f'gpu_memory_usage_percent{{gpu="0"}} {gpu["memory_usage_percent"]}')
    
    return '\n'.join(metrics)

# 暴露HTTP端点
from flask import Flask, Response

app = Flask(__name__)

@app.route('/metrics')
def metrics():
    return Response(export_metrics(), mimetype='text/plain')

# app.run(host='0.0.0.0', port=9090)
```

## 8. 常见问题

### 8.1 FAQ

**Q: 如何完全禁用日志？**
```python
import logging
logging.disable(logging.CRITICAL)

# 或者使用环境变量
os.environ['LOG_LEVEL'] = 'CRITICAL'
os.environ['LOG_CONSOLE_OUTPUT'] = 'false'
```

**Q: 如何只记录错误日志？**
```python
logger = init_logger(level="ERROR")
```

**Q: 如何查看实时日志？**
```bash
# Linux/Mac
tail -f logs/training/training_*.log

# Windows PowerShell
Get-Content logs\training\training_*.log -Wait -Tail 10
```

**Q: 如何分析日志文件？**
```python
import pandas as pd
import json

def analyze_logs(log_file):
    records = []
    with open(log_file, 'r') as f:
        for line in f:
            try:
                records.append(json.loads(line))
            except:
                pass
    
    df = pd.DataFrame(records)
    
    # 分析示例
    print(f"总记录数: {len(df)}")
    print(f"时间范围: {df['timestamp'].min()} - {df['timestamp'].max()}")
    print(f"错误率: {(df['level'] == 'ERROR').sum() / len(df) * 100:.2f}%")
    
    return df
```

**Q: 日志文件太大怎么办？**
```python
# 1. 启用压缩
logger.create_game_archive(game_id, compress=True)

# 2. 减少保留时间
config = {
    "layers": {
        "debug": {"retention_hours": 12},  # 只保留12小时
        "training": {"retention_days": 3}   # 只保留3天
    }
}

# 3. 定期清理
import glob
import os
from datetime import datetime, timedelta

def cleanup_old_logs(days=7):
    cutoff = datetime.now() - timedelta(days=days)
    
    for log_file in glob.glob("logs/**/*.log", recursive=True):
        if os.path.getmtime(log_file) < cutoff.timestamp():
            os.remove(log_file)
            print(f"删除旧日志: {log_file}")
```

### 8.2 最佳实践

1. **结构化日志**
```python
# 好的做法
logger.info("用户登录", 
    user_id=123,
    ip="***********",
    success=True
)

# 避免
logger.info(f"User 123 logged in from ***********")
```

2. **适当的日志级别**
```python
logger.debug("函数入参", params=params)  # 调试信息
logger.info("请求处理完成", duration=0.5)  # 一般信息
logger.warning("内存使用率高", usage=85)  # 警告
logger.error("数据库连接失败", error=str(e))  # 错误
logger.critical("系统崩溃", traceback=tb)  # 严重错误
```

3. **避免敏感信息**
```python
# 错误示例
logger.info("用户登录", password=password)

# 正确做法
logger.info("用户登录", user_id=user_id, success=True)
```

4. **性能考虑**
```python
# 避免在循环中频繁记录
# 错误示例
for i in range(1000000):
    logger.debug(f"Processing item {i}")

# 正确做法
for i in range(1000000):
    if i % 10000 == 0:
        logger.debug(f"Processing progress: {i/1000000*100:.1f}%")
```

## 9. 维护任务

### 9.1 日常维护

#### 每日任务
- [ ] 检查健康状态报告
- [ ] 查看错误日志摘要
- [ ] 监控磁盘使用率
- [ ] 验证日志完整性

#### 每周任务
- [ ] 清理过期日志文件
- [ ] 分析性能趋势
- [ ] 更新告警规则
- [ ] 备份重要日志

#### 每月任务
- [ ] 性能基准测试
- [ ] 配置优化评估
- [ ] 日志分析报告
- [ ] 容量规划

### 9.2 维护脚本

```python
#!/usr/bin/env python3
"""日志系统维护脚本"""

import os
import sys
import time
from datetime import datetime, timedelta
from pathlib import Path

def daily_maintenance():
    """每日维护任务"""
    logger = get_logger()
    
    # 1. 健康检查
    health = logger.force_health_check()
    if health['overall_status'] != 'healthy':
        send_alert("日志系统健康检查失败", health)
    
    # 2. 清理临时文件
    temp_files = Path("logs").glob("**/*.tmp")
    for temp_file in temp_files:
        temp_file.unlink()
    
    # 3. 压缩昨天的日志
    yesterday = datetime.now() - timedelta(days=1)
    date_str = yesterday.strftime("%Y%m%d")
    
    for log_file in Path("logs").glob(f"**/*{date_str}*.log"):
        if not log_file.with_suffix('.log.gz').exists():
            compress_file(log_file)

def weekly_maintenance():
    """每周维护任务"""
    # 1. 清理过期日志
    cleanup_expired_logs()
    
    # 2. 生成性能报告
    generate_performance_report()
    
    # 3. 优化日志索引
    optimize_log_indices()

def cleanup_expired_logs():
    """清理过期日志"""
    retention_config = {
        "debug": 1,      # 天
        "training": 7,   # 天
        "game": 30,      # 天
        "games": 90      # 天
    }
    
    for layer, days in retention_config.items():
        layer_path = Path(f"logs/{layer}")
        if not layer_path.exists():
            continue
            
        cutoff = datetime.now() - timedelta(days=days)
        
        for log_file in layer_path.glob("**/*.log*"):
            if os.path.getmtime(log_file) < cutoff.timestamp():
                log_file.unlink()
                print(f"删除过期日志: {log_file}")

if __name__ == "__main__":
    # 根据参数执行不同的维护任务
    if len(sys.argv) > 1:
        task = sys.argv[1]
        if task == "daily":
            daily_maintenance()
        elif task == "weekly":
            weekly_maintenance()
    else:
        print("Usage: maintenance.py [daily|weekly]")
```

### 9.3 故障恢复流程

```mermaid
graph TD
    A[发现日志异常] --> B{健康检查}
    B -->|通过| C[记录并监控]
    B -->|失败| D[识别问题类型]
    
    D --> E{问题分类}
    E -->|文件损坏| F[尝试恢复文件]
    E -->|目录缺失| G[重建目录结构]
    E -->|性能问题| H[调整配置参数]
    E -->|空间不足| I[清理和归档]
    
    F --> J{恢复成功?}
    G --> J
    H --> J
    I --> J
    
    J -->|是| K[验证修复]
    J -->|否| L[启用备用方案]
    
    K --> M[恢复正常]
    L --> N[人工介入]
```

## 10. API参考

### 10.1 ConfigurableLogger类

```python
class ConfigurableLogger:
    """可配置的日志器类"""
    
    def __init__(self, **kwargs):
        """初始化日志器"""
        
    def log(self, level: str, message: str, **kwargs):
        """通用日志方法"""
        
    def debug(self, message: str, **kwargs):
        """记录DEBUG日志"""
        
    def info(self, message: str, **kwargs):
        """记录INFO日志"""
        
    def warning(self, message: str, **kwargs):
        """记录WARNING日志"""
        
    def error(self, message: str, **kwargs):
        """记录ERROR日志"""
        
    def critical(self, message: str, **kwargs):
        """记录CRITICAL日志"""
        
    def log_training_step(self, step: int, loss: float, **kwargs):
        """记录训练步骤"""
        
    def log_game_action(self, player_id: int, action: str, **kwargs):
        """记录游戏动作"""
        
    def log_metric(self, name: str, value: float, **kwargs):
        """记录指标"""
        
    def flush(self):
        """刷新日志缓冲"""
        
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        
    def shutdown(self):
        """关闭日志系统"""
```

### 10.2 全局函数

```python
def get_logger(name: str = None) -> ConfigurableLogger:
    """获取日志器实例"""
    
def init_logger(**kwargs) -> ConfigurableLogger:
    """初始化日志器"""
    
def debug(message: str, **kwargs):
    """全局DEBUG日志"""
    
def info(message: str, **kwargs):
    """全局INFO日志"""
    
def warning(message: str, **kwargs):
    """全局WARNING日志"""
    
def error(message: str, **kwargs):
    """全局ERROR日志"""
    
def critical(message: str, **kwargs):
    """全局CRITICAL日志"""
```

### 10.3 健康检查API

```python
class LogHealthChecker:
    """日志健康检查器"""
    
    def perform_health_check(self) -> LogSystemHealth:
        """执行健康检查"""
        
    def start(self):
        """启动持续监控"""
        
    def stop(self):
        """停止监控"""
        
    def get_health_report(self) -> Dict[str, Any]:
        """获取健康报告"""
        
    def force_health_check(self) -> LogSystemHealth:
        """强制执行健康检查"""
```

---

**维护指南结束**

如需更多帮助，请联系系统管理员或查看在线文档。