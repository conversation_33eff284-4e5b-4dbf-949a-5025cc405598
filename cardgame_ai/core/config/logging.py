"""
日志配置模块
统一管理所有日志相关配置
"""

from dataclasses import dataclass, field
from typing import Optional, Dict, Any, List
from enum import Enum
from .base import BaseConfig, ConfigRegistry

class LogLevel(str, Enum):
    """日志级别"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"
    
class LogFormat(str, Enum):
    """日志格式"""
    SIMPLE = "simple"
    DETAILED = "detailed"
    JSON = "json"
    CUSTOM = "custom"

@dataclass
class AsyncLoggerConfig(BaseConfig):
    """异步日志配置"""
    enable: bool = True
    buffer_size: int = 10000
    batch_size: int = 100
    flush_interval: float = 1.0
    max_workers: int = 2
    
@dataclass
class LogFileConfig(BaseConfig):
    """日志文件配置"""
    # 文件设置
    base_dir: str = "logs"
    filename_pattern: str = "{category}_{timestamp}.log"
    
    # 轮转设置
    max_size_mb: int = 100
    backup_count: int = 10
    encoding: str = "utf-8"
    
    # 分类设置
    categories: List[str] = field(default_factory=lambda: [
        "training", "mcts", "games", "metrics", "system"
    ])
    
    # 压缩设置
    compress_old_logs: bool = True
    compression_format: str = "gz"

@dataclass
class LoggingConfig(BaseConfig):
    """统一的日志配置"""
    # 基础设置
    level: LogLevel = LogLevel.INFO
    format: LogFormat = LogFormat.DETAILED
    
    # 输出设置
    console: bool = True
    file: bool = True
    
    # 文件配置
    file_config: LogFileConfig = field(default_factory=LogFileConfig)
    
    # 异步日志
    async_logger: AsyncLoggerConfig = field(default_factory=AsyncLoggerConfig)
    
    # 格式模板
    format_template: str = "[{timestamp}] [{level}] [{category}] [{module}] {message}"
    timestamp_format: str = "%Y-%m-%d %H:%M:%S"
    
    # 过滤设置
    module_levels: Dict[str, str] = field(default_factory=lambda: {
        "cardgame_ai.algorithms": "INFO",
        "cardgame_ai.training": "INFO",
        "cardgame_ai.games": "WARNING",
        "cardgame_ai.utils": "WARNING",
    })
    
    # 性能设置
    log_performance_metrics: bool = True
    metrics_interval: int = 100
    
    # 特殊日志
    log_gradients: bool = False
    log_weights: bool = False
    log_activations: bool = False
    
    def validate(self) -> bool:
        """验证配置有效性"""
        self.file_config.validate()
        self.async_logger.validate()
        
        # 验证模块级别
        valid_levels = [level.value for level in LogLevel]
        for module, level in self.module_levels.items():
            if level not in valid_levels:
                raise ValueError(f"Invalid log level for {module}: {level}")
                
        return True
        
    def get_level_value(self, level: str) -> int:
        """获取日志级别数值"""
        level_map = {
            "DEBUG": 10,
            "INFO": 20,
            "WARNING": 30,
            "ERROR": 40,
            "CRITICAL": 50
        }
        return level_map.get(level, 20)
        
    def should_log(self, module: str, level: str) -> bool:
        """判断是否应该记录日志"""
        # 获取模块级别
        module_level = self.level.value
        for pattern, configured_level in self.module_levels.items():
            if module.startswith(pattern):
                module_level = configured_level
                break
                
        # 比较级别
        return self.get_level_value(level) >= self.get_level_value(module_level)

@dataclass
class MetricsLoggingConfig(BaseConfig):
    """指标日志配置"""
    # WandB设置
    use_wandb: bool = False
    wandb_project: str = "cardgame-ai"
    wandb_entity: Optional[str] = None
    
    # TensorBoard设置
    use_tensorboard: bool = True
    tensorboard_dir: str = "runs"
    
    # 自定义指标
    custom_metrics: Dict[str, Any] = field(default_factory=dict)
    
    # 日志频率
    scalar_interval: int = 10
    histogram_interval: int = 100
    image_interval: int = 1000
    
    # 指标聚合
    aggregation_window: int = 100
    aggregation_method: str = "mean"  # mean, max, min, median

# 注册配置类
ConfigRegistry.register(AsyncLoggerConfig)
ConfigRegistry.register(LogFileConfig)
ConfigRegistry.register(LoggingConfig)
ConfigRegistry.register(MetricsLoggingConfig)
