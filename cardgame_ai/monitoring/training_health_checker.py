#!/usr/bin/env python3
"""
训练健康检查和自动恢复机制

提供训练过程的健康检查和自动恢复功能，包括：
- 进程存活检查
- 训练进度监控
- 资源使用检查
- 检查点完整性验证
- 自动恢复机制

作者：AI系统
创建时间：2025-07-05
"""

import os
import sys
import time
import json
import psutil
import torch
import threading
import subprocess
import shutil
import numpy as np
from typing import Dict, Any, Optional, List, Tuple, Callable
from datetime import datetime, timedelta
from pathlib import Path
from collections import deque, defaultdict
import warnings

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from cardgame_ai.utils.logging import get_logger
from cardgame_ai.core.interrupt_manager import InterruptManager
from cardgame_ai.monitoring.realtime_training_monitor import RealtimeTrainingMonitor
from cardgame_ai.core.monitoring.gpu_memory_manager import GPUMemoryManager

logger = get_logger(__name__)


class HealthStatus:
    """健康状态枚举"""
    HEALTHY = "healthy"
    WARNING = "warning"
    CRITICAL = "critical"
    DEAD = "dead"
    RECOVERING = "recovering"


class RecoveryAction:
    """恢复动作枚举"""
    NONE = "none"
    CLEAR_CACHE = "clear_cache"
    REDUCE_BATCH_SIZE = "reduce_batch_size"
    RESTART_FROM_CHECKPOINT = "restart_from_checkpoint"
    RESTART_PROCESS = "restart_process"
    EMERGENCY_SAVE = "emergency_save"


class TrainingHealthChecker:
    """训练健康检查器"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化健康检查器
        
        Args:
            config: 配置字典
        """
        self.config = config or {}
        self.logger = get_logger(__name__)
        
        # 中断管理器
        self.interrupt_manager = InterruptManager.get_instance()
        
        # 监控器集成
        self.training_monitor = None
        self.gpu_memory_manager = None
        
        # 健康检查配置
        self.check_interval = self.config.get('check_interval', 30.0)  # 检查间隔（秒）
        self.max_stagnation_time = self.config.get('max_stagnation_time', 600)  # 最大停滞时间（秒）
        self.max_recovery_attempts = self.config.get('max_recovery_attempts', 3)  # 最大恢复尝试次数
        self.checkpoint_dir = self.config.get('checkpoint_dir', 'checkpoints')
        
        # 健康阈值
        self.thresholds = {
            'min_progress_rate': self.config.get('min_progress_rate', 0.1),  # 最小进度率（episodes/分钟）
            'max_memory_usage': self.config.get('max_memory_usage', 90),     # 最大内存使用率（%）
            'max_gpu_memory': self.config.get('max_gpu_memory', 95),         # 最大GPU内存使用率（%）
            'min_checkpoint_interval': self.config.get('min_checkpoint_interval', 300),  # 最小检查点间隔（秒）
            'max_loss_nan_count': self.config.get('max_loss_nan_count', 5),  # 最大NaN损失次数
            'max_oom_count': self.config.get('max_oom_count', 3)             # 最大OOM次数
        }
        
        # 健康状态
        self.health_status = HealthStatus.HEALTHY
        self.last_check_time = time.time()
        self.recovery_attempts = 0
        self.health_history = deque(maxlen=100)
        
        # 进度跟踪
        self.last_episode_count = 0
        self.last_sample_count = 0
        self.last_checkpoint_time = 0
        self.stagnation_start_time = None
        
        # 错误计数
        self.nan_loss_count = 0
        self.oom_count = 0
        self.error_counts = defaultdict(int)
        
        # 检查线程
        self.check_thread = None
        self.checking_active = False
        
        # 恢复回调
        self.recovery_callbacks = []
        
        # 进程信息
        self.process_id = os.getpid()
        self.training_process = None
        self.training_command = None
        
        self.logger.info("训练健康检查器初始化完成")
    
    def start_checking(self, training_monitor: Optional[RealtimeTrainingMonitor] = None):
        """
        启动健康检查
        
        Args:
            training_monitor: 训练监控器实例
        """
        if self.checking_active:
            self.logger.warning("健康检查已在运行中")
            return
        
        self.training_monitor = training_monitor
        if not self.gpu_memory_manager:
            self.gpu_memory_manager = GPUMemoryManager()
        
        self.checking_active = True
        self.check_thread = threading.Thread(target=self._health_check_loop, daemon=True)
        self.check_thread.start()
        
        # 注册中断回调
        self.interrupt_manager.register_shutdown_callback(self._emergency_save)
        
        self.logger.info("健康检查已启动")
    
    def stop_checking(self):
        """停止健康检查"""
        if not self.checking_active:
            return
        
        self.checking_active = False
        if self.check_thread:
            self.check_thread.join(timeout=5.0)
        
        # 生成健康报告
        self._generate_health_report()
        
        self.logger.info("健康检查已停止")
    
    def _health_check_loop(self):
        """健康检查主循环"""
        while self.checking_active and not self.interrupt_manager.is_interrupted():
            try:
                # 执行健康检查
                health_result = self._perform_health_check()
                
                # 记录健康状态
                self.health_history.append({
                    'timestamp': time.time(),
                    'status': health_result['status'],
                    'metrics': health_result['metrics']
                })
                
                # 判断是否需要恢复
                if health_result['status'] in [HealthStatus.CRITICAL, HealthStatus.DEAD]:
                    recovery_action = self._determine_recovery_action(health_result)
                    if recovery_action != RecoveryAction.NONE:
                        self._execute_recovery(recovery_action, health_result)
                
                # 更新健康状态
                self.health_status = health_result['status']
                self.last_check_time = time.time()
                
            except Exception as e:
                self.logger.error(f"健康检查错误: {e}")
                self.error_counts['health_check_error'] += 1
            
            time.sleep(self.check_interval)
    
    def _perform_health_check(self) -> Dict[str, Any]:
        """
        执行健康检查
        
        Returns:
            健康检查结果
        """
        result = {
            'status': HealthStatus.HEALTHY,
            'metrics': {},
            'issues': []
        }
        
        # 1. 检查进程存活
        if not self._check_process_alive():
            result['status'] = HealthStatus.DEAD
            result['issues'].append('训练进程已死亡')
            return result
        
        # 2. 检查训练进度
        progress_status, progress_metrics = self._check_training_progress()
        result['metrics']['progress'] = progress_metrics
        
        if progress_status == 'stagnant':
            result['status'] = HealthStatus.WARNING
            result['issues'].append('训练进度停滞')
            
            # 检查停滞时间
            if self.stagnation_start_time:
                stagnation_duration = time.time() - self.stagnation_start_time
                if stagnation_duration > self.max_stagnation_time:
                    result['status'] = HealthStatus.CRITICAL
                    result['issues'].append(f'训练停滞超过{self.max_stagnation_time}秒')
        else:
            self.stagnation_start_time = None
        
        # 3. 检查资源使用
        resource_status, resource_metrics = self._check_resource_usage()
        result['metrics']['resources'] = resource_metrics
        
        if resource_status == 'critical':
            result['status'] = HealthStatus.CRITICAL
            result['issues'].append('资源使用异常')
        elif resource_status == 'warning' and result['status'] == HealthStatus.HEALTHY:
            result['status'] = HealthStatus.WARNING
        
        # 4. 检查训练指标
        if self.training_monitor:
            training_metrics = self._check_training_metrics()
            result['metrics']['training'] = training_metrics
            
            # 检查NaN损失
            if training_metrics.get('has_nan_loss', False):
                self.nan_loss_count += 1
                if self.nan_loss_count > self.thresholds['max_loss_nan_count']:
                    result['status'] = HealthStatus.CRITICAL
                    result['issues'].append(f'NaN损失次数过多: {self.nan_loss_count}')
        
        # 5. 检查检查点
        checkpoint_status = self._check_checkpoint_integrity()
        result['metrics']['checkpoint'] = checkpoint_status
        
        if not checkpoint_status['valid']:
            if result['status'] == HealthStatus.HEALTHY:
                result['status'] = HealthStatus.WARNING
            result['issues'].append('检查点验证失败')
        
        return result
    
    def _check_process_alive(self) -> bool:
        """检查进程是否存活"""
        try:
            if self.training_process:
                # 检查subprocess
                return self.training_process.poll() is None
            else:
                # 检查当前进程
                return psutil.pid_exists(self.process_id)
        except Exception as e:
            self.logger.error(f"检查进程存活失败: {e}")
            return False
    
    def _check_training_progress(self) -> Tuple[str, Dict[str, Any]]:
        """
        检查训练进度
        
        Returns:
            (状态, 指标字典)
        """
        metrics = {
            'episodes_per_minute': 0.0,
            'samples_per_minute': 0.0,
            'time_since_last_checkpoint': 0.0
        }
        
        if not self.training_monitor:
            return 'unknown', metrics
        
        # 获取当前统计
        stats = self.training_monitor.get_current_stats()
        current_episodes = stats.get('total_episodes', 0)
        current_samples = stats.get('total_samples', 0)
        
        # 计算进度率
        time_delta = time.time() - self.last_check_time
        if time_delta > 0:
            episodes_delta = current_episodes - self.last_episode_count
            samples_delta = current_samples - self.last_sample_count
            
            metrics['episodes_per_minute'] = (episodes_delta / time_delta) * 60
            metrics['samples_per_minute'] = (samples_delta / time_delta) * 60
        
        # 更新计数
        self.last_episode_count = current_episodes
        self.last_sample_count = current_samples
        
        # 检查是否停滞
        if metrics['episodes_per_minute'] < self.thresholds['min_progress_rate']:
            if self.stagnation_start_time is None:
                self.stagnation_start_time = time.time()
            return 'stagnant', metrics
        
        # 检查检查点时间
        metrics['time_since_last_checkpoint'] = time.time() - self.last_checkpoint_time
        
        return 'normal', metrics
    
    def _check_resource_usage(self) -> Tuple[str, Dict[str, Any]]:
        """
        检查资源使用情况
        
        Returns:
            (状态, 指标字典)
        """
        metrics = {
            'cpu_percent': 0.0,
            'memory_percent': 0.0,
            'gpu_memory_percent': 0.0,
            'disk_usage_percent': 0.0
        }
        
        status = 'normal'
        
        try:
            # CPU和内存
            process = psutil.Process(self.process_id)
            metrics['cpu_percent'] = process.cpu_percent(interval=0.1)
            metrics['memory_percent'] = process.memory_percent()
            
            # 检查内存使用
            if metrics['memory_percent'] > self.thresholds['max_memory_usage']:
                status = 'critical'
                self.logger.warning(f"内存使用过高: {metrics['memory_percent']:.1f}%")
            
            # GPU内存
            if torch.cuda.is_available() and self.gpu_memory_manager:
                _, _, total = self.gpu_memory_manager.get_gpu_memory_info()
                if total > 0:
                    used = torch.cuda.memory_allocated() / (1024**3)
                    metrics['gpu_memory_percent'] = (used / total) * 100
                    
                    if metrics['gpu_memory_percent'] > self.thresholds['max_gpu_memory']:
                        status = 'critical'
                        self.logger.warning(f"GPU内存使用过高: {metrics['gpu_memory_percent']:.1f}%")
            
            # 磁盘使用
            disk_usage = psutil.disk_usage(self.checkpoint_dir)
            metrics['disk_usage_percent'] = disk_usage.percent
            
            if metrics['disk_usage_percent'] > 90:
                status = 'warning' if status == 'normal' else status
                self.logger.warning(f"磁盘空间不足: {metrics['disk_usage_percent']:.1f}%")
            
        except Exception as e:
            self.logger.error(f"资源检查失败: {e}")
            status = 'unknown'
        
        return status, metrics
    
    def _check_training_metrics(self) -> Dict[str, Any]:
        """检查训练指标"""
        metrics = {
            'has_nan_loss': False,
            'loss_trend': 'unknown',
            'win_rate': 0.0
        }
        
        if not self.training_monitor:
            return metrics
        
        stats = self.training_monitor.get_current_stats()
        
        # 检查损失
        if 'loss' in stats.get('metrics', {}):
            loss_data = stats['metrics']['loss']
            if loss_data.get('value') is not None:
                if np.isnan(loss_data['value']) or np.isinf(loss_data['value']):
                    metrics['has_nan_loss'] = True
                metrics['loss_trend'] = loss_data.get('trend', 'unknown')
        
        # 检查胜率
        if 'win_rate' in stats.get('metrics', {}):
            win_rate_data = stats['metrics']['win_rate']
            metrics['win_rate'] = win_rate_data.get('value', 0.0)
        
        return metrics
    
    def _check_checkpoint_integrity(self) -> Dict[str, Any]:
        """检查检查点完整性"""
        result = {
            'valid': True,
            'latest_checkpoint': None,
            'checkpoint_age': None,
            'size_mb': 0
        }
        
        try:
            checkpoint_path = Path(self.checkpoint_dir)
            if not checkpoint_path.exists():
                result['valid'] = False
                return result
            
            # 查找最新的检查点
            checkpoints = list(checkpoint_path.glob("*.pth")) + list(checkpoint_path.glob("*.pt"))
            if not checkpoints:
                result['valid'] = False
                return result
            
            latest_checkpoint = max(checkpoints, key=lambda p: p.stat().st_mtime)
            result['latest_checkpoint'] = str(latest_checkpoint)
            
            # 检查文件大小
            size_bytes = latest_checkpoint.stat().st_size
            result['size_mb'] = size_bytes / (1024 * 1024)
            
            if result['size_mb'] < 0.1:  # 小于100KB，可能是损坏的
                result['valid'] = False
                return result
            
            # 检查年龄
            age_seconds = time.time() - latest_checkpoint.stat().st_mtime
            result['checkpoint_age'] = age_seconds
            
            # 尝试加载验证
            try:
                # 首先尝试安全加载
                try:
                    checkpoint = torch.load(latest_checkpoint, map_location='cpu', weights_only=True)
                except Exception:
                    # 如果安全加载失败，使用不安全加载（Python 3.13兼容）
                    checkpoint = torch.load(latest_checkpoint, map_location='cpu', weights_only=False)
                
                if not isinstance(checkpoint, dict):
                    result['valid'] = False
                elif 'model_state_dict' not in checkpoint and 'state_dict' not in checkpoint and 'algorithm_data' not in checkpoint:
                    # 更宽松的验证条件，支持更多格式
                    result['valid'] = False
            except Exception as e:
                self.logger.error(f"检查点加载失败: {e}")
                result['valid'] = False
            
        except Exception as e:
            self.logger.error(f"检查点验证失败: {e}")
            result['valid'] = False
        
        return result
    
    def _determine_recovery_action(self, health_result: Dict[str, Any]) -> str:
        """
        确定恢复动作
        
        Args:
            health_result: 健康检查结果
            
        Returns:
            恢复动作
        """
        status = health_result['status']
        issues = health_result['issues']
        metrics = health_result['metrics']
        
        # 如果已经尝试太多次，停止恢复
        if self.recovery_attempts >= self.max_recovery_attempts:
            self.logger.error("达到最大恢复尝试次数，停止自动恢复")
            return RecoveryAction.NONE
        
        # 进程死亡
        if status == HealthStatus.DEAD:
            return RecoveryAction.RESTART_PROCESS
        
        # 资源问题
        if 'resources' in metrics:
            resources = metrics['resources']
            
            # GPU内存过高
            if resources.get('gpu_memory_percent', 0) > self.thresholds['max_gpu_memory']:
                self.oom_count += 1
                if self.oom_count <= self.thresholds['max_oom_count']:
                    return RecoveryAction.REDUCE_BATCH_SIZE
                else:
                    return RecoveryAction.CLEAR_CACHE
            
            # 内存过高
            if resources.get('memory_percent', 0) > self.thresholds['max_memory_usage']:
                return RecoveryAction.CLEAR_CACHE
        
        # 训练停滞
        if '训练停滞' in str(issues):
            return RecoveryAction.RESTART_FROM_CHECKPOINT
        
        # NaN损失
        if 'NaN损失' in str(issues):
            return RecoveryAction.RESTART_FROM_CHECKPOINT
        
        return RecoveryAction.NONE
    
    def _execute_recovery(self, action: str, health_result: Dict[str, Any]):
        """
        执行恢复动作
        
        Args:
            action: 恢复动作
            health_result: 健康检查结果
        """
        self.logger.info(f"执行恢复动作: {action}")
        self.health_status = HealthStatus.RECOVERING
        self.recovery_attempts += 1
        
        recovery_log = {
            'timestamp': datetime.now().isoformat(),
            'action': action,
            'health_result': health_result,
            'attempt': self.recovery_attempts,
            'success': False,
            'error': None
        }
        
        try:
            if action == RecoveryAction.CLEAR_CACHE:
                self._clear_cache()
                recovery_log['success'] = True
                
            elif action == RecoveryAction.REDUCE_BATCH_SIZE:
                self._reduce_batch_size()
                recovery_log['success'] = True
                
            elif action == RecoveryAction.RESTART_FROM_CHECKPOINT:
                self._restart_from_checkpoint()
                recovery_log['success'] = True
                
            elif action == RecoveryAction.RESTART_PROCESS:
                self._restart_training_process()
                recovery_log['success'] = True
                
            elif action == RecoveryAction.EMERGENCY_SAVE:
                self._emergency_save()
                recovery_log['success'] = True
            
            # 执行恢复回调
            for callback in self.recovery_callbacks:
                try:
                    callback(action, recovery_log)
                except Exception as e:
                    self.logger.error(f"恢复回调执行失败: {e}")
            
        except Exception as e:
            self.logger.error(f"恢复动作执行失败: {e}")
            recovery_log['error'] = str(e)
            self.error_counts[f'recovery_{action}_failed'] += 1
        
        # 记录恢复日志
        self._log_recovery_action(recovery_log)
        
        # 如果恢复成功，重置计数器
        if recovery_log['success']:
            self.logger.info(f"恢复动作 {action} 执行成功")
            if action in [RecoveryAction.RESTART_FROM_CHECKPOINT, RecoveryAction.RESTART_PROCESS]:
                self.nan_loss_count = 0
                self.oom_count = 0
    
    def _clear_cache(self):
        """清理缓存"""
        self.logger.info("清理缓存和临时文件...")
        
        # 清理PyTorch缓存
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
            torch.cuda.synchronize()
        
        # 清理Python垃圾
        import gc
        gc.collect()
        
        # 清理临时文件
        temp_dirs = [
            Path("/tmp") / "pytorch_*",
            Path.home() / ".cache" / "torch",
            Path(self.checkpoint_dir) / "temp_*"
        ]
        
        for pattern in temp_dirs:
            for temp_file in Path(pattern.parent).glob(pattern.name):
                try:
                    if temp_file.is_file():
                        temp_file.unlink()
                    elif temp_file.is_dir():
                        shutil.rmtree(temp_file)
                except Exception as e:
                    self.logger.debug(f"清理临时文件失败: {e}")
        
        self.logger.info("缓存清理完成")
    
    def _reduce_batch_size(self):
        """减小批次大小"""
        self.logger.info("尝试减小批次大小...")
        
        # 如果有GPU内存管理器，使用它来计算新的批次大小
        if self.gpu_memory_manager:
            current_batch_size = self.config.get('batch_size', 32)
            new_batch_size = self.gpu_memory_manager.calculate_optimal_batch_size(current_batch_size)
            
            # 更新配置
            self.config['batch_size'] = new_batch_size
            
            # 写入配置文件
            self._update_training_config({'batch_size': new_batch_size})
            
            self.logger.info(f"批次大小已调整: {current_batch_size} -> {new_batch_size}")
        else:
            # 简单减半
            current_batch_size = self.config.get('batch_size', 32)
            new_batch_size = max(1, current_batch_size // 2)
            self.config['batch_size'] = new_batch_size
            
            self._update_training_config({'batch_size': new_batch_size})
            
            self.logger.info(f"批次大小已减半: {current_batch_size} -> {new_batch_size}")
    
    def _restart_from_checkpoint(self):
        """从检查点重启训练"""
        self.logger.info("从最新检查点重启训练...")
        
        # 检查是否有有效的检查点
        checkpoint_status = self._check_checkpoint_integrity()
        if not checkpoint_status['valid']:
            raise RuntimeError("没有有效的检查点可以恢复")
        
        # 设置恢复标志
        self._update_training_config({
            'resume_from_checkpoint': checkpoint_status['latest_checkpoint'],
            'recovery_mode': True
        })
        
        # 如果有训练进程，先停止它
        if self.training_process:
            self._stop_training_process()
        
        # 重启训练
        self._start_training_process()
        
        self.logger.info(f"已从检查点恢复: {checkpoint_status['latest_checkpoint']}")
    
    def _restart_training_process(self):
        """重启训练进程"""
        self.logger.info("重启训练进程...")
        
        # 停止当前进程
        if self.training_process:
            self._stop_training_process()
        
        # 等待进程完全停止
        time.sleep(5)
        
        # 启动新进程
        self._start_training_process()
        
        self.logger.info("训练进程已重启")
    
    def _stop_training_process(self):
        """停止训练进程"""
        if not self.training_process:
            return
        
        try:
            # 发送终止信号
            self.training_process.terminate()
            
            # 等待进程结束
            try:
                self.training_process.wait(timeout=30)
            except subprocess.TimeoutExpired:
                # 强制终止
                self.training_process.kill()
                self.training_process.wait()
            
            self.training_process = None
            self.logger.info("训练进程已停止")
            
        except Exception as e:
            self.logger.error(f"停止训练进程失败: {e}")
    
    def _start_training_process(self):
        """启动训练进程"""
        if not self.training_command:
            # 默认训练命令
            self.training_command = [
                sys.executable,
                "cardgame_ai/zhuchengxu/auto_deploy.py"
            ]
        
        try:
            self.training_process = subprocess.Popen(
                self.training_command,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                cwd=str(project_root)
            )
            
            self.process_id = self.training_process.pid
            self.logger.info(f"训练进程已启动，PID: {self.process_id}")
            
        except Exception as e:
            self.logger.error(f"启动训练进程失败: {e}")
            raise
    
    def _emergency_save(self):
        """紧急保存"""
        self.logger.warning("执行紧急保存...")
        
        try:
            # 创建紧急保存目录
            emergency_dir = Path(self.checkpoint_dir) / "emergency"
            emergency_dir.mkdir(exist_ok=True)
            
            # 生成时间戳
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # 保存当前状态
            emergency_data = {
                'timestamp': timestamp,
                'health_status': self.health_status,
                'health_history': list(self.health_history),
                'error_counts': dict(self.error_counts),
                'recovery_attempts': self.recovery_attempts,
                'last_checkpoint': None
            }
            
            # 查找最新检查点
            checkpoint_status = self._check_checkpoint_integrity()
            if checkpoint_status['valid']:
                # 复制检查点到紧急目录
                src = Path(checkpoint_status['latest_checkpoint'])
                dst = emergency_dir / f"emergency_checkpoint_{timestamp}.pth"
                shutil.copy2(src, dst)
                emergency_data['last_checkpoint'] = str(dst)
            
            # 保存紧急数据
            emergency_file = emergency_dir / f"emergency_state_{timestamp}.json"
            with open(emergency_file, 'w') as f:
                json.dump(emergency_data, f, indent=2)
            
            self.logger.info(f"紧急保存完成: {emergency_file}")
            
        except Exception as e:
            self.logger.error(f"紧急保存失败: {e}")
    
    def _update_training_config(self, updates: Dict[str, Any]):
        """更新训练配置"""
        config_file = Path(self.config.get('config_file', 'training_config.json'))
        
        try:
            # 读取现有配置
            if config_file.exists():
                with open(config_file, 'r') as f:
                    config = json.load(f)
            else:
                config = {}
            
            # 更新配置
            config.update(updates)
            
            # 保存配置
            with open(config_file, 'w') as f:
                json.dump(config, f, indent=2)
            
            self.logger.info(f"训练配置已更新: {updates}")
            
        except Exception as e:
            self.logger.error(f"更新训练配置失败: {e}")
    
    def _log_recovery_action(self, recovery_log: Dict[str, Any]):
        """记录恢复动作"""
        log_dir = Path(self.checkpoint_dir) / "recovery_logs"
        log_dir.mkdir(exist_ok=True)
        
        log_file = log_dir / f"recovery_{datetime.now().strftime('%Y%m%d')}.jsonl"
        
        try:
            with open(log_file, 'a') as f:
                f.write(json.dumps(recovery_log) + '\n')
        except Exception as e:
            self.logger.error(f"记录恢复日志失败: {e}")
    
    def _generate_health_report(self):
        """生成健康报告"""
        report = {
            'timestamp': datetime.now().isoformat(),
            'total_checks': len(self.health_history),
            'health_summary': self._calculate_health_summary(),
            'recovery_summary': {
                'total_attempts': self.recovery_attempts,
                'actions_taken': self._get_recovery_action_summary()
            },
            'error_summary': dict(self.error_counts),
            'recommendations': self._generate_recommendations()
        }
        
        # 保存报告
        report_file = Path(self.checkpoint_dir) / f"health_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        try:
            with open(report_file, 'w') as f:
                json.dump(report, f, indent=2)
            
            self.logger.info(f"健康报告已生成: {report_file}")
        except Exception as e:
            self.logger.error(f"生成健康报告失败: {e}")
    
    def _calculate_health_summary(self) -> Dict[str, Any]:
        """计算健康摘要"""
        if not self.health_history:
            return {}
        
        status_counts = defaultdict(int)
        for entry in self.health_history:
            status_counts[entry['status']] += 1
        
        total = len(self.health_history)
        return {
            'healthy_percentage': (status_counts[HealthStatus.HEALTHY] / total * 100) if total > 0 else 0,
            'warning_percentage': (status_counts[HealthStatus.WARNING] / total * 100) if total > 0 else 0,
            'critical_percentage': (status_counts[HealthStatus.CRITICAL] / total * 100) if total > 0 else 0,
            'status_counts': dict(status_counts)
        }
    
    def _get_recovery_action_summary(self) -> Dict[str, int]:
        """获取恢复动作摘要"""
        # 从恢复日志中统计
        action_counts = defaultdict(int)
        
        log_dir = Path(self.checkpoint_dir) / "recovery_logs"
        if log_dir.exists():
            for log_file in log_dir.glob("*.jsonl"):
                try:
                    with open(log_file, 'r') as f:
                        for line in f:
                            entry = json.loads(line)
                            action_counts[entry['action']] += 1
                except Exception:
                    pass
        
        return dict(action_counts)
    
    def _generate_recommendations(self) -> List[str]:
        """生成优化建议"""
        recommendations = []
        
        # 基于错误计数
        if self.oom_count > 0:
            recommendations.append(f"发生{self.oom_count}次OOM，建议减小批次大小或优化内存使用")
        
        if self.nan_loss_count > 0:
            recommendations.append(f"发生{self.nan_loss_count}次NaN损失，建议检查学习率和梯度裁剪")
        
        # 基于健康历史
        health_summary = self._calculate_health_summary()
        if health_summary.get('critical_percentage', 0) > 10:
            recommendations.append("系统频繁进入危急状态，建议检查训练配置")
        
        if health_summary.get('warning_percentage', 0) > 30:
            recommendations.append("系统经常出现警告，建议优化资源配置")
        
        # 基于恢复尝试
        if self.recovery_attempts > self.max_recovery_attempts / 2:
            recommendations.append("恢复尝试次数较多，建议人工介入检查")
        
        return recommendations
    
    def register_recovery_callback(self, callback: Callable[[str, Dict[str, Any]], None]):
        """
        注册恢复回调函数
        
        Args:
            callback: 回调函数，接收(action, recovery_log)参数
        """
        self.recovery_callbacks.append(callback)
    
    def set_training_command(self, command: List[str]):
        """
        设置训练命令
        
        Args:
            command: 训练命令列表
        """
        self.training_command = command
    
    def get_health_status(self) -> Dict[str, Any]:
        """获取当前健康状态"""
        return {
            'status': self.health_status,
            'last_check_time': self.last_check_time,
            'recovery_attempts': self.recovery_attempts,
            'error_counts': dict(self.error_counts),
            'is_checking': self.checking_active
        }
    
    def manual_recovery(self, action: str) -> bool:
        """
        手动触发恢复动作
        
        Args:
            action: 恢复动作
            
        Returns:
            是否成功
        """
        self.logger.info(f"手动触发恢复动作: {action}")
        
        try:
            # 构造健康结果
            health_result = {
                'status': HealthStatus.CRITICAL,
                'metrics': {},
                'issues': [f'手动触发: {action}']
            }
            
            # 执行恢复
            self._execute_recovery(action, health_result)
            return True
            
        except Exception as e:
            self.logger.error(f"手动恢复失败: {e}")
            return False


def create_health_checker_with_defaults() -> TrainingHealthChecker:
    """创建带默认配置的健康检查器"""
    config = {
        'check_interval': 30.0,
        'max_stagnation_time': 600,
        'max_recovery_attempts': 3,
        'checkpoint_dir': 'checkpoints',
        'min_progress_rate': 0.1,
        'max_memory_usage': 90,
        'max_gpu_memory': 95,
        'min_checkpoint_interval': 300,
        'max_loss_nan_count': 5,
        'max_oom_count': 3
    }
    
    return TrainingHealthChecker(config)


if __name__ == "__main__":
    """测试健康检查器"""
    import argparse
    
    parser = argparse.ArgumentParser(description="训练健康检查器")
    parser.add_argument('--test', action='store_true', help='运行测试模式')
    parser.add_argument('--monitor', action='store_true', help='启动监控模式')
    parser.add_argument('--command', nargs='+', help='训练命令')
    
    args = parser.parse_args()
    
    if args.test:
        # 测试模式
        print("启动健康检查器测试...")
        checker = create_health_checker_with_defaults()
        
        # 模拟训练监控器
        from cardgame_ai.monitoring.realtime_training_monitor import create_training_monitor_with_callbacks
        monitor = create_training_monitor_with_callbacks()
        monitor.start_monitoring()
        
        # 启动健康检查
        checker.start_checking(monitor)
        
        try:
            # 模拟训练过程
            for i in range(20):
                # 更新训练指标
                monitor.update_training_metrics(
                    loss=1.0 / (i + 1),
                    win_rate=50 + i * 2,
                    episodes=i * 10,
                    samples=i * 1000
                )
                
                # 偶尔触发问题
                if i == 10:
                    # 模拟NaN损失
                    monitor.update_training_metrics(loss=float('nan'))
                
                time.sleep(3)
                
                # 打印健康状态
                status = checker.get_health_status()
                print(f"\n步骤 {i}: 健康状态 = {status['status']}")
                
        except KeyboardInterrupt:
            print("\n停止测试...")
        finally:
            checker.stop_checking()
            monitor.stop_monitoring()
    
    elif args.monitor:
        # 监控模式
        checker = create_health_checker_with_defaults()
        
        if args.command:
            checker.set_training_command(args.command)
        
        print("启动健康监控...")
        checker.start_checking()
        
        try:
            while True:
                time.sleep(10)
                status = checker.get_health_status()
                print(f"健康状态: {status['status']} (恢复尝试: {status['recovery_attempts']})")
        except KeyboardInterrupt:
            print("\n停止监控...")
        finally:
            checker.stop_checking()
    
    else:
        parser.print_help()