"""
斗地主状态管理模块基础导入
"""

import numpy as np
from typing import Dict, List, Tuple, Optional, Any, Set
from enum import Enum
from dataclasses import dataclass
from cardgame_ai.utils.logging import get_logger

logger = get_logger(__name__)

from .state_types import GamePhase, PlayerRole, GameScore
from .card_manager import CardManager

class StateManager:
    """状态管理器"""
    
    def __init__(self):
        """初始化状态管理器"""
        self.phase = GamePhase.BIDDING
        self.current_player = "farmer1"
        self.landlord = None
        self.turn_count = 0
        self.game_score = GameScore()
        self.card_manager = CardManager()
        self.history = []
        
        logger.info("状态管理器初始化")
    
    def start_bidding_phase(self):
        """开始叫地主阶段"""
        self.phase = GamePhase.BIDDING
        self.current_player = "farmer1"
        self.card_manager.shuffle_and_deal()
        logger.info("进入叫地主阶段")
    
    def set_landlord(self, player: str):
        """设置地主"""
        self.landlord = player
        self.card_manager.add_bottom_cards_to_landlord()
        self.phase = GamePhase.PLAYING
        self.current_player = self.landlord
        logger.info(f"地主确定: {player}")
    
    def next_turn(self):
        """下一回合"""
        players = ["landlord", "farmer1", "farmer2"]
        current_idx = players.index(self.current_player)
        self.current_player = players[(current_idx + 1) % 3]
        self.turn_count += 1
        logger.debug(f"轮到: {self.current_player}")
    
    def is_game_finished(self) -> bool:
        """判断游戏是否结束"""
        for player in ["landlord", "farmer1", "farmer2"]:
            if len(self.card_manager.get_hand_cards(player)) == 0:
                self.phase = GamePhase.FINISHED
                return True
        return False
    
    def get_winner(self) -> Optional[str]:
        """获取获胜者"""
        if not self.is_game_finished():
            return None
            
        # 检查谁先出完牌
        for player in ["landlord", "farmer1", "farmer2"]:
            if len(self.card_manager.get_hand_cards(player)) == 0:
                if player == "landlord":
                    return "landlord"
                else:
                    return "farmers"
        return None
    
    def get_state_vector(self) -> np.ndarray:
        """获取状态向量"""
        # 简化的状态向量
        state = np.zeros(656)  # 斗地主标准状态空间
        
        # 编码游戏阶段
        if self.phase == GamePhase.BIDDING:
            state[0] = 1
        elif self.phase == GamePhase.PLAYING:
            state[1] = 1
        else:
            state[2] = 1
        
        # 编码当前玩家
        if self.current_player == "landlord":
            state[3] = 1
        elif self.current_player == "farmer1":
            state[4] = 1
        else:
            state[5] = 1
        
        # 编码回合数
        state[6] = min(self.turn_count / 100.0, 1.0)
        
        return state
