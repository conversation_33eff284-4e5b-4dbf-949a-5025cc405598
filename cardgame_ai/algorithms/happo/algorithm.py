"""
HAPPO模块基础导入
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
from cardgame_ai.utils.logging import get_logger
from ..mappo import MAPPO, MAPPOConfig
from ..role_specific_mappo import RoleSpecificMAPPO
from ...core.base import State, Action
from ...games.doudizhu.state_modules import Do<PERSON>zhuState

logger = get_logger(__name__)

from .config import HAPPOConfig
from .communication import FarmerCommunicationModule
from .advantage_decomposer import HAPPOAdvantageDecomposer
from .reward_shaper import CooperationRewardShaper

class HAPPOAlgorithm(MAPPO):
    """HAPPO算法主类"""
    
    def __init__(self, config, obs_shape=(400,), act_shape=(2300,), global_state_shape=None):
        """初始化HAPPO算法"""
        # 处理配置参数类型
        if isinstance(config, dict):
            # 如果传入的是字典，创建HAPPOConfig对象
            happo_config = HAPPOConfig()
            # 从字典中提取形状参数
            if 'observation_shape' in config:
                obs_shape = config['observation_shape']
            if 'action_space_size' in config:
                act_shape = (config['action_space_size'],)
        else:
            # 如果传入的是HAPPOConfig对象
            happo_config = config
        
        # 调用MAPPO的构造函数，使用位置参数
        super().__init__(
            obs_shape,  # 位置参数1
            act_shape,  # 位置参数2
            global_state_shape,  # 位置参数3
            hidden_dims=[happo_config.farmer_hidden_dim, 128],  # 使用配置中的隐藏层维度
            learning_rate=happo_config.lr,
            gamma=happo_config.gamma,
            gae_lambda=happo_config.gae_lambda,
            clip_ratio=happo_config.clip_epsilon,
            value_coef=happo_config.value_loss_coef,
            entropy_coef=happo_config.entropy_coef,
            max_grad_norm=happo_config.max_grad_norm,
            update_epochs=happo_config.ppo_epochs,
            batch_size=happo_config.mini_batch_size,
            use_central_critic=happo_config.use_central_critic
        )
        self.config = happo_config
        
        # 确保可以访问PPO参数（使用不同的名称避免冲突）
        self.happo_clip_ratio = happo_config.clip_epsilon
        self.happo_value_coef = happo_config.value_loss_coef
        self.happo_entropy_coef = happo_config.entropy_coef
        self.happo_max_grad_norm = happo_config.max_grad_norm
        self.happo_gamma = happo_config.gamma
        self.happo_gae_lambda = happo_config.gae_lambda
        
        # 初始化HAPPO特有组件
        if happo_config.enable_farmer_communication:
            self.communication_module = FarmerCommunicationModule(
                hidden_dim=happo_config.farmer_hidden_dim,
                communication_dim=happo_config.communication_dim,
                num_rounds=happo_config.communication_rounds
            )
        
        self.advantage_decomposer = HAPPOAdvantageDecomposer(
            method=happo_config.advantage_decomposition,
            temperature=happo_config.credit_temperature
        )
        
        self.reward_shaper = CooperationRewardShaper(
            cooperation_weight=happo_config.cooperation_reward_weight,
            synergy_bonus=happo_config.synergy_bonus
        )
        
        logger.info("HAPPO算法初始化完成")
    
    def _validate_and_fix_actions(self, actions, agent_id):
        """验证和修复动作数据"""
        if isinstance(actions, np.ndarray):
            # 检查动作形状
            if actions.ndim > 1:
                logger.warning(f"智能体 {agent_id} 的动作维度过高 {actions.shape}，展平为1维")
                actions = actions.flatten()
            
            # 检查动作范围
            max_action = self.act_dim - 1
            invalid_mask = (actions < 0) | (actions > max_action)
            if np.any(invalid_mask):
                num_invalid = np.sum(invalid_mask)
                logger.warning(f"智能体 {agent_id} 有 {num_invalid} 个无效动作，将其限制在有效范围内")
                actions = np.clip(actions, 0, max_action)
        
        elif isinstance(actions, torch.Tensor):
            # 检查动作形状
            if actions.dim() > 1:
                logger.warning(f"智能体 {agent_id} 的动作维度过高 {actions.shape}，展平为1维")
                actions = actions.flatten()
            
            # 检查动作范围
            max_action = self.act_dim - 1
            invalid_mask = (actions < 0) | (actions > max_action)
            if torch.any(invalid_mask):
                num_invalid = torch.sum(invalid_mask).item()
                logger.warning(f"智能体 {agent_id} 有 {num_invalid} 个无效动作，将其限制在有效范围内")
                actions = torch.clamp(actions, 0, max_action)
        
        return actions
    
    def _safe_gather_log_probs(self, log_probs_tensor, actions_tensor):
        """安全的对数概率gather操作"""
        try:
            # 确保张量维度正确
            if log_probs_tensor.dim() != 2:
                raise ValueError(f"log_probs_tensor应该是2维，但得到 {log_probs_tensor.dim()}维")
            
            if actions_tensor.dim() != 1:
                if actions_tensor.dim() == 0:
                    actions_tensor = actions_tensor.unsqueeze(0)
                else:
                    actions_tensor = actions_tensor.flatten()
            
            # 验证动作范围
            max_action = log_probs_tensor.size(1) - 1
            if torch.any(actions_tensor < 0) or torch.any(actions_tensor > max_action):
                logger.warning(f"动作超出范围 [0, {max_action}]，进行裁剪")
                actions_tensor = torch.clamp(actions_tensor, 0, max_action)
            
            # 确保批次大小匹配
            if log_probs_tensor.size(0) != actions_tensor.size(0):
                min_batch_size = min(log_probs_tensor.size(0), actions_tensor.size(0))
                logger.warning(f"批次大小不匹配，调整为 {min_batch_size}")
                log_probs_tensor = log_probs_tensor[:min_batch_size]
                actions_tensor = actions_tensor[:min_batch_size]
            
            # 执行gather操作
            gathered_log_probs = log_probs_tensor.gather(1, actions_tensor.unsqueeze(1)).squeeze(1)
            
            return gathered_log_probs.cpu().numpy()
            
        except Exception as e:
            logger.error(f"gather操作失败: {e}")
            # 返回默认值
            batch_size = actions_tensor.size(0) if isinstance(actions_tensor, torch.Tensor) else len(actions_tensor)
            return np.full(batch_size, -2.0, dtype=np.float32)  # 默认对数概率
    
    def _validate_tensor_dimensions(self, obs, actions, rewards, old_values, old_log_probs, agent_id):
        """验证和修复张量维度"""
        try:
            # 获取批次大小
            batch_sizes = []
            if obs.numel() > 0:
                batch_sizes.append(obs.size(0))
            if actions.numel() > 0:
                batch_sizes.append(actions.size(0) if actions.dim() > 0 else 1)
            if rewards.numel() > 0:
                batch_sizes.append(rewards.size(0) if rewards.dim() > 0 else 1)
            if old_values.numel() > 0:
                batch_sizes.append(old_values.size(0) if old_values.dim() > 0 else 1)
            if old_log_probs.numel() > 0:
                batch_sizes.append(old_log_probs.size(0) if old_log_probs.dim() > 0 else 1)
            
            # 确定有效的批次大小
            if not batch_sizes:
                raise ValueError("所有张量都为空")
            
            target_batch_size = max(set(batch_sizes), key=batch_sizes.count)  # 众数
            
            # 调整张量维度
            # 观察张量
            if obs.dim() == 1:
                obs = obs.unsqueeze(0)
            if obs.size(0) != target_batch_size:
                if obs.size(0) == 1:
                    obs = obs.repeat(target_batch_size, 1)
                else:
                    obs = obs[:target_batch_size]
            
            # 动作张量
            if actions.dim() == 0:
                actions = actions.unsqueeze(0)
            if actions.dim() > 1:
                actions = actions.flatten()
            if actions.size(0) != target_batch_size:
                if actions.size(0) == 1:
                    actions = actions.repeat(target_batch_size)
                else:
                    actions = actions[:target_batch_size]
            
            # 奖励张量
            if rewards.dim() == 0:
                rewards = rewards.unsqueeze(0)
            if rewards.dim() > 1:
                rewards = rewards.flatten()
            if rewards.size(0) != target_batch_size:
                if rewards.size(0) == 1:
                    rewards = rewards.repeat(target_batch_size)
                else:
                    rewards = rewards[:target_batch_size]
            
            # 价值张量
            if old_values.dim() == 0:
                old_values = old_values.unsqueeze(0)
            if old_values.dim() > 1:
                old_values = old_values.flatten()
            if old_values.size(0) != target_batch_size:
                if old_values.size(0) == 1:
                    old_values = old_values.repeat(target_batch_size)
                else:
                    old_values = old_values[:target_batch_size]
            
            # 对数概率张量
            if old_log_probs.dim() == 0:
                old_log_probs = old_log_probs.unsqueeze(0)
            if old_log_probs.dim() > 1:
                old_log_probs = old_log_probs.flatten()
            if old_log_probs.size(0) != target_batch_size:
                if old_log_probs.size(0) == 1:
                    old_log_probs = old_log_probs.repeat(target_batch_size)
                else:
                    old_log_probs = old_log_probs[:target_batch_size]
            
            # 最终验证
            assert obs.size(0) == target_batch_size, f"obs批次大小不匹配: {obs.size(0)} vs {target_batch_size}"
            assert actions.size(0) == target_batch_size, f"actions批次大小不匹配: {actions.size(0)} vs {target_batch_size}"
            assert rewards.size(0) == target_batch_size, f"rewards批次大小不匹配: {rewards.size(0)} vs {target_batch_size}"
            assert old_values.size(0) == target_batch_size, f"old_values批次大小不匹配: {old_values.size(0)} vs {target_batch_size}"
            assert old_log_probs.size(0) == target_batch_size, f"old_log_probs批次大小不匹配: {old_log_probs.size(0)} vs {target_batch_size}"
            
            logger.debug(f"智能体 {agent_id} 张量维度验证通过，批次大小: {target_batch_size}")
            
            return obs, actions, rewards, old_values, old_log_probs
            
        except Exception as e:
            logger.error(f"智能体 {agent_id} 张量维度验证失败: {e}")
            # 创建默认张量
            batch_size = 1
            obs = torch.zeros(batch_size, self.obs_dim, device=self.device)
            actions = torch.zeros(batch_size, dtype=torch.long, device=self.device)
            rewards = torch.zeros(batch_size, device=self.device)
            old_values = torch.zeros(batch_size, device=self.device)
            old_log_probs = torch.full((batch_size,), -2.0, device=self.device)
            
            return obs, actions, rewards, old_values, old_log_probs
    
    def update(self, batch_data: Dict[str, Any]) -> Dict[str, float]:
        """更新策略"""
        # 异构智能体处理
        if self.config.use_heterogeneous_agents:
            return self._heterogeneous_update(batch_data)
        else:
            return super().update(batch_data)
    
    def _heterogeneous_update(self, batch_data):
        """异构智能体更新 - 带损失聚合"""
        update_info = {}
        total_loss = 0.0
        total_policy_loss = 0.0
        total_value_loss = 0.0
        total_entropy = 0.0
        
        # 按顺序更新各个智能体
        for agent_id in self.config.update_order:
            agent_data = self._extract_agent_data(batch_data, agent_id)
            agent_info = self._update_single_agent(agent_data, agent_id)
            update_info[agent_id] = agent_info
            
            # 聚合损失
            total_loss += agent_info['loss']
            total_policy_loss += agent_info['policy_loss']
            total_value_loss += agent_info['value_loss']
            total_entropy += agent_info['entropy']
        
        # 计算平均损失
        num_agents = len(self.config.update_order)
        update_info['total'] = {
            'loss': total_loss / num_agents,
            'policy_loss': total_policy_loss / num_agents,
            'value_loss': total_value_loss / num_agents,
            'entropy': total_entropy / num_agents
        }
        
        logger.debug(f"多智能体更新完成，总损失: {update_info['total']['loss']:.4f}")
        
        return update_info
    
    def _extract_agent_data(self, batch_data, agent_id):
        """提取单个智能体的数据 - 健壮性处理"""
        try:
            # 支持不同的数据格式
            if isinstance(batch_data['obs'], dict):
                obs = batch_data['obs'][agent_id]
            else:
                # 假设obs是多智能体批次，取对应索引
                agent_idx = self.config.update_order.index(agent_id)
                obs = batch_data['obs'][agent_idx] if len(batch_data['obs']) > agent_idx else batch_data['obs'][0]
            
            if isinstance(batch_data['actions'], dict):
                actions = batch_data['actions'][agent_id]
            else:
                agent_idx = self.config.update_order.index(agent_id)
                actions = batch_data['actions'][agent_idx] if len(batch_data['actions']) > agent_idx else batch_data['actions'][0]
            
            if isinstance(batch_data['rewards'], dict):
                rewards = batch_data['rewards'][agent_id]
            else:
                agent_idx = self.config.update_order.index(agent_id)
                rewards = batch_data['rewards'][agent_idx] if len(batch_data['rewards']) > agent_idx else batch_data['rewards'][0]
            
            # 验证和修复动作数据
            actions = self._validate_and_fix_actions(actions, agent_id)
            
            # 确保数据为数组格式（处理标量）
            if not isinstance(obs, np.ndarray):
                obs = np.array([obs]) if np.isscalar(obs) else np.array(obs)
            if not isinstance(actions, np.ndarray):
                actions = np.array([actions]) if np.isscalar(actions) else np.array(actions)
            if not isinstance(rewards, np.ndarray):
                rewards = np.array([rewards]) if np.isscalar(rewards) else np.array(rewards)
            
            # 对于values和log_probs，如果不存在则创建默认值
            if 'values' in batch_data and batch_data['values'] is not None:
                if isinstance(batch_data['values'], dict):
                    values = batch_data['values'][agent_id]
                else:
                    agent_idx = self.config.update_order.index(agent_id)
                    values = batch_data['values'][agent_idx] if len(batch_data['values']) > agent_idx else batch_data['values'][0]
            else:
                # 如果没有values，创建零值作为初始估计
                values = np.zeros_like(rewards)
            
            # 确保values为数组格式
            if not isinstance(values, np.ndarray):
                values = np.array([values]) if np.isscalar(values) else np.array(values)
            
            if 'log_probs' in batch_data and batch_data['log_probs'] is not None:
                if isinstance(batch_data['log_probs'], dict):
                    log_probs = batch_data['log_probs'][agent_id]
                else:
                    agent_idx = self.config.update_order.index(agent_id)
                    log_probs = batch_data['log_probs'][agent_idx] if len(batch_data['log_probs']) > agent_idx else batch_data['log_probs'][0]
            else:
                # 如果没有log_probs，使用网络预测
                if not isinstance(obs, torch.Tensor):
                    obs_tensor = torch.FloatTensor(obs).to(self.device)
                else:
                    obs_tensor = obs.to(self.device)
                    
                with torch.no_grad():
                    log_probs_tensor = self.network.get_action_log_probs(obs_tensor)
                    if not isinstance(actions, torch.Tensor):
                        actions_tensor = torch.LongTensor(actions).to(self.device)
                    else:
                        actions_tensor = actions.to(self.device)
                    
                    # 安全的gather操作
                    log_probs = self._safe_gather_log_probs(log_probs_tensor, actions_tensor)
            
            # 确保log_probs为数组格式
            if not isinstance(log_probs, np.ndarray):
                log_probs = np.array([log_probs]) if np.isscalar(log_probs) else np.array(log_probs)
            
            return {
                'obs': obs,
                'actions': actions,
                'rewards': rewards,
                'values': values,
                'log_probs': log_probs
            }
            
        except Exception as e:
            logger.error(f"提取智能体数据失败 {agent_id}: {e}")
            # 返回默认数据结构
            return {
                'obs': np.zeros(self.obs_dim),
                'actions': np.array([0]),
                'rewards': np.array([0.0]),
                'values': np.array([0.0]),
                'log_probs': np.array([0.0])
            }
    
    def _update_single_agent(self, agent_data, agent_id):
        """更新单个智能体 - 修复PPO损失计算"""
        logger.debug(f"更新智能体: {agent_id}")
        
        # 提取智能体数据
        obs = agent_data['obs']
        actions = agent_data['actions']
        rewards = agent_data['rewards']
        old_values = agent_data['values']
        old_log_probs = agent_data['log_probs']
        
        # 确保数据是张量格式并验证维度
        if not isinstance(obs, torch.Tensor):
            obs = torch.FloatTensor(obs).to(self.device)
        if not isinstance(actions, torch.Tensor):
            actions = torch.LongTensor(actions).to(self.device)
        if not isinstance(rewards, torch.Tensor):
            rewards = torch.FloatTensor(rewards).to(self.device)
        if not isinstance(old_values, torch.Tensor):
            old_values = torch.FloatTensor(old_values).to(self.device)
        if not isinstance(old_log_probs, torch.Tensor):
            old_log_probs = torch.FloatTensor(old_log_probs).to(self.device)
        
        # 验证和修复张量维度
        obs, actions, rewards, old_values, old_log_probs = self._validate_tensor_dimensions(
            obs, actions, rewards, old_values, old_log_probs, agent_id
        )
        
        # 计算GAE优势和回报 - 改进版本
        advantages, returns = self._compute_robust_gae_advantages_returns(rewards, old_values)
        advantages = torch.FloatTensor(advantages).to(self.device)
        returns = torch.FloatTensor(returns).to(self.device)
        
        # 改进的优势标准化（确保非零方差）
        adv_mean = advantages.mean()
        adv_std = advantages.std()
        
        # 处理标准差为0或NaN的情况 - 更稳健的处理
        if torch.isnan(adv_std) or adv_std <= 1e-8:
            logger.warning(f"智能体 {agent_id} 优势标准差异常: {adv_std}, 使用最小方差")
            # 如果方差太小，添加小的随机噪声以避免除零
            advantages = advantages - adv_mean
            if advantages.abs().max() < 1e-6:
                advantages = torch.randn_like(advantages) * 0.5  # 增大优势值，确保策略损失更明显
        else:
            advantages = (advantages - adv_mean) / (adv_std + 1e-8)
        
        # 检查并处理NaN值 - 更严格的处理
        if torch.any(torch.isnan(advantages)) or torch.any(torch.isinf(advantages)):
            logger.warning(f"智能体 {agent_id} 检测到异常优势值，使用默认分布")
            advantages = torch.randn_like(advantages) * 0.5  # 增大优势值范围
        
        # 使用优势分解器（如果需要）
        if hasattr(self, 'advantage_decomposer') and agent_id in ['farmer1', 'farmer2']:
            try:
                # 对农民智能体进行优势分解
                individual_values = [old_values]  # 简化：使用当前智能体的价值
                decomposed_advantages = self.advantage_decomposer.decompose_advantage(
                    advantages, individual_values, [agent_id]
                )
                advantages = decomposed_advantages[agent_id]
            except Exception as e:
                logger.warning(f"智能体 {agent_id} 优势分解失败，使用原始优势: {e}")
        
        # 前向传播获取当前策略 - 修复网络调用
        try:
            # 直接调用网络的前向传播方法，避免evaluate_actions的维度问题
            action_logits = self.network.forward_actor(obs)
            current_log_probs_full = F.log_softmax(action_logits, dim=-1)
            current_action_probs = F.softmax(action_logits, dim=-1)
            
            # 正确获取选择动作的对数概率
            current_log_probs = current_log_probs_full.gather(1, actions.unsqueeze(-1)).squeeze(-1)
            
            # 计算正确的熵（所有动作的熵，不是选择动作的交叉熵）
            entropy = -(current_action_probs * current_log_probs_full).sum(dim=-1).mean()
            
            # 获取价值
            current_values = self.network.forward_critic(obs).squeeze(-1)
            
            # 数值稳定性检查
            if torch.any(torch.isnan(current_log_probs)) or torch.any(torch.isinf(current_log_probs)):
                logger.warning(f"智能体 {agent_id} 检测到异常对数概率，重置")
                current_log_probs = torch.full_like(current_log_probs, -2.0)
            
            if torch.any(torch.isnan(current_values)) or torch.any(torch.isinf(current_values)):
                logger.warning(f"智能体 {agent_id} 检测到异常价值，重置")
                current_values = torch.zeros_like(current_values)
                
            if torch.isnan(entropy) or torch.isinf(entropy):
                logger.warning(f"智能体 {agent_id} 检测到异常熵，重置")
                entropy = torch.tensor(0.1, device=self.device)  # 使用小的正值而不是0
                
        except Exception as e:
            logger.error(f"智能体 {agent_id} 网络前向传播失败: {e}")
            # 创建默认输出
            batch_size = actions.size(0)
            current_log_probs = torch.full((batch_size,), -2.0, device=self.device)
            current_values = torch.zeros(batch_size, device=self.device)
            entropy = torch.tensor(0.1, device=self.device)
        
        # 计算策略损失 - 改进的PPO Clipped Objective
        # 添加数值稳定性限制
        log_ratio = current_log_probs - old_log_probs
        log_ratio = torch.clamp(log_ratio, -10.0, 10.0)  # 防止数值溢出
        ratio = torch.exp(log_ratio)
        
        # 检查比率的合理性
        if torch.any(torch.isnan(ratio)) or torch.any(torch.isinf(ratio)):
            logger.warning(f"智能体 {agent_id} 检测到异常比率，重置")
            ratio = torch.ones_like(ratio)
        
        # PPO裁剪目标
        surr1 = ratio * advantages
        surr2 = torch.clamp(ratio, 1.0 - self.happo_clip_ratio, 1.0 + self.happo_clip_ratio) * advantages
        policy_loss = -torch.min(surr1, surr2).mean()
        
        # 确保policy_loss是正数（PPO损失应该被最小化，所以我们取负号）
        if policy_loss.item() >= 0:
            logger.warning(f"智能体 {agent_id} 策略损失为非负值: {policy_loss.item()}")
        
        # 计算价值损失 (MSE) - 添加稳定性检查
        value_loss = F.mse_loss(current_values, returns)
        if torch.isnan(value_loss) or torch.isinf(value_loss):
            logger.warning(f"智能体 {agent_id} 价值损失异常，重置")
            value_loss = torch.tensor(0.0, device=self.device)
        
        # 计算总损失 - 确保所有项都有合理的大小
        policy_component = policy_loss
        value_component = self.happo_value_coef * value_loss
        entropy_component = -self.happo_entropy_coef * entropy  # 熵项应该是负的（鼓励探索）
        
        total_loss = policy_component + value_component + entropy_component
        
        # 最终检查总损失
        if torch.isnan(total_loss) or torch.isinf(total_loss):
            logger.error(f"智能体 {agent_id} 总损失异常，跳过更新")
            return {
                'loss': 0.0,
                'policy_loss': 0.0,
                'value_loss': 0.0,
                'entropy': 0.0,
                'advantages_mean': 0.0,
                'advantages_std': 1.0
            }
        
        # 执行反向传播
        self.optimizer.zero_grad()
        
        # 检查损失是否需要梯度
        if total_loss.requires_grad:
            total_loss.backward()
            
            # 检查梯度是否正常
            total_norm = 0
            for p in self.network.parameters():
                if p.grad is not None:
                    param_norm = p.grad.data.norm(2)
                    total_norm += param_norm.item() ** 2
            total_norm = total_norm ** (1. / 2)
            
            if total_norm == 0:
                logger.warning(f"智能体 {agent_id} 梯度为零")
            elif total_norm > 100:
                logger.warning(f"智能体 {agent_id} 梯度很大: {total_norm}")
                
        else:
            logger.warning(f"智能体 {agent_id} 损失不需要梯度，跳过反向传播")
        
        # 梯度裁剪
        torch.nn.utils.clip_grad_norm_(self.network.parameters(), self.happo_max_grad_norm)
        
        # 更新参数
        self.optimizer.step()
        
        # 返回损失指标（确保所有值都是有效的）
        loss_value = total_loss.item() if not torch.isnan(total_loss) else 0.0
        policy_loss_value = abs(policy_loss.item()) if not torch.isnan(policy_loss) else 0.0  # 取绝对值显示
        value_loss_value = value_loss.item() if not torch.isnan(value_loss) else 0.0
        entropy_value = entropy.item() if not torch.isnan(entropy) else 0.0
        adv_mean_value = advantages.mean().item() if not torch.isnan(advantages.mean()) else 0.0
        adv_std_value = advantages.std().item() if not torch.isnan(advantages.std()) else 1.0
        
        # 记录调试信息
        logger.debug(f"智能体 {agent_id} 更新完成: policy_loss={policy_loss_value:.6f}, "
                    f"value_loss={value_loss_value:.6f}, entropy={entropy_value:.6f}, "
                    f"adv_mean={adv_mean_value:.6f}, ratio_mean={ratio.mean().item():.6f}")
        
        return {
            'loss': loss_value,
            'policy_loss': policy_loss_value,
            'value_loss': value_loss_value,
            'entropy': entropy_value,
            'advantages_mean': adv_mean_value,
            'advantages_std': adv_std_value,
            'ratio_mean': ratio.mean().item() if not torch.isnan(ratio.mean()) else 1.0
        }
    
    def _compute_gae_advantages_returns(self, rewards, values):
        """计算GAE优势和回报 - 原始版本"""
        # 转换为numpy进行计算
        if isinstance(rewards, torch.Tensor):
            rewards = rewards.cpu().numpy()
        if isinstance(values, torch.Tensor):
            values = values.cpu().numpy()
        
        # 处理空数组或标量的情况
        if rewards.size == 0:
            return np.array([]), np.array([])
        
        if rewards.ndim == 0:
            rewards = np.array([rewards])
        if values.ndim == 0:
            values = np.array([values])
        
        # 确保数组长度一致
        min_len = min(len(rewards), len(values))
        rewards = rewards[:min_len]
        values = values[:min_len]
        
        # 处理NaN和无穷大值
        rewards = np.nan_to_num(rewards, nan=0.0, posinf=1.0, neginf=-1.0)
        values = np.nan_to_num(values, nan=0.0, posinf=1.0, neginf=-1.0)
        
        # 添加最后的值作为引导值
        values_with_next = np.append(values, 0.0)
        
        # 计算TD误差
        deltas = rewards + self.happo_gamma * values_with_next[1:] - values_with_next[:-1]
        
        # 计算GAE优势
        advantages = np.zeros_like(rewards)
        gae = 0
        for t in reversed(range(len(rewards))):
            gae = deltas[t] + self.happo_gamma * self.happo_gae_lambda * gae
            advantages[t] = gae
        
        # 计算回报
        returns = advantages + values
        
        # 最终清理NaN值
        advantages = np.nan_to_num(advantages, nan=0.0, posinf=1.0, neginf=-1.0)
        returns = np.nan_to_num(returns, nan=0.0, posinf=1.0, neginf=-1.0)
        
        return advantages, returns
    
    def _compute_robust_gae_advantages_returns(self, rewards, values):
        """计算GAE优势和回报 - 改进版本，增强数值稳定性"""
        # 转换为numpy进行计算
        if isinstance(rewards, torch.Tensor):
            rewards = rewards.cpu().numpy()
        if isinstance(values, torch.Tensor):
            values = values.cpu().numpy()
        
        # 处理空数组或标量的情况
        if rewards.size == 0:
            return np.array([0.0]), np.array([0.0])
        
        if rewards.ndim == 0:
            rewards = np.array([rewards])
        if values.ndim == 0:
            values = np.array([values])
        
        # 确保数组长度一致
        min_len = min(len(rewards), len(values))
        if min_len == 0:
            return np.array([0.0]), np.array([0.0])
            
        rewards = rewards[:min_len]
        values = values[:min_len]
        
        # 处理NaN和无穷大值，使用更保守的处理
        rewards = np.nan_to_num(rewards, nan=0.0, posinf=0.5, neginf=-0.5)
        values = np.nan_to_num(values, nan=0.0, posinf=0.5, neginf=-0.5)
        
        # 限制值的范围，防止数值爆炸
        rewards = np.clip(rewards, -10.0, 10.0)
        values = np.clip(values, -10.0, 10.0)
        
        # 添加最后的值作为引导值（使用更合理的估计）
        next_value = values[-1] if len(values) > 0 else 0.0
        values_with_next = np.append(values, next_value)
        
        # 计算TD误差
        deltas = rewards + self.happo_gamma * values_with_next[1:] - values_with_next[:-1]
        
        # 限制TD误差的范围
        deltas = np.clip(deltas, -5.0, 5.0)
        
        # 计算GAE优势 - 使用更稳定的计算
        advantages = np.zeros_like(rewards)
        gae = 0
        discount_factor = self.happo_gamma * self.happo_gae_lambda
        
        for t in reversed(range(len(rewards))):
            gae = deltas[t] + discount_factor * gae
            # 防止GAE值过大
            gae = np.clip(gae, -10.0, 10.0)
            advantages[t] = gae
        
        # 计算回报
        returns = advantages + values
        
        # 最终清理异常值
        advantages = np.nan_to_num(advantages, nan=0.0, posinf=1.0, neginf=-1.0)
        returns = np.nan_to_num(returns, nan=0.0, posinf=1.0, neginf=-1.0)
        
        # 确保输出合理性
        advantages = np.clip(advantages, -20.0, 20.0)
        returns = np.clip(returns, -20.0, 20.0)
        
        # 如果所有优势都是零，添加小的随机扰动
        if np.all(np.abs(advantages) < 1e-8):
            advantages = np.random.normal(0, 0.01, size=advantages.shape)
            logger.debug("所有优势值接近零，添加随机扰动")
        
        return advantages, returns
