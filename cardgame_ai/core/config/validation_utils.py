"""
配置验证工具模块
提供配置验证功能
"""

from typing import Any, Dict, List, Optional, Callable
from dataclasses import dataclass
from .base import ConfigValidationError

@dataclass
class ValidationRule:
    """验证规则"""
    field: str
    validator: Callable[[Any], bool]
    error_message: str
    
@dataclass
class ConfigValidationResult:
    """验证结果"""
    is_valid: bool
    errors: List[str] = None
    warnings: List[str] = None
    
    def __post_init__(self):
        if self.errors is None:
            self.errors = []
        if self.warnings is None:
            self.warnings = []

class ConfigValidator:
    """配置验证器"""
    
    def __init__(self):
        self.rules: List[ValidationRule] = []
        
    def add_rule(self, field: str, validator: Callable[[Any], bool], 
                 error_message: str):
        """添加验证规则"""
        self.rules.append(ValidationRule(field, validator, error_message))
        
    def validate(self, config: Dict[str, Any]) -> ConfigValidationResult:
        """验证配置"""
        result = ConfigValidationResult(is_valid=True)
        
        for rule in self.rules:
            # 获取字段值
            value = self._get_field_value(config, rule.field)
            
            if value is not None:
                # 执行验证
                try:
                    if not rule.validator(value):
                        result.is_valid = False
                        result.errors.append(f"{rule.field}: {rule.error_message}")
                except Exception as e:
                    result.is_valid = False
                    result.errors.append(f"{rule.field}: Validation error - {str(e)}")
                    
        return result
        
    def _get_field_value(self, config: Dict[str, Any], field_path: str) -> Any:
        """获取嵌套字段值"""
        parts = field_path.split('.')
        value = config
        
        for part in parts:
            if isinstance(value, dict) and part in value:
                value = value[part]
            else:
                return None
                
        return value

# 预定义验证器
class Validators:
    """常用验证器集合"""
    
    @staticmethod
    def positive(value: Any) -> bool:
        """验证正数"""
        return isinstance(value, (int, float)) and value > 0
        
    @staticmethod
    def non_negative(value: Any) -> bool:
        """验证非负数"""
        return isinstance(value, (int, float)) and value >= 0
        
    @staticmethod
    def in_range(min_val: float, max_val: float) -> Callable[[Any], bool]:
        """验证范围"""
        def validator(value: Any) -> bool:
            return isinstance(value, (int, float)) and min_val <= value <= max_val
        return validator
        
    @staticmethod
    def in_choices(choices: List[Any]) -> Callable[[Any], bool]:
        """验证选择项"""
        def validator(value: Any) -> bool:
            return value in choices
        return validator
        
    @staticmethod
    def is_type(expected_type: type) -> Callable[[Any], bool]:
        """验证类型"""
        def validator(value: Any) -> bool:
            return isinstance(value, expected_type)
        return validator
        
    @staticmethod
    def list_of(item_validator: Callable[[Any], bool]) -> Callable[[Any], bool]:
        """验证列表项"""
        def validator(value: Any) -> bool:
            if not isinstance(value, list):
                return False
            return all(item_validator(item) for item in value)
        return validator
        
    @staticmethod
    def dict_of(key_validator: Callable[[Any], bool],
                value_validator: Callable[[Any], bool]) -> Callable[[Any], bool]:
        """验证字典项"""
        def validator(value: Any) -> bool:
            if not isinstance(value, dict):
                return False
            return all(key_validator(k) and value_validator(v) 
                      for k, v in value.items())
        return validator

# 创建默认验证器
def create_default_validators() -> Dict[str, ConfigValidator]:
    """创建默认配置验证器"""
    validators = {}
    
    # 算法配置验证器
    algo_validator = ConfigValidator()
    algo_validator.add_rule('efficient_zero.hidden_size', Validators.positive,
                          "Hidden size must be positive")
    algo_validator.add_rule('efficient_zero.learning_rate', Validators.positive,
                          "Learning rate must be positive")
    algo_validator.add_rule('efficient_zero.batch_size', Validators.positive,
                          "Batch size must be positive")
    algo_validator.add_rule('efficient_zero.discount', Validators.in_range(0, 1),
                          "Discount factor must be in [0, 1]")
    validators['algorithm'] = algo_validator
    
    # 训练配置验证器
    train_validator = ConfigValidator()
    train_validator.add_rule('total_steps', Validators.positive,
                           "Total steps must be positive")
    train_validator.add_rule('eval_interval', Validators.positive,
                           "Eval interval must be positive")
    train_validator.add_rule('optimizer.lr', Validators.positive,
                           "Learning rate must be positive")
    validators['training'] = train_validator
    
    # MCTS配置验证器
    mcts_validator = ConfigValidator()
    mcts_validator.add_rule('num_simulations', Validators.positive,
                          "Number of simulations must be positive")
    mcts_validator.add_rule('c_puct', Validators.positive,
                          "c_puct must be positive")
    mcts_validator.add_rule('num_threads', Validators.positive,
                          "Number of threads must be positive")
    validators['mcts'] = mcts_validator
    
    # 硬件配置验证器
    hw_validator = ConfigValidator()
    hw_validator.add_rule('gpu.memory_fraction', Validators.in_range(0, 1),
                        "GPU memory fraction must be in [0, 1]")
    hw_validator.add_rule('cpu.num_threads', Validators.positive,
                        "Number of CPU threads must be positive")
    validators['hardware'] = hw_validator
    
    return validators