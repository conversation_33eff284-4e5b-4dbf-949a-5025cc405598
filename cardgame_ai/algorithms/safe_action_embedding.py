"""
安全的动作嵌入层模块

解决565维动作空间到256维嵌入的映射问题
"""

import torch
import torch.nn as nn
from cardgame_ai.utils.logging import get_logger

logger = get_logger(__name__)

class SafeActionEmbedding(nn.Module):
    """
    安全的动作嵌入层
    
    特性：
    1. 支持任意大小的动作空间映射到固定嵌入空间
    2. 自动边界检查和错误恢复
    3. 可配置的映射策略
    4. GPU加速的索引映射
    """
    
    def __init__(self, original_action_space, embedding_dim, hidden_dim, mapping_strategy="hash_based"):
        super().__init__()
        self.original_action_space = original_action_space
        self.embedding_dim = embedding_dim
        self.mapping_strategy = mapping_strategy
        
        # 创建实际的嵌入层
        # 修复：nn.Embedding的第一个参数应该是num_embeddings（词汇表大小），第二个是embedding_dim
        self.embedding = nn.Embedding(original_action_space, hidden_dim)
        
        # 创建动作映射
        self.action_mapping = self._create_action_mapping()
        
        # 创建映射张量（用于快速索引）
        self.register_buffer("mapping_tensor", self._create_mapping_tensor())
        
        logger.info(f"安全动作嵌入层初始化: {original_action_space} → {embedding_dim}, 策略: {mapping_strategy}")
    
    def _create_action_mapping(self):
        """创建动作映射策略"""
        mapping = {}
        
        # 基于哈希的映射（减少冲突）
        for action_id in range(self.original_action_space):
            hash_val = (action_id * 31 + 17) % self.embedding_dim
            mapping[action_id] = hash_val
        
        return mapping
    
    def _create_mapping_tensor(self):
        """创建映射张量用于快速查找"""
        mapping_tensor = torch.zeros(self.original_action_space, dtype=torch.long)
        for orig_id, mapped_id in self.action_mapping.items():
            mapping_tensor[orig_id] = mapped_id
        return mapping_tensor
    
    def forward(self, action_indices):
        """前向传播，支持安全索引"""
        # 确保输入是正确的张量类型
        if not torch.is_tensor(action_indices):
            action_indices = torch.tensor(action_indices, dtype=torch.long)
        
        # 移动到正确设备（与嵌入层权重相同）
        target_device = self.embedding.weight.device
        if action_indices.device != target_device:
            action_indices = action_indices.to(target_device)
        
        # 确保mapping_tensor也在正确设备上
        if self.mapping_tensor.device != target_device:
            self.mapping_tensor = self.mapping_tensor.to(target_device)
        
        # 增强的边界验证和调试
        if torch.is_tensor(action_indices):
            # 检查是否有越界索引
            min_idx = action_indices.min().item()
            max_idx = action_indices.max().item()
            
            if min_idx < 0 or max_idx >= self.original_action_space:
                logger.warning(f"动作索引越界: min={min_idx}, max={max_idx}, 有效范围=[0, {self.original_action_space-1}]")
                
                # 详细记录越界索引
                invalid_mask = (action_indices < 0) | (action_indices >= self.original_action_space)
                if invalid_mask.any():
                    invalid_indices = action_indices[invalid_mask].cpu().numpy()
                    logger.warning(f"越界索引值: {invalid_indices}")
        
        # 安全的边界检查
        action_indices = self._safe_clamp(action_indices)
        
        # 确保索引在有效范围内
        action_indices = torch.clamp(action_indices, 0, self.original_action_space - 1)
        
        # 执行嵌入查找，添加异常处理
        try:
            embedded = self.embedding(action_indices)
            return embedded
        except Exception as e:
            logger.error(f"嵌入查找失败: {e}")
            logger.error(f"动作索引shape: {action_indices.shape}, dtype: {action_indices.dtype}")
            logger.error(f"动作索引范围: [{action_indices.min().item()}, {action_indices.max().item()}]")
            logger.error(f"嵌入表大小: {self.embedding.num_embeddings} x {self.embedding.embedding_dim}")
            raise
    
    def _safe_clamp(self, action_indices):
        """安全的索引限制"""
        # 处理超出上界的索引
        action_indices = torch.where(
            action_indices >= self.original_action_space,
            torch.tensor(206, device=action_indices.device),  # 默认为"不出"动作
            action_indices
        )
        
        # 处理负索引
        action_indices = torch.where(
            action_indices < 0,
            torch.tensor(206, device=action_indices.device),
            action_indices
        )
        
        return action_indices
