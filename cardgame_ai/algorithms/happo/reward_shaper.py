"""
HAPPO模块基础导入
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
from cardgame_ai.utils.logging import get_logger
from ..mappo import MAPPO, MAPPOConfig
from ..role_specific_mappo import RoleSpecificMAPPO
from ...core.base import State, Action
from ...games.doudizhu.state_modules import DoudizhuState

logger = get_logger(__name__)


class CooperationRewardShaper:
    """协作奖励塑形器 - 鼓励农民协作"""
    
    def __init__(self, cooperation_weight: float = 0.3, synergy_bonus: float = 0.2):
        """初始化奖励塑形器"""
        self.cooperation_weight = cooperation_weight
        self.synergy_bonus = synergy_bonus
        logger.info(f"协作奖励塑形器初始化: weight={cooperation_weight}")
    
    def shape_rewards(self, base_rewards: Dict[str, float], 
                     actions: Dict[str, Any],
                     state: 'DoudizhuState') -> Dict[str, float]:
        """塑形奖励以鼓励协作"""
        shaped_rewards = base_rewards.copy()
        
        # 检测农民协作
        farmer_cooperation = self._detect_farmer_cooperation(actions, state)
        
        if farmer_cooperation:
            # 给农民额外的协作奖励
            for agent_id in ["farmer1", "farmer2"]:
                if agent_id in shaped_rewards:
                    shaped_rewards[agent_id] += self.synergy_bonus
        
        return shaped_rewards
    
    def _detect_farmer_cooperation(self, actions, state):
        """检测农民是否在协作"""
        # 简化的协作检测逻辑
        farmer1_action = actions.get("farmer1")
        farmer2_action = actions.get("farmer2")
        
        if farmer1_action is None or farmer2_action is None:
            return False
        
        # 如果一个农民选择不要牌，让队友出牌
        if (farmer1_action.action_type == "pass" and 
            farmer2_action.action_type in ["single", "pair", "sequence"]):
            return True
        
        if (farmer2_action.action_type == "pass" and 
            farmer1_action.action_type in ["single", "pair", "sequence"]):
            return True
        
        return False
