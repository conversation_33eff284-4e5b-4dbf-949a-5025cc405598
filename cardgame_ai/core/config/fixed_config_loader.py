"""
固定配置加载器
支持手动优化的固定配置，不使用自动调整
"""

import os
from pathlib import Path
from typing import Dict, Any, Optional, List
import yaml

from cardgame_ai.utils.logging import get_logger

logger = get_logger(__name__)


class FixedConfigLoader:
    """固定配置加载器 - 加载手动优化的配置文件"""
    
    def __init__(self, config_path: Optional[str] = None):
        """
        初始化配置加载器
        
        Args:
            config_path: 配置文件路径，如果为None则使用默认配置
        """
        self.config_path = self._resolve_config_path(config_path)
        self.config = self._load_config()
        self._validate_config()
        
    def _resolve_config_path(self, config_path: Optional[str]) -> Path:
        """解析配置文件路径"""
        if config_path:
            path = Path(config_path)
            if path.exists():
                return path
            else:
                logger.warning(f"配置文件不存在: {config_path}")
        
        # 默认配置文件搜索顺序
        default_paths = [
            Path("optimized_fixed_config.yaml"),
            Path("fixed_config.yaml"),
            Path("config.yaml"),
            Path("configs/optimized_fixed_config.yaml"),
            Path("configs/fixed_config.yaml"),
            Path("configs/config.yaml"),
        ]
        
        for path in default_paths:
            if path.exists():
                logger.info(f"使用配置文件: {path}")
                return path
        
        raise FileNotFoundError("未找到配置文件，请创建optimized_fixed_config.yaml")
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        logger.info(f"加载配置文件: {self.config_path}")
        
        with open(self.config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # 验证配置版本
        config_version = config.get('config_version', '1.0')
        if float(config_version) < 4.0:
            logger.warning(f"配置版本较旧: {config_version}，建议升级到4.0")
        
        return config
    
    def _validate_config(self):
        """验证配置的完整性和正确性"""
        required_sections = [
            'algorithm',
            'training', 
            'mcts',
            'system',
            'performance',
            'logging'
        ]
        
        for section in required_sections:
            if section not in self.config:
                raise ValueError(f"配置缺少必要部分: {section}")
        
        # 检查是否有"auto"参数
        self._check_no_auto_params(self.config)
        
        logger.info("配置验证通过")
    
    def _check_no_auto_params(self, config: Any, path: str = ""):
        """递归检查配置中是否有auto参数"""
        if isinstance(config, dict):
            for key, value in config.items():
                current_path = f"{path}.{key}" if path else key
                self._check_no_auto_params(value, current_path)
        elif isinstance(config, list):
            for i, item in enumerate(config):
                self._check_no_auto_params(item, f"{path}[{i}]")
        elif isinstance(config, str) and config.lower() == "auto":
            raise ValueError(f"发现自动参数: {path} = 'auto'，请使用固定值")
    
    # 配置访问方法
    def get(self, key: str, default: Any = None) -> Any:
        """获取配置值，支持点号分隔的路径"""
        keys = key.split('.')
        value = self.config
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    def get_section(self, section: str) -> Dict[str, Any]:
        """获取配置的某个部分"""
        return self.config.get(section, {})
    
    def to_dict(self) -> Dict[str, Any]:
        """返回完整配置字典"""
        return self.config.copy()
    
    # 算法相关配置
    def get_algorithm_name(self) -> str:
        """获取算法名称"""
        return self.get('algorithm.name', 'efficient_zero_v2')
    
    def get_algorithm_config(self) -> Dict[str, Any]:
        """获取算法配置"""
        return self.get_section('algorithm')
    
    def is_sve_enabled(self) -> bool:
        """是否启用SVE"""
        return self.get('algorithm.enable_sve', True)
    
    # 训练相关配置
    def get_batch_size(self) -> int:
        """获取批次大小"""
        return self.get('training.batch_size', 256)
    
    def get_learning_rate(self) -> float:
        """获取学习率"""
        return self.get('training.learning_rate', 0.0005)
    
    def get_num_episodes(self) -> int:
        """获取训练轮数"""
        return self.get('training.episodes', 100000)
    
    def get_save_interval(self) -> int:
        """获取保存间隔"""
        return self.get('training.save_interval', 500)
    
    def get_checkpoint_dir(self) -> str:
        """获取检查点目录 - 支持多种配置格式"""
        # 优先使用新格式 checkpoint.save_dir
        checkpoint_dir = self.get('checkpoint.save_dir')
        if checkpoint_dir:
            return checkpoint_dir
        
        # 回退到 training.checkpoint_dir
        checkpoint_dir = self.get('training.checkpoint_dir')
        if checkpoint_dir:
            return checkpoint_dir
        
        # 回退到顶级 checkpoint_dir
        checkpoint_dir = self.get('checkpoint_dir')
        if checkpoint_dir:
            return checkpoint_dir
        
        # 最后使用默认值
        return 'checkpoints/superhuman'
    
    def get_training_config(self) -> Dict[str, Any]:
        """获取训练配置"""
        return self.get_section('training')
    
    # MCTS相关配置
    def get_mcts_algorithm(self) -> str:
        """获取MCTS算法"""
        return self.get('mcts.algorithm', 'ppb_mcts')
    
    def get_mcts_simulations(self) -> int:
        """获取MCTS模拟次数"""
        return self.get('mcts.simulation_count', 800)
    
    def get_mcts_threads(self) -> int:
        """获取MCTS线程数"""
        return self.get('mcts.threads', 11)
    
    def get_mcts_config(self) -> Dict[str, Any]:
        """获取MCTS配置"""
        return self.get_section('mcts')
    
    # 系统相关配置
    def get_device(self) -> str:
        """获取设备"""
        device = self.get('system.device', 'cuda')
        if device.lower() == "auto":
            raise ValueError("不允许使用auto设备，请明确指定cuda或cpu")
        return device
    
    def is_torch_compile_enabled(self) -> bool:
        """是否启用torch.compile"""
        return self.get('system.torch_compile', True)
    
    def get_gpu_memory_fraction(self) -> float:
        """获取GPU内存使用比例"""
        return self.get('system.gpu_memory_fraction', 0.9)
    
    # Phase 4相关配置
    def is_phase4_enabled(self) -> bool:
        """是否启用Phase 4"""
        return self.get('phase4.enabled', True)
    
    def get_phase4_preset(self) -> str:
        """获取Phase 4预设"""
        return self.get('phase4.preset', 'expert')
    
    def get_phase4_features(self) -> Dict[str, bool]:
        """获取Phase 4功能开关"""
        return self.get('phase4.features', {})
    
    def get_phase4_config(self) -> Dict[str, Any]:
        """获取Phase 4配置"""
        return self.get_section('phase4')
    
    # 性能相关配置
    def get_performance_mode(self) -> str:
        """获取性能模式"""
        return self.get('performance.mode', 'aggressive')
    
    def is_mixed_precision_enabled(self) -> bool:
        """是否启用混合精度"""
        return self.get('training.mixed_precision', True)
    
    # 日志相关配置
    def get_logging_config(self) -> Dict[str, Any]:
        """获取日志配置"""
        return self.get_section('logging')
    
    def get_log_level(self) -> str:
        """获取日志级别"""
        return self.get('logging.level', 'INFO')
    
    def get_log_save_dir(self) -> str:
        """获取日志保存目录"""
        return self.get('logging.save_dir', 'logs')
    
    def should_clean_logs_on_start(self) -> bool:
        """是否在启动时清理日志"""
        return self.get('logging.clean_on_start', True)
    
    def is_game_log_enabled(self) -> bool:
        """是否启用游戏日志"""
        return self.get('logging.game_log_enabled', True)
    
    def get_game_log_dir(self) -> str:
        """获取游戏日志目录"""
        return self.get('logging.game_log_dir', 'logs/games')
    
    def get_log_layers_config(self) -> Dict[str, Any]:
        """获取5层日志架构配置"""
        return self.get('logging.layers', {
            'debug': {'enabled': True, 'directory': 'debug', 'level': 'DEBUG'},
            'training': {'enabled': True, 'directory': 'training', 'level': 'INFO'},
            'game': {'enabled': True, 'directory': 'game', 'level': 'INFO'},
            'summary': {'enabled': True, 'directory': 'summary', 'level': 'INFO'},
            'games': {'enabled': True, 'directory': 'games', 'level': 'INFO'}
        })
    
    def get_log_categories(self) -> Dict[str, bool]:
        """获取日志分类配置"""
        return self.get('logging.categories', {
            'training': True,
            'games': True,
            'metrics': True,
            'mcts': True,
            'rewards': True,
            'phase4': True,
            'debug': True,
            'system': True
        })
    
    def is_async_logging_enabled(self) -> bool:
        """是否启用异步日志"""
        return self.get('logging.async_logging', True)
    
    def get_log_buffer_size(self) -> int:
        """获取日志缓冲区大小"""
        return self.get('logging.buffer_size', 10000)
    
    def get_log_batch_size(self) -> int:
        """获取日志批量写入大小"""
        return self.get('logging.batch_size', 100)
    
    def get_log_flush_interval(self) -> float:
        """获取日志刷新间隔"""
        return self.get('logging.flush_interval', 1.0)
    
    def is_gpu_monitoring_enabled(self) -> bool:
        """是否启用GPU监控"""
        return self.get('logging.gpu_monitoring.enabled', True)
    
    def should_include_gpu_stats(self) -> bool:
        """是否包含GPU统计信息"""
        return self.get('logging.gpu_monitoring.include_stats', True)
    
    def get_log_rotation_config(self) -> Dict[str, Any]:
        """获取日志轮转配置"""
        return self.get('logging.rotation', {
            'max_bytes': 104857600,  # 100MB
            'backup_count': 5
        })
    
    def get_log_monitoring_config(self) -> Dict[str, Any]:
        """获取日志监控配置"""
        return self.get('logging.monitoring', {
            'enabled': True,
            'interval': 10,
            'metrics': [
                'gpu_utilization',
                'gpu_memory', 
                'training_speed',
                'loss_values',
                'win_rate',
                'episode_rewards'
            ]
        })
    
    def get_complete_logging_params(self) -> Dict[str, Any]:
        """获取完整的日志初始化参数"""
        return {
            'base_dir': self.get_log_save_dir(),
            'buffer_size': self.get_log_buffer_size(),
            'batch_size': self.get_log_batch_size(),
            'flush_interval': self.get_log_flush_interval(),
            'max_file_size': self.get_log_rotation_config().get('max_bytes', 104857600),
            'filename_template': 'daily'
        }
    
    def get_simple_logger_params(self) -> Dict[str, Any]:
        """获取SimpleLogger的初始化参数"""
        return {
            'base_dir': self.get_log_save_dir(),
            'level': self.get_log_level(),
            'buffer_size': self.get_log_buffer_size(),
            'batch_size': self.get_log_batch_size(),
            'flush_interval': self.get_log_flush_interval(),
            'enable_async': self.is_async_logging_enabled(),
            'enable_gpu_monitoring': self.is_gpu_monitoring_enabled(),
            'console_output': True,
            'max_file_size': self.get_log_rotation_config().get('max_bytes', 104857600)
        }
    
    # 多智能体相关配置
    def get_multi_agent_algorithm(self) -> str:
        """获取多智能体算法"""
        return self.get('multi_agent.algorithm', 'happo')
    
    def is_happo_enabled(self) -> bool:
        """是否启用HAPPO"""
        return self.get_multi_agent_algorithm() == 'happo'
    
    # 奖励系统配置
    def get_reward_config(self) -> Dict[str, Any]:
        """获取奖励配置"""
        return self.get_section('reward')
    
    # 验证配置
    def get_validation_config(self) -> Dict[str, Any]:
        """获取验证配置"""
        return self.get_section('validation')
    
    def get_startup_checks(self) -> List[str]:
        """获取启动检查项"""
        return self.get('validation.startup_checks', [])
    
    def get_runtime_checks(self) -> Dict[str, Any]:
        """获取运行时检查配置"""
        return self.get('validation.runtime_checks', {})
    
    # 评估配置
    def get_evaluation_config(self) -> Dict[str, Any]:
        """获取评估配置"""
        return self.get_section('evaluation')
    
    def get_target_win_rate(self) -> float:
        """获取目标胜率"""
        return self.get('evaluation.targets.win_rate', 0.85)
    
    def print_summary(self):
        """打印配置摘要"""
        logger.info("=" * 60)
        logger.info("配置摘要")
        logger.info("=" * 60)
        logger.info(f"配置文件: {self.config_path}")
        logger.info(f"配置版本: {self.config.get('config_version', 'unknown')}")
        logger.info(f"算法: {self.get_algorithm_name()}")
        logger.info(f"设备: {self.get_device()}")
        logger.info(f"批次大小: {self.get_batch_size()}")
        logger.info(f"学习率: {self.get_learning_rate()}")
        logger.info(f"MCTS模拟: {self.get_mcts_simulations()}")
        logger.info(f"性能模式: {self.get_performance_mode()}")
        
        if self.is_phase4_enabled():
            logger.info(f"Phase 4预设: {self.get_phase4_preset()}")
            features = self.get_phase4_features()
            enabled = [k for k, v in features.items() if v]
            logger.info(f"Phase 4功能: {', '.join(enabled)}")
        
        logger.info("=" * 60)


# 便捷函数
_default_loader = None

def get_fixed_config(config_path: Optional[str] = None) -> FixedConfigLoader:
    """获取固定配置加载器的单例"""
    global _default_loader
    
    if _default_loader is None or config_path is not None:
        _default_loader = FixedConfigLoader(config_path)
    
    return _default_loader