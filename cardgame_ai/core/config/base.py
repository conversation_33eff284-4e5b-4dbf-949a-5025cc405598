"""
基础配置类和接口定义
提供所有配置类的基类和通用功能
"""

from abc import ABC, abstractmethod
from dataclasses import dataclass, field, asdict
from typing import Any, Dict, Optional, Type, TypeVar, List
from pathlib import Path
import json
import yaml
from enum import Enum

T = TypeVar('T', bound='BaseConfig')

class ConfigError(Exception):
    """配置错误基类"""
    pass

class ConfigValidationError(ConfigError):
    """配置验证错误"""
    pass

class ConfigLoadError(ConfigError):
    """配置加载错误"""
    pass

@dataclass
class ConfigMetadata:
    """配置元数据"""
    name: str
    version: str = "1.0.0"
    description: str = ""
    created_at: str = ""
    updated_at: str = ""
    author: str = "AI共生者"
    
class ConfigInterface(ABC):
    """配置接口定义"""
    
    @abstractmethod
    def validate(self) -> bool:
        """验证配置有效性"""
        pass
        
    @abstractmethod
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        pass
        
    @abstractmethod
    def from_dict(self, data: Dict[str, Any]) -> 'ConfigInterface':
        """从字典创建"""
        pass
        
    @abstractmethod
    def merge(self, other: 'ConfigInterface') -> 'ConfigInterface':
        """合并配置"""
        pass

@dataclass
class BaseConfig(ConfigInterface):
    """所有配置的基类"""
    
    metadata: Optional[ConfigMetadata] = None
    
    def __post_init__(self):
        """初始化后处理"""
        if self.metadata is None:
            self.metadata = ConfigMetadata(
                name=self.__class__.__name__,
                description=self.__class__.__doc__ or ""
            )
    
    def validate(self) -> bool:
        """验证配置有效性"""
        # 子类实现具体验证逻辑
        return True
        
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)
        
    @classmethod
    def from_dict(cls: Type[T], data: Dict[str, Any]) -> T:
        """从字典创建配置实例"""
        # 移除metadata避免重复
        metadata_data = data.pop('metadata', None)
        config = cls(**data)
        
        # 恢复metadata
        if metadata_data:
            config.metadata = ConfigMetadata(**metadata_data)
            
        return config
        
    def merge(self, other: 'BaseConfig') -> 'BaseConfig':
        """合并配置"""
        if not isinstance(other, self.__class__):
            raise ConfigError(f"Cannot merge {self.__class__.__name__} with {other.__class__.__name__}")
            
        # 获取两个配置的字典表示
        self_dict = self.to_dict()
        other_dict = other.to_dict()
        
        # 深度合并
        merged_dict = self._deep_merge(self_dict, other_dict)
        
        # 创建新实例
        return self.from_dict(merged_dict)
        
    def _deep_merge(self, dict1: Dict[str, Any], dict2: Dict[str, Any]) -> Dict[str, Any]:
        """深度合并两个字典"""
        result = dict1.copy()
        
        for key, value in dict2.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._deep_merge(result[key], value)
            else:
                result[key] = value
                
        return result
        
    def save(self, path: Path, format: str = 'yaml'):
        """保存配置到文件"""
        data = self.to_dict()
        
        if format == 'yaml':
            with open(path, 'w', encoding='utf-8') as f:
                yaml.dump(data, f, default_flow_style=False, allow_unicode=True)
        elif format == 'json':
            with open(path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
        else:
            raise ConfigError(f"Unsupported format: {format}")
            
    @classmethod
    def load(cls: Type[T], path: Path) -> T:
        """从文件加载配置"""
        if not path.exists():
            raise ConfigLoadError(f"Config file not found: {path}")
            
        with open(path, 'r', encoding='utf-8') as f:
            if path.suffix == '.yaml' or path.suffix == '.yml':
                data = yaml.safe_load(f)
            elif path.suffix == '.json':
                data = json.load(f)
            else:
                raise ConfigLoadError(f"Unsupported file format: {path.suffix}")
                
        return cls.from_dict(data)

class ConfigRegistry:
    """配置注册表 - 管理所有配置类"""
    
    _configs: Dict[str, Type[BaseConfig]] = {}
    
    @classmethod
    def register(cls, config_class: Type[BaseConfig], name: Optional[str] = None):
        """注册配置类"""
        config_name = name or config_class.__name__
        cls._configs[config_name] = config_class
        
    @classmethod
    def get(cls, name: str) -> Type[BaseConfig]:
        """获取配置类"""
        if name not in cls._configs:
            raise ConfigError(f"Config not registered: {name}")
        return cls._configs[name]
        
    @classmethod
    def list_configs(cls) -> List[str]:
        """列出所有注册的配置"""
        return list(cls._configs.keys())
