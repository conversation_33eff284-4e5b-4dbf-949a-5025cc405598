name: Quality Validation Tests

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  schedule:
    # 每天运行一次完整的质量验证
    - cron: '0 2 * * *'
  workflow_dispatch:
    inputs:
      test_scope:
        description: 'Test scope to run'
        required: true
        default: 'all'
        type: choice
        options:
          - all
          - prerequisites
          - architecture
          - performance
          - monitoring
          - evaluation

jobs:
  quality-validation:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ['3.10']  # 质量测试只在主要版本上运行
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
    
    - name: Cache pip packages
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-quality-${{ hashFiles('requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-quality-
          ${{ runner.os }}-pip-
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install torch>=2.0.0 torchvision>=0.15.0
        pip install -r requirements.txt
        pip install -r requirements-dev.txt
        pip install -e .
    
    - name: Run prerequisite checks
      if: github.event.inputs.test_scope == 'prerequisites' || github.event.inputs.test_scope == 'all' || github.event.inputs.test_scope == ''
      run: |
        pytest tests/quality/test_training_quality.py::TestPrerequisites -v --tb=short
    
    - name: Run architecture consistency tests
      if: github.event.inputs.test_scope == 'architecture' || github.event.inputs.test_scope == 'all' || github.event.inputs.test_scope == ''
      run: |
        pytest tests/quality/test_training_quality.py::TestArchitectureConsistency -v --tb=short
    
    - name: Run performance benchmarks
      if: github.event.inputs.test_scope == 'performance' || github.event.inputs.test_scope == 'all' || github.event.inputs.test_scope == ''
      run: |
        pytest tests/quality/test_training_quality.py::TestPerformanceBenchmarks -v --tb=short -m performance
    
    - name: Run monitoring tests
      if: github.event.inputs.test_scope == 'monitoring' || github.event.inputs.test_scope == 'all' || github.event.inputs.test_scope == ''
      run: |
        pytest tests/quality/test_training_quality.py::TestRealtimeMonitoring -v --tb=short
    
    - name: Run model evaluation tests
      if: github.event.inputs.test_scope == 'evaluation' || github.event.inputs.test_scope == 'all' || github.event.inputs.test_scope == ''
      run: |
        pytest tests/quality/test_training_quality.py::TestModelEvaluation -v --tb=short -m "not slow"
    
    - name: Run integration tests
      if: github.event.inputs.test_scope == 'all' || github.event.inputs.test_scope == ''
      run: |
        pytest tests/quality/test_training_quality.py::TestQualityIntegration -v --tb=short -m integration
    
    - name: Generate quality report
      if: always()
      run: |
        pytest tests/quality/ -v --html=quality-report.html --self-contained-html
    
    - name: Upload quality report
      if: always()
      uses: actions/upload-artifact@v3
      with:
        name: quality-report-${{ github.run_id }}
        path: |
          quality-report.html
          htmlcov/
          coverage.xml
    
    - name: Comment PR with results
      if: github.event_name == 'pull_request' && always()
      uses: actions/github-script@v6
      with:
        script: |
          const fs = require('fs');
          
          // 读取测试结果
          let comment = '## 🔍 Quality Validation Results\n\n';
          
          // 添加测试摘要
          if (fs.existsSync('test-results.txt')) {
            const results = fs.readFileSync('test-results.txt', 'utf8');
            comment += results;
          } else {
            comment += '✅ Quality validation completed. Check artifacts for detailed report.\n';
          }
          
          // 发布评论
          github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: comment
          });

  quality-monitoring:
    runs-on: ubuntu-latest
    needs: quality-validation
    if: github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Download quality report
      uses: actions/download-artifact@v3
      with:
        name: quality-report-${{ github.run_id }}
    
    - name: Parse quality metrics
      run: |
        python -c "
        import xml.etree.ElementTree as ET
        import json
        
        # 解析coverage.xml
        tree = ET.parse('coverage.xml')
        root = tree.getroot()
        
        metrics = {
            'coverage': float(root.get('line-rate', 0)) * 100,
            'branch_coverage': float(root.get('branch-rate', 0)) * 100,
            'timestamp': '${{ github.event.head_commit.timestamp }}'
        }
        
        with open('quality-metrics.json', 'w') as f:
            json.dump(metrics, f, indent=2)
        "
    
    - name: Update quality badge
      run: |
        COVERAGE=$(python -c "import json; print(json.load(open('quality-metrics.json'))['coverage'])")
        echo "Coverage: ${COVERAGE}%"
        
        # 生成徽章数据
        if (( $(echo "$COVERAGE >= 90" | bc -l) )); then
          COLOR="brightgreen"
        elif (( $(echo "$COVERAGE >= 80" | bc -l) )); then
          COLOR="green"
        elif (( $(echo "$COVERAGE >= 70" | bc -l) )); then
          COLOR="yellowgreen"
        elif (( $(echo "$COVERAGE >= 60" | bc -l) )); then
          COLOR="yellow"
        else
          COLOR="red"
        fi
        
        echo "{\"coverage\": \"${COVERAGE}%\", \"color\": \"${COLOR}\"}" > badge-data.json
    
    - name: Commit metrics
      if: github.event_name == 'push'
      run: |
        git config --local user.email "<EMAIL>"
        git config --local user.name "GitHub Action"
        
        mkdir -p .metrics
        cp quality-metrics.json .metrics/
        cp badge-data.json .metrics/
        
        git add .metrics/
        git diff --quiet && git diff --staged --quiet || git commit -m "Update quality metrics [skip ci]"
        git push || echo "No changes to push"