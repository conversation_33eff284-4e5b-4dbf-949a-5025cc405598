"""
观察空间统一管理器

这个模块提供了整个系统中观察空间配置的单一真实来源。
所有需要观察空间大小的组件都应该从这里获取，而不是使用硬编码值。

统一维度管理，彻底解决维度死循环问题。
基于ActionSpaceManager成熟的单例模式设计。

作者: AI系统架构师
创建日期: 2025-07-05
"""

import logging
from typing import Optional, List, Tuple
import numpy as np

logger = logging.getLogger(__name__)


class ObservationSpaceManager:
    """
    统一管理观察空间配置的单例类
    
    这个类确保整个系统使用一致的观察空间大小，
    避免了硬编码和配置不一致的问题，彻底解决维度死循环。
    """
    _instance = None
    _initialized = False
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        # 避免重复初始化
        if ObservationSpaceManager._initialized:
            return
            
        self._encoder = None
        self._observation_shape = None
        self._validated = False
        self._compression_enabled = None
        
        # 观察空间配置
        self._original_dim = 656      # 原始观察空间维度
        self._compressed_dim = 116    # 压缩后观察空间维度
        
        ObservationSpaceManager._initialized = True
        logger.info("观察空间管理器初始化完成")
    
    def _init_encoder(self):
        """延迟初始化CompactObservationEncoder"""
        if self._encoder is None:
            try:
                from cardgame_ai.games.doudizhu.compact_observation import CompactObservationEncoder
                self._encoder = CompactObservationEncoder()
                logger.info("CompactObservationEncoder初始化成功")
            except ImportError as e:
                logger.warning(f"无法导入CompactObservationEncoder: {e}")
                self._encoder = None
    
    def _check_compression_config(self) -> bool:
        """检查Phase4配置中是否启用观察空间压缩"""
        if self._compression_enabled is not None:
            return self._compression_enabled
            
        try:
            # 尝试从Phase4配置中获取压缩设置
            from cardgame_ai.zhuchengxu.phase4.config import PRESET_CONFIGS, Phase4Preset
            
            # 默认检查expert预设
            expert_config = PRESET_CONFIGS.get(Phase4Preset.EXPERT, {})
            features = expert_config.get('features', {})
            self._compression_enabled = features.get('observation_compression', True)
            
            logger.info(f"从Phase4配置读取观察空间压缩设置: {self._compression_enabled}")
            
        except ImportError:
            # 如果无法导入Phase4配置，默认启用压缩
            self._compression_enabled = True
            logger.warning("无法读取Phase4配置，默认启用观察空间压缩")
        
        return self._compression_enabled
    
    @property
    def observation_shape(self) -> List[int]:
        """获取观察空间形状"""
        if self._observation_shape is None:
            if self._check_compression_config():
                # 启用压缩，使用116维
                self._observation_shape = [self._compressed_dim]
                logger.info(f"使用压缩观察空间: {self._observation_shape}")
            else:
                # 禁用压缩，使用原始656维
                self._observation_shape = [self._original_dim]
                logger.info(f"使用原始观察空间: {self._observation_shape}")
        
        return self._observation_shape
    
    @property
    def observation_dim(self) -> int:
        """获取观察空间维度"""
        return self.observation_shape[0]
    
    @property
    def compression_enabled(self) -> bool:
        """检查是否启用压缩"""
        return self._check_compression_config()
    
    def is_compression_enabled(self) -> bool:
        """检查是否启用压缩（方法版本）"""
        return self.compression_enabled
    
    @property
    def compression_ratio(self) -> float:
        """获取压缩比率"""
        if self.compression_enabled:
            return self._original_dim / self._compressed_dim
        return 1.0
    
    @property
    def memory_saved_percent(self) -> float:
        """获取内存节省百分比"""
        if self.compression_enabled:
            return (1 - self._compressed_dim / self._original_dim) * 100
        return 0.0
    
    def get_observation_shape(self) -> List[int]:
        """
        获取观察空间形状（公共接口）
        
        Returns:
            List[int]: 观察空间形状，例如[116]或[656]
        """
        return self.observation_shape
    
    def get_observation_dim(self) -> int:
        """
        获取观察空间维度（公共接口）
        
        Returns:
            int: 观察空间维度
        """
        return self.observation_dim
    
    def validate_consistency(self, config_shape: List[int], model_shape: List[int], 
                           env_shape: Optional[List[int]] = None) -> bool:
        """
        验证各组件的观察空间大小一致性
        
        Args:
            config_shape: 配置文件中的观察空间形状
            model_shape: 模型的观察空间形状
            env_shape: 环境的观察空间形状（可选）
            
        Returns:
            bool: 验证是否通过
            
        Raises:
            ValueError: 当观察空间大小不一致时
        """
        expected_shape = self.observation_shape
        
        # 检查所有值是否一致
        shapes = {
            "config": config_shape,
            "model": model_shape,
            "expected": expected_shape
        }
        
        if env_shape is not None:
            shapes["env"] = env_shape
        
        # 找出所有不同的值
        unique_shapes = set(tuple(shape) for shape in shapes.values())
        
        if len(unique_shapes) > 1:
            error_msg = f"观察空间形状不一致: {shapes}"
            logger.error(error_msg)
            
            # 详细的错误信息
            for name, shape in shapes.items():
                if shape != expected_shape:
                    logger.error(f"  {name}: {shape} (期望: {expected_shape})")
            
            raise ValueError(error_msg)
        
        self._validated = True
        logger.info(f"✅ 观察空间验证通过: 所有组件使用一致的形状 {expected_shape}")
        return True
    
    def encode_observation(self, raw_observation: np.ndarray) -> np.ndarray:
        """
        编码观察数据（如果启用压缩）
        
        Args:
            raw_observation: 原始观察数据
            
        Returns:
            np.ndarray: 编码后的观察数据
        """
        if not self.compression_enabled:
            return raw_observation
        
        self._init_encoder()
        
        if self._encoder is None:
            logger.warning("压缩编码器不可用，返回原始观察")
            return raw_observation
        
        # 如果是游戏状态对象，需要特殊处理
        if hasattr(raw_observation, 'hands') and hasattr(raw_observation, 'history'):
            # 这是一个DouDizhuState对象
            player_id = getattr(raw_observation, 'current_player', 0)
            return self._encoder.encode_state(raw_observation, player_id)
        
        # 如果是张量或数组，检查维度
        if isinstance(raw_observation, np.ndarray):
            if raw_observation.shape[-1] == self._original_dim:
                # 原始656维，需要压缩（简化版本）
                # 这里实现一个简单的压缩算法作为fallback
                compressed = self._simple_compress(raw_observation)
                logger.debug(f"使用简化压缩: {raw_observation.shape} -> {compressed.shape}")
                return compressed
            elif raw_observation.shape[-1] == self._compressed_dim:
                # 已经是116维，直接返回
                return raw_observation
        
        return raw_observation
    
    def _simple_compress(self, observation: np.ndarray) -> np.ndarray:
        """
        简化的观察压缩算法（fallback）
        
        Args:
            observation: 原始656维观察
            
        Returns:
            np.ndarray: 压缩到116维的观察
        """
        if observation.shape[-1] != self._original_dim:
            return observation
        
        # 使用等间距采样压缩
        batch_size = observation.shape[0] if observation.ndim > 1 else 1
        
        if observation.ndim == 1:
            observation = observation.reshape(1, -1)
            
        compressed = []
        for i in range(batch_size):
            obs = observation[i]
            # 等间距采样116个点
            indices = np.linspace(0, len(obs)-1, self._compressed_dim, dtype=int)
            compressed_obs = obs[indices]
            compressed.append(compressed_obs)
        
        result = np.array(compressed, dtype=np.float32)
        
        if batch_size == 1:
            result = result.reshape(-1)
            
        return result
    
    def set_compression_mode(self, enabled: bool):
        """
        设置压缩模式
        
        Args:
            enabled: 是否启用压缩
        """
        self._compression_enabled = enabled
        self.reset()  # 重置以便重新计算
        logger.info(f"观察空间压缩模式设置为: {'启用' if enabled else '禁用'}")
    
    def reset(self):
        """重置管理器状态（主要用于测试）"""
        self._encoder = None
        self._observation_shape = None
        self._validated = False
        logger.info("观察空间管理器已重置")
    
    def get_info(self) -> dict:
        """
        获取管理器详细信息
        
        Returns:
            dict: 包含配置信息的字典
        """
        return {
            "observation_shape": self.observation_shape,
            "observation_dim": self.observation_dim,
            "compression_enabled": self.compression_enabled,
            "compression_ratio": self.compression_ratio,
            "memory_saved_percent": f"{self.memory_saved_percent:.1f}%",
            "original_dim": self._original_dim,
            "compressed_dim": self._compressed_dim,
            "validated": self._validated
        }
    
    def __str__(self):
        return (f"ObservationSpaceManager(shape={self.observation_shape}, "
                f"compression={'enabled' if self.compression_enabled else 'disabled'}, "
                f"validated={self._validated})")


# 全局实例
_observation_space_manager = ObservationSpaceManager()


def get_observation_space_manager() -> ObservationSpaceManager:
    """获取全局观察空间管理器实例"""
    return _observation_space_manager


def get_observation_shape() -> List[int]:
    """快捷方法：获取观察空间形状"""
    return _observation_space_manager.get_observation_shape()


def get_observation_dim() -> int:
    """快捷方法：获取观察空间维度"""
    return _observation_space_manager.get_observation_dim()