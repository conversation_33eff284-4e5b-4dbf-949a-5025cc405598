# 质量验证测试框架

## 概述

本目录包含了斗地主AI训练系统的质量验证pytest测试集成。这些测试用于确保训练过程的各个环节都达到预期质量标准。

## 测试结构

```
tests/quality/
├── __init__.py              # 模块初始化
├── test_training_quality.py # 主要测试文件
└── README.md               # 本文档
```

## 测试类别

### 1. 前提条件测试 (TestPrerequisites)
- 环境检查（Python版本、PyTorch、CUDA）
- 配置文件验证
- GPU可用性测试

### 2. 架构一致性测试 (TestArchitectureConsistency)
- 116维观察空间一致性验证
- 组件间接口一致性
- 架构性能影响测试

### 3. 性能基准测试 (TestPerformanceBenchmarks)
- GPU利用率基准（目标>98%）
- 内存效率基准（目标减少64.7%）
- 批处理吞吐量测试

### 4. 实时监控测试 (TestRealtimeMonitoring)
- 监控器初始化和生命周期
- 指标追踪功能
- 预警系统测试
- 监控性能开销测试

### 5. 模型评估测试 (TestModelEvaluation)
- 基本评估功能
- 评估指标完整性
- 评估性能测试

### 6. 集成测试 (TestQualityIntegration)
- 完整质量验证流程
- 预警系统集成
- 报告生成测试

## 运行测试

### 使用pytest直接运行

```bash
# 运行所有质量测试
pytest tests/quality/ -v

# 运行特定测试类
pytest tests/quality/test_training_quality.py::TestPrerequisites -v

# 运行带特定标记的测试
pytest tests/quality/ -m quality -v
pytest tests/quality/ -m performance -v
pytest tests/quality/ -m "not slow" -v

# 生成HTML报告
pytest tests/quality/ --html=report.html --self-contained-html
```

### 使用便捷脚本

```bash
# 运行所有测试
python run_quality_tests.py --all

# 运行前提条件测试
python run_quality_tests.py --prerequisites

# 运行性能测试
python run_quality_tests.py --performance

# 运行CI测试套件（快速）
python run_quality_tests.py --ci

# 生成覆盖率报告
python run_quality_tests.py --coverage
```

## pytest标记

- `@pytest.mark.quality`: 所有质量验证测试
- `@pytest.mark.performance`: 性能相关测试
- `@pytest.mark.gpu`: 需要GPU的测试
- `@pytest.mark.slow`: 慢速测试（>30秒）
- `@pytest.mark.integration`: 集成测试
- `@pytest.mark.order(n)`: 测试执行顺序

## CI/CD集成

### GitHub Actions

质量测试已集成到GitHub Actions工作流中：

```yaml
# .github/workflows/quality-tests.yml
- 自动在PR和推送时运行
- 支持手动触发和选择测试范围
- 生成测试报告和覆盖率数据
- 在PR中自动评论测试结果
```

### 本地CI测试

```bash
# 模拟CI环境运行测试
pytest tests/quality/ -m "not slow and not gpu" --maxfail=3
```

## 配置

### pytest.ini配置

项目根目录的`pytest.ini`文件包含了所有必要的pytest配置：

- 测试标记定义
- 覆盖率配置
- 日志设置
- 超时设置

### 自定义配置

可以通过环境变量或配置文件自定义测试行为：

```python
# conftest.py中的fixture
@pytest.fixture
def test_config():
    return {
        "algorithm": {...},
        "environment": {...},
        "training": {...}
    }
```

## 扩展测试

### 添加新的测试类

```python
class TestNewFeature:
    """新功能测试"""
    
    @pytest.mark.quality
    def test_feature_basic(self):
        """基本功能测试"""
        assert True
    
    @pytest.mark.performance
    def test_feature_performance(self):
        """性能测试"""
        assert True
```

### 添加新的fixture

在`conftest.py`中添加：

```python
@pytest.fixture
def new_fixture():
    """新的测试fixture"""
    return SomeTestObject()
```

## 最佳实践

1. **测试隔离**: 每个测试应该独立运行，不依赖其他测试
2. **合理使用标记**: 使用适当的标记来组织测试
3. **性能测试**: 性能测试应该有明确的基准和阈值
4. **清理资源**: 使用fixture的yield或teardown确保资源清理
5. **详细断言**: 使用描述性的断言消息

## 故障排除

### 常见问题

1. **CUDA不可用**: 使用`@pytest.mark.gpu`标记GPU测试，在无GPU环境自动跳过
2. **测试超时**: 调整pytest.ini中的timeout设置
3. **导入错误**: 确保项目已正确安装（`pip install -e .`）

### 调试技巧

```bash
# 显示详细输出
pytest tests/quality/ -vv

# 在第一个失败时停止
pytest tests/quality/ -x

# 显示print输出
pytest tests/quality/ -s

# 运行上次失败的测试
pytest tests/quality/ --lf
```

## 贡献指南

1. 新测试应该遵循现有的命名和组织约定
2. 使用适当的pytest标记
3. 确保测试有清晰的文档字符串
4. 性能测试应该有明确的基准值
5. 提交前运行完整测试套件

## 相关文档

- [训练质量验证器使用指南](../../docs/quality_validator_usage.md)
- [pytest官方文档](https://docs.pytest.org/)
- [项目测试策略](../../docs/testing_strategy.md)