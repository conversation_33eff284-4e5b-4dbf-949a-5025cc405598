"""
斗地主状态管理模块基础导入
"""

import numpy as np
from typing import Dict, List, Tuple, Optional, Any, Set
from enum import Enum
from dataclasses import dataclass
from cardgame_ai.utils.logging import get_logger

logger = get_logger(__name__)


class GamePhase(Enum):
    """游戏阶段"""
    BIDDING = "bidding"      # 叫地主
    PLAYING = "playing"      # 出牌
    FINISHED = "finished"    # 结束

class PlayerRole(Enum):
    """玩家角色"""
    LANDLORD = "landlord"    # 地主
    FARMER1 = "farmer1"      # 农民1
    FARMER2 = "farmer2"      # 农民2

@dataclass
class CardInfo:
    """牌信息"""
    suit: str       # 花色
    rank: int       # 点数
    value: int      # 值
    
@dataclass  
class GameScore:
    """游戏得分"""
    landlord_score: int = 0
    farmer_score: int = 0
    round_multiplier: int = 1
