"""
5层日志实时分析系统
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

实时监控和分析所有5层日志：DEBUG, TRAINING, METRICS, GAME, SUMMARY + MCTS
提供自动异常检测、趋势分析、模式匹配和智能告警

作者: Claude AI
版本: 3.0
日期: 2025-07-06
"""

import os
import re
import time
import threading
import queue
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Set, Pattern, Callable, Any
from dataclasses import dataclass, field
from collections import defaultdict, deque
from enum import Enum
import json

from cardgame_ai.utils.logging import get_logger

logger = get_logger(__name__)


class LogLevel(Enum):
    """日志级别分类"""
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"
    DEBUG = "DEBUG"


class AlertLevel(Enum):
    """告警级别"""
    LOW = "低"
    MEDIUM = "中"
    HIGH = "高"
    CRITICAL = "紧急"


@dataclass
class LogPattern:
    """日志模式匹配定义"""
    name: str
    pattern: str
    level: LogLevel
    alert_level: AlertLevel
    description: str
    action: Optional[str] = None
    cooldown: int = 60  # 告警冷却时间（秒）
    count_threshold: int = 1  # 触发阈值
    time_window: int = 300  # 时间窗口（秒）


@dataclass
class LogMetrics:
    """日志指标统计"""
    total_lines: int = 0
    error_count: int = 0
    warning_count: int = 0
    critical_count: int = 0
    info_count: int = 0
    debug_count: int = 0
    
    # 特定指标
    checkpoint_errors: int = 0
    gpu_warnings: int = 0
    memory_warnings: int = 0
    training_errors: int = 0
    mcts_errors: int = 0
    
    # 时间戳
    last_update: datetime = field(default_factory=datetime.now)
    start_time: datetime = field(default_factory=datetime.now)


@dataclass
class Alert:
    """告警信息"""
    timestamp: datetime
    level: AlertLevel
    category: str
    pattern_name: str
    message: str
    file_path: str
    line_number: int
    count: int
    details: Dict[str, Any] = field(default_factory=dict)


class LogFileWatcher:
    """单个日志文件监控器"""
    
    def __init__(self, file_path: Path, category: str, analyzer: 'RealtimeLogAnalyzer'):
        self.file_path = file_path
        self.category = category
        self.analyzer = analyzer
        self.last_position = 0
        self.last_size = 0
        self.is_running = False
        self.thread = None
        
    def start(self):
        """启动文件监控"""
        if self.is_running:
            return
            
        self.is_running = True
        self.thread = threading.Thread(target=self._watch_loop, daemon=True)
        self.thread.start()
        logger.info(f"✅ 启动日志监控: {self.category} - {self.file_path}")
        
    def stop(self):
        """停止文件监控"""
        self.is_running = False
        if self.thread:
            self.thread.join(timeout=2)
            
    def _watch_loop(self):
        """监控循环"""
        while self.is_running:
            try:
                if self.file_path.exists():
                    current_size = self.file_path.stat().st_size
                    
                    if current_size > self.last_size:
                        # 文件有新内容
                        self._read_new_lines()
                        self.last_size = current_size
                    elif current_size < self.last_size:
                        # 文件被截断或重建
                        self.last_position = 0
                        self.last_size = current_size
                        self._read_new_lines()
                        
                time.sleep(0.5)  # 500ms检查间隔
                
            except Exception as e:
                logger.error(f"日志监控异常 {self.category}: {e}")
                time.sleep(1)
                
    def _read_new_lines(self):
        """读取新行"""
        try:
            with open(self.file_path, 'r', encoding='utf-8', errors='ignore') as f:
                f.seek(self.last_position)
                
                for line_num, line in enumerate(f, start=1):
                    line = line.strip()
                    if line:
                        # 发送到分析器
                        self.analyzer.process_log_line(
                            line, self.category, self.file_path, 
                            self.last_position + line_num
                        )
                        
                self.last_position = f.tell()
                
        except Exception as e:
            logger.error(f"读取日志文件失败 {self.category}: {e}")


class RealtimeLogAnalyzer:
    """5层日志实时分析系统"""
    
    def __init__(self, logs_dir: str = "logs"):
        self.logs_dir = Path(logs_dir)
        self.watchers: Dict[str, LogFileWatcher] = {}
        self.metrics: Dict[str, LogMetrics] = {}
        self.alerts: deque = deque(maxlen=1000)  # 保留最近1000条告警
        self.alert_history: Dict[str, datetime] = {}  # 告警冷却跟踪
        
        # 模式计数器（用于阈值检测）
        self.pattern_counts: Dict[str, deque] = defaultdict(lambda: deque(maxlen=100))
        
        # 统计锁
        self._stats_lock = threading.Lock()
        
        # 初始化日志模式
        self._init_log_patterns()
        
        # 启动统计报告线程
        self._start_stats_reporter()
        
    def _init_log_patterns(self):
        """初始化日志模式匹配规则"""
        self.patterns: Dict[str, List[LogPattern]] = {
            "summary": [
                LogPattern(
                    name="检查点保存失败",
                    pattern=r"检查点.*(?:失败|错误|不存在)",
                    level=LogLevel.ERROR,
                    alert_level=AlertLevel.HIGH,
                    description="检查点保存系统故障",
                    action="检查磁盘空间和权限",
                    count_threshold=1
                ),
                LogPattern(
                    name="序列化错误",
                    pattern=r"Can't get local object.*EfficientZero",
                    level=LogLevel.CRITICAL,
                    alert_level=AlertLevel.CRITICAL,
                    description="模型序列化失败",
                    action="需要修复模型架构",
                    count_threshold=1
                ),
                LogPattern(
                    name="训练异常退出",
                    pattern=r"训练.*(?:异常|失败|中断|crash)",
                    level=LogLevel.ERROR,
                    alert_level=AlertLevel.HIGH,
                    description="训练进程异常终止"
                )
            ],
            "metrics": [
                LogPattern(
                    name="GPU利用率过低",
                    pattern=r"GPU利用率较低.*(\d+)%",
                    level=LogLevel.WARNING,
                    alert_level=AlertLevel.MEDIUM,
                    description="GPU性能未充分利用",
                    count_threshold=5,
                    time_window=60
                ),
                LogPattern(
                    name="内存泄漏检测",
                    pattern=r"检测到可能的内存泄漏.*(\d+\.?\d*)MB",
                    level=LogLevel.WARNING,
                    alert_level=AlertLevel.MEDIUM,
                    description="可能存在内存泄漏",
                    count_threshold=3
                ),
                LogPattern(
                    name="检查点文件过小",
                    pattern=r"检查点文件过小.*\((\d+\.\d+)MB\)",
                    level=LogLevel.ERROR,
                    alert_level=AlertLevel.HIGH,
                    description="检查点保存不完整",
                    count_threshold=1
                )
            ],
            "training": [
                LogPattern(
                    name="训练损失异常",
                    pattern=r"loss.*(?:nan|inf|异常|explosion)",
                    level=LogLevel.ERROR,
                    alert_level=AlertLevel.HIGH,
                    description="训练损失值异常"
                ),
                LogPattern(
                    name="梯度爆炸",
                    pattern=r"gradient.*(?:explosion|clip|异常)",
                    level=LogLevel.WARNING,
                    alert_level=AlertLevel.MEDIUM,
                    description="梯度值异常"
                )
            ],
            "games": [
                LogPattern(
                    name="游戏环境错误",
                    pattern=r"game.*(?:error|failed|异常|失败)",
                    level=LogLevel.ERROR,
                    alert_level=AlertLevel.MEDIUM,
                    description="游戏环境运行异常"
                )
            ],
            "mcts": [
                LogPattern(
                    name="MCTS搜索异常",
                    pattern=r"mcts.*(?:error|timeout|异常|超时)",
                    level=LogLevel.ERROR,
                    alert_level=AlertLevel.MEDIUM,
                    description="MCTS搜索异常"
                )
            ],
            "debug": [
                LogPattern(
                    name="严重调试错误",
                    pattern=r"(?:critical|fatal|严重|致命)",
                    level=LogLevel.CRITICAL,
                    alert_level=AlertLevel.HIGH,
                    description="严重系统错误"
                )
            ]
        }
        
    def start_monitoring(self):
        """启动所有日志监控"""
        logger.info("🚀 启动5层日志实时分析系统...")
        
        # 定义日志文件映射
        log_categories = {
            "summary": "summary",
            "metrics": "metrics", 
            "training": "training",
            "games": "games",
            "mcts": "mcts",
            "debug": "debug"
        }
        
        # 获取今天的日期
        today = datetime.now().strftime("%Y%m%d")
        
        for category, dir_name in log_categories.items():
            # 创建指标对象
            self.metrics[category] = LogMetrics()
            
            # 寻找日志文件（支持多种命名格式）
            log_dir = self.logs_dir / dir_name
            if not log_dir.exists():
                log_dir.mkdir(parents=True, exist_ok=True)
                
            # 查找日志文件
            log_files = []
            possible_names = [
                f"{today}.log",
                f"{category}_{today}.log", 
                f"{category}.log"
            ]
            
            for name in possible_names:
                log_file = log_dir / name
                if log_file.exists():
                    log_files.append(log_file)
                    
            if not log_files:
                # 创建一个新的日志文件来监控
                log_file = log_dir / f"{today}.log"
                log_files.append(log_file)
                
            # 为每个找到的文件创建监控器
            for log_file in log_files:
                watcher_key = f"{category}_{log_file.name}"
                self.watchers[watcher_key] = LogFileWatcher(log_file, category, self)
                self.watchers[watcher_key].start()
                
        logger.info(f"✅ 启动了 {len(self.watchers)} 个日志监控器")
        
    def stop_monitoring(self):
        """停止所有监控"""
        logger.info("🛑 停止日志监控...")
        for watcher in self.watchers.values():
            watcher.stop()
        self.watchers.clear()
        
    def process_log_line(self, line: str, category: str, file_path: Path, line_number: int):
        """处理单行日志"""
        timestamp = datetime.now()
        
        # 更新基础统计
        with self._stats_lock:
            metrics = self.metrics.get(category, LogMetrics())
            metrics.total_lines += 1
            metrics.last_update = timestamp
            
            # 分析日志级别
            line_lower = line.lower()
            if 'error' in line_lower or '❌' in line:
                metrics.error_count += 1
                if 'checkpoint' in line_lower:
                    metrics.checkpoint_errors += 1
                elif 'training' in line_lower or 'train' in line_lower:
                    metrics.training_errors += 1
            elif 'warning' in line_lower or '⚠️' in line:
                metrics.warning_count += 1
                if 'gpu' in line_lower:
                    metrics.gpu_warnings += 1
                elif 'memory' in line_lower or '内存' in line:
                    metrics.memory_warnings += 1
            elif 'critical' in line_lower or 'fatal' in line_lower:
                metrics.critical_count += 1
            elif 'info' in line_lower:
                metrics.info_count += 1
            elif 'debug' in line_lower:
                metrics.debug_count += 1
                
            self.metrics[category] = metrics
            
        # 模式匹配检测
        self._check_patterns(line, category, file_path, line_number, timestamp)
        
    def _check_patterns(self, line: str, category: str, file_path: Path, 
                       line_number: int, timestamp: datetime):
        """检查日志行是否匹配告警模式"""
        patterns = self.patterns.get(category, [])
        
        for pattern in patterns:
            if re.search(pattern.pattern, line, re.IGNORECASE):
                # 记录模式匹配
                pattern_key = f"{category}_{pattern.name}"
                self.pattern_counts[pattern_key].append(timestamp)
                
                # 检查阈值
                recent_count = self._count_recent_matches(pattern_key, pattern.time_window)
                
                if recent_count >= pattern.count_threshold:
                    # 检查冷却时间
                    if self._should_alert(pattern_key, pattern.cooldown):
                        self._create_alert(
                            pattern, line, category, file_path, 
                            line_number, recent_count, timestamp
                        )
                        
    def _count_recent_matches(self, pattern_key: str, time_window: int) -> int:
        """统计时间窗口内的匹配次数"""
        now = datetime.now()
        cutoff = now - timedelta(seconds=time_window)
        
        counts = self.pattern_counts[pattern_key]
        recent_matches = [t for t in counts if t >= cutoff]
        
        # 更新deque只保留最近的匹配
        self.pattern_counts[pattern_key] = deque(recent_matches, maxlen=100)
        
        return len(recent_matches)
        
    def _should_alert(self, pattern_key: str, cooldown: int) -> bool:
        """检查是否应该发送告警（考虑冷却时间）"""
        now = datetime.now()
        last_alert = self.alert_history.get(pattern_key)
        
        if last_alert is None or (now - last_alert).total_seconds() >= cooldown:
            self.alert_history[pattern_key] = now
            return True
        return False
        
    def _create_alert(self, pattern: LogPattern, line: str, category: str,
                     file_path: Path, line_number: int, count: int, timestamp: datetime):
        """创建告警"""
        alert = Alert(
            timestamp=timestamp,
            level=pattern.alert_level,
            category=category,
            pattern_name=pattern.name,
            message=f"{pattern.description}: {line[:200]}",
            file_path=str(file_path),
            line_number=line_number,
            count=count,
            details={
                "pattern": pattern.pattern,
                "action": pattern.action,
                "full_line": line
            }
        )
        
        self.alerts.append(alert)
        
        # 记录告警日志
        alert_emoji = {"低": "💡", "中": "⚠️", "高": "🚨", "紧急": "🔥"}
        logger.warning(
            f"{alert_emoji.get(alert.level.value, '🔔')} "
            f"[{alert.level.value}] {alert.category.upper()}: {alert.pattern_name} "
            f"(计数: {count})"
        )
        
    def get_realtime_stats(self) -> Dict[str, Any]:
        """获取实时统计信息"""
        with self._stats_lock:
            stats = {
                "timestamp": datetime.now().isoformat(),
                "monitoring_active": len(self.watchers),
                "categories": {},
                "alerts": {
                    "total": len(self.alerts),
                    "recent_1h": len([a for a in self.alerts 
                                    if (datetime.now() - a.timestamp).total_seconds() < 3600]),
                    "by_level": {}
                },
                "top_patterns": {}
            }
            
            # 分类统计
            for category, metrics in self.metrics.items():
                if metrics.total_lines > 0:
                    runtime = (datetime.now() - metrics.start_time).total_seconds()
                    stats["categories"][category] = {
                        "total_lines": metrics.total_lines,
                        "errors": metrics.error_count,
                        "warnings": metrics.warning_count,
                        "error_rate": metrics.error_count / metrics.total_lines * 100,
                        "lines_per_second": metrics.total_lines / max(runtime, 1),
                        "last_update": metrics.last_update.isoformat(),
                        "special_counts": {
                            "checkpoint_errors": metrics.checkpoint_errors,
                            "gpu_warnings": metrics.gpu_warnings,
                            "memory_warnings": metrics.memory_warnings,
                            "training_errors": metrics.training_errors
                        }
                    }
                    
            # 告警级别统计
            for alert in self.alerts:
                level = alert.level.value
                stats["alerts"]["by_level"][level] = stats["alerts"]["by_level"].get(level, 0) + 1
                
            # 热门模式
            pattern_stats = {}
            for pattern_key, counts in self.pattern_counts.items():
                if counts:
                    pattern_stats[pattern_key] = len(counts)
                    
            stats["top_patterns"] = dict(sorted(pattern_stats.items(), 
                                              key=lambda x: x[1], reverse=True)[:10])
                                              
            return stats
            
    def get_recent_alerts(self, limit: int = 20) -> List[Dict[str, Any]]:
        """获取最近的告警"""
        recent_alerts = list(self.alerts)[-limit:]
        return [
            {
                "timestamp": alert.timestamp.isoformat(),
                "level": alert.level.value,
                "category": alert.category,
                "pattern": alert.pattern_name,
                "message": alert.message,
                "count": alert.count,
                "file": alert.file_path,
                "line": alert.line_number
            }
            for alert in reversed(recent_alerts)
        ]
        
    def _start_stats_reporter(self):
        """启动统计报告线程"""
        def report_loop():
            while True:
                try:
                    time.sleep(30)  # 30秒报告一次
                    stats = self.get_realtime_stats()
                    
                    # 简化报告
                    total_errors = sum(cat.get("errors", 0) for cat in stats["categories"].values())
                    total_lines = sum(cat.get("total_lines", 0) for cat in stats["categories"].values())
                    recent_alerts = stats["alerts"]["recent_1h"]
                    
                    if total_lines > 0:
                        logger.info(
                            f"📊 日志分析报告: 总行数={total_lines}, 错误={total_errors}, "
                            f"近1小时告警={recent_alerts}, 监控器={stats['monitoring_active']}"
                        )
                        
                except Exception as e:
                    logger.error(f"统计报告异常: {e}")
                    
        reporter_thread = threading.Thread(target=report_loop, daemon=True)
        reporter_thread.start()


# 全局实例
_analyzer_instance: Optional[RealtimeLogAnalyzer] = None
_analyzer_lock = threading.Lock()


def get_realtime_analyzer(logs_dir: str = "logs") -> RealtimeLogAnalyzer:
    """获取实时日志分析器单例"""
    global _analyzer_instance
    
    if _analyzer_instance is None:
        with _analyzer_lock:
            if _analyzer_instance is None:
                _analyzer_instance = RealtimeLogAnalyzer(logs_dir)
                
    return _analyzer_instance


def start_realtime_analysis(logs_dir: str = "logs") -> RealtimeLogAnalyzer:
    """启动实时日志分析"""
    analyzer = get_realtime_analyzer(logs_dir)
    analyzer.start_monitoring()
    logger.info("✅ 5层日志实时分析系统已启动")
    return analyzer


if __name__ == "__main__":
    # 测试运行
    print("🚀 启动5层日志实时分析系统测试...")
    analyzer = start_realtime_analysis()
    
    try:
        # 运行60秒测试
        time.sleep(60)
        stats = analyzer.get_realtime_stats()
        print("\n📊 测试统计结果:")
        print(json.dumps(stats, indent=2, ensure_ascii=False))
        
        alerts = analyzer.get_recent_alerts(10)
        if alerts:
            print("\n🚨 最近告警:")
            for alert in alerts:
                print(f"  {alert['timestamp']} [{alert['level']}] {alert['pattern']}: {alert['message'][:100]}")
                
    except KeyboardInterrupt:
        print("\n🛑 停止测试")
    finally:
        analyzer.stop_monitoring()