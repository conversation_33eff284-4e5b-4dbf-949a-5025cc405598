"""
算法配置模块
统一管理所有算法相关配置（EfficientZero, HAPPO, MAPPO等）
"""

from dataclasses import dataclass, field
from typing import Optional, Dict, Any, List
from .base import BaseConfig, ConfigRegistry

@dataclass
class EfficientZeroConfig(BaseConfig):
    """EfficientZero算法配置"""
    # 网络架构
    observation_shape: List[int] = field(default_factory=lambda: [116])
    action_space_size: int = 2300
    support_size: int = 300
    hidden_size: int = 512
    
    # 训练参数
    learning_rate: float = 0.0003
    weight_decay: float = 0.0001
    momentum: float = 0.9
    
    # MCTS参数
    num_simulations: int = 800
    discount: float = 0.997
    root_dirichlet_alpha: float = 0.3
    root_exploration_fraction: float = 0.25
    pb_c_base: float = 19652
    pb_c_init: float = 1.25
    
    # 价值估计
    value_prefix: bool = True
    value_prefix_steps: int = 5
    use_sve: bool = True  # Search-based Value Estimation
    
    # 自监督学习
    use_simsiam: bool = True
    simsiam_proj_hidden_dim: int = 2048
    simsiam_proj_out_dim: int = 2048
    simsiam_pred_hidden_dim: int = 512
    
    # 其他
    td_steps: int = 5
    unroll_steps: int = 5
    batch_size: int = 256
    replay_buffer_size: int = 100000
    priority_alpha: float = 0.6
    priority_beta: float = 0.4
    
    def validate(self) -> bool:
        """验证配置有效性"""
        if self.hidden_size <= 0:
            raise ValueError("hidden_size must be positive")
        if self.learning_rate <= 0:
            raise ValueError("learning_rate must be positive")
        if self.batch_size <= 0:
            raise ValueError("batch_size must be positive")
        return True

@dataclass
class HAPPOConfig(BaseConfig):
    """HAPPO算法配置"""
    # 基础参数
    lr: float = 3e-4
    gamma: float = 0.99
    gae_lambda: float = 0.95
    clip_epsilon: float = 0.2
    value_loss_coef: float = 0.5
    entropy_coef: float = 0.01
    max_grad_norm: float = 0.5
    ppo_epochs: int = 10
    mini_batch_size: int = 64
    
    # 异构智能体设置
    use_heterogeneous_agents: bool = True
    landlord_hidden_dim: int = 512
    farmer_hidden_dim: int = 256
    
    # 优势分解
    advantage_decomposition: str = "credit"  # credit, shapley, attention
    credit_temperature: float = 1.0
    
    # 农民通信
    enable_farmer_communication: bool = True
    communication_dim: int = 64  # 调整为适合116维观察空间的大小
    communication_rounds: int = 2
    
    # 协作奖励
    cooperation_reward_weight: float = 0.3
    synergy_bonus: float = 0.2
    
    # 角色特定学习率
    landlord_lr: float = 0.0003
    farmer_lr: float = 0.0005
    
    # 序列化训练
    sequential_updates: bool = True
    update_order: List[str] = field(default_factory=lambda: ["landlord", "farmer1", "farmer2"])
    
    def validate(self) -> bool:
        """验证配置有效性"""
        if self.lr <= 0 or self.landlord_lr <= 0 or self.farmer_lr <= 0:
            raise ValueError("Learning rates must be positive")
        if self.clip_epsilon <= 0:
            raise ValueError("clip_epsilon must be positive")
        return True

@dataclass
class MAPPOConfig(BaseConfig):
    """MAPPO算法配置"""
    # 基础PPO参数
    lr: float = 5e-4
    gamma: float = 0.99
    gae_lambda: float = 0.95
    clip_epsilon: float = 0.2
    value_loss_coef: float = 0.5
    entropy_coef: float = 0.01
    max_grad_norm: float = 0.5
    
    # 多智能体特定
    use_central_critic: bool = True
    use_shared_policy: bool = False
    num_agents: int = 3
    
    # 训练参数
    ppo_epochs: int = 15
    mini_batch_size: int = 32
    
    def validate(self) -> bool:
        """验证配置有效性"""
        if self.num_agents <= 0:
            raise ValueError("num_agents must be positive")
        return True

@dataclass
class AlgorithmConfig(BaseConfig):
    """统一的算法配置"""
    name: str = "efficient_zero_v2"  # 算法名称
    
    # 各算法的具体配置
    efficient_zero: Optional[EfficientZeroConfig] = None
    happo: Optional[HAPPOConfig] = None
    mappo: Optional[MAPPOConfig] = None
    
    # 通用参数
    device: str = "cuda"
    seed: int = 42
    num_workers: int = 8
    
    def __post_init__(self):
        """初始化后处理"""
        super().__post_init__()
        
        # 根据算法名称初始化对应配置
        if self.name == "efficient_zero_v2" and self.efficient_zero is None:
            self.efficient_zero = EfficientZeroConfig()
        elif self.name == "happo" and self.happo is None:
            self.happo = HAPPOConfig()
        elif self.name == "mappo" and self.mappo is None:
            self.mappo = MAPPOConfig()
            
    def get_active_config(self) -> BaseConfig:
        """获取当前激活的算法配置"""
        if self.name == "efficient_zero_v2":
            return self.efficient_zero
        elif self.name == "happo":
            return self.happo
        elif self.name == "mappo":
            return self.mappo
        else:
            raise ValueError(f"Unknown algorithm: {self.name}")
            
    def validate(self) -> bool:
        """验证配置有效性"""
        active_config = self.get_active_config()
        if active_config:
            return active_config.validate()
        return True

# 注册配置类
ConfigRegistry.register(EfficientZeroConfig)
ConfigRegistry.register(HAPPOConfig)
ConfigRegistry.register(MAPPOConfig)
ConfigRegistry.register(AlgorithmConfig)
