# CLAUDE.md

This file provides guidance to <PERSON> Code (claude.ai/code) when working with code in this repository.

本文件为 Claude Code (claude.ai/code) 在处理此仓库代码时提供指导。

> 最后更新：2025-07-06

## 项目概述

斗地主 AI - 使用 EfficientZero V2 + HAPPO + PPB-MCTS 的超人类水平多智能体强化学习系统。

**核心目标**：
- 目标胜率：85-95%
- 在策略、模式识别和规划方面超越人类专家玩家
- 实现农民智能体的有效合作
- 单机优化架构（自 2025-06-14 起已移除分布式代码）

## 关键要求

1. **运行任何 Python 代码前必须激活 conda 环境 `cardgame`**
2. **GPU 是必需的** - 没有 CUDA 设备训练将失败
3. **需要 Python 3.8-3.11**（推荐 3.10）
4. **安装项目**：
   ```bash
   # 完整安装（推荐）
   pip install -r requirements.txt
   
   # 或最小安装（仅核心功能）
   pip install torch>=2.0.0 numpy>=1.21.0 PyYAML>=6.0 Jinja2>=3.0.0 psutil>=5.8.0 nvidia-ml-py>=12.535.133
   ```

## 基本命令

### 训练
```bash
# 基本训练（使用 optimized_fixed_config.yaml）
python cardgame_ai/zhuchengxu/auto_deploy.py

# 使用自定义配置文件
python cardgame_ai/zhuchengxu/auto_deploy.py --config my_config.yaml
```

注意：Phase 4 功能现在通过 `optimized_fixed_config.yaml` 配置，不再使用命令行参数。

### 测试与质量
```bash
# 运行所有测试
pytest
pytest -m smoke                 # 快速验证（3 分钟）
pytest -m core                  # 核心测试
pytest --cov=cardgame_ai        # 生成覆盖率报告

# 代码质量
black cardgame_ai/              # 格式化代码（88 字符行长度）
flake8 cardgame_ai/             # 代码检查（最大行长度 100）
mypy cardgame_ai/               # 类型检查

# 116 维架构验证
python comprehensive_dimension_test.py
```

### 进程管理
```bash
# 检查训练进程
ps aux | grep python | grep cardgame

# 终止训练（如果存在）
kill -TERM <PID>

# 查看检查点
ls -la checkpoints/
```

## 架构

### 116 维统一架构（重要更新）

**观察空间**：从 656 维压缩到 116 维
- **管理器**：`cardgame_ai/core/config/observation_space_manager.py`（单例模式）
- **编码器**：`cardgame_ai/games/doudizhu/compact_observation.py`
- **压缩效果**：5.66 倍压缩比，82.3% 内存减少，4 倍以上编码速度

**关键规则**：必须使用 `ObservationSpaceManager` 进行维度管理：
```python
from cardgame_ai.core.config.observation_space_manager import get_observation_space_manager
obs_manager = get_observation_space_manager()
observation_shape = obs_manager.get_observation_shape()  # 返回 [116]
```

### 核心系统

1. **训练入口**：`cardgame_ai/zhuchengxu/optimized_main_training.py`
   - GPU 优化，强制使用 CUDA
   - 集成内存管理和性能优化

2. **配置文件**：`optimized_fixed_config.yaml`
   - 状态形状：`[116]`（之前是 656）
   - Phase 4 预设：`expert`
   - 观察压缩：`true`

3. **算法**：
   - **EfficientZero V2**：基于搜索的价值估计
   - **HAPPO**：用于农民合作的异构智能体 PPO
   - **PPB-MCTS**：并行批处理（300%+ 加速）

4. **关键接口**：
   ```python
   # 动作映射（永远不要硬编码大小）
   from cardgame_ai.core.action_mapping import get_global_action_mapper
   mapper = get_global_action_mapper()
   
   # 组件管理（快速失败初始化）
   from cardgame_ai.core.component_manager import ComponentManager
   manager = ComponentManager.get_instance()
   ```

### 动作空间设计
- **游戏动作**：198 个实际动作
- **神经网络**：2300 维度（为未来扩展预留）
  - CardGroup：0-1999
  - BidAction：2000-2099
  - GrabAction：2100-2199
  - PassAction：2200-2299

## 开发指南

### 必须遵守的实践
- **维度管理**：始终使用 `ObservationSpaceManager`，永远不要硬编码 656 或 116
- **动作映射**：始终使用 `get_global_action_mapper()`
- **组件初始化**：使用 `ComponentManager` 实现快速失败行为
- **配置**：使用结构化配置类（如 `EfficientZeroConfig`）
- **错误处理**：初始化错误时立即失败

### 禁止的做法
- ❌ 创建简化/临时/测试版本
- ❌ 跳过问题或使用后备策略
- ❌ 在生产代码中使用模拟数据
- ❌ 隐藏错误并继续执行
- ❌ 使用宽泛的 try/except 块
- ❌ 硬编码维度大小（656 或 116）

### 测试要求
- 所有测试文件必须在 `tests/` 目录中
- 保持 85%+ 的测试覆盖率（在 pytest.ini 中强制执行）
- 使用 pytest 标记：`smoke`、`core`、`training`、`integration`、`slow`、`gpu`、`unit`、`performance`、`quality`、`monitoring`、`evaluation`、`critical`、`order`
- 测试覆盖率配置在 pytest.ini 中，包含 HTML、XML 报告生成

## 进程管理和故障排除

### 进程管理脚本
- `终止训练进程.py`: 专用训练进程管理工具
- `show_models_info.py`: 模型信息查看
- `cardgame_ai/utils/log_query.py`: 日志查询工具

### 常见故障排除
```bash
# 检查CUDA可用性
python -c "import torch; print(torch.cuda.is_available())"

# 检查GPU内存
nvidia-smi

# 清理PyTorch缓存
python -c "import torch; torch.cuda.empty_cache()"

# 检查conda环境
conda env list
conda activate cardgame
```

### WSL环境特殊配置
- GPU利用率目标自动适配为40%（`optimized_fixed_config.yaml`）
- 启用WSL环境检测和自动调优
- 考虑使用较小的批处理大小

## 最近更新

### 2025-07-06：CLAUDE.md优化更新
- 添加详细的依赖管理指南
- 增加配置文件说明
- 完善进程管理和故障排除指南
- 更新测试配置和标记说明

### 2025-07-05：116 维统一架构
- 解决了维度不匹配循环问题（656↔116）
- 创建了 `ObservationSpaceManager` 单例
- 更新所有组件使用 116 维观察空间
- 达到 83.3% 的维度一致性测试成功率

### 2025-07-03：HAPPO 算法修复
- 修复了 PPO 损失计算实现
- 添加了 GAE 优势估计
- 实现了正确的梯度更新

### 2025-06-29：Phase 4 集成
- 11 个高级功能全部集成
- 性能分析器：50-70% 训练速度提升
- GPU 利用率：98%+

## Phase 4 功能（通过 YAML 配置）

以下功能通过 `optimized_fixed_config.yaml` 控制：

- **intelligent_bidding**：多因子叫地主决策
- **humanlike_memory**：遗忘曲线模拟（5 个难度等级）
- **game_theory_bomb**：炸弹使用的纳什均衡
- **farmer_cooperation**：农民间的隐式信号传递
- **spring_rules**：春天/反春天检测
- **statistical_analysis**：Wilson 分数置信区间
- **decision_visualization**：MCTS 树可视化
- **observation_compression**：656→116 维度压缩
- **zero_copy_batch**：内存优化

预设模式：`minimal`、`balanced`、`full`、`expert`（当前使用 `expert`）

## 性能目标
- GPU 利用率：98%+
- 训练速度提升：50%+
- 内存减少：64.7%
- 目标胜率：85-95%
- 推理时间：<0.2ms（116 维架构）

## 硬件要求

| 级别 | 最低显存 | 推荐显存 | CPU | 内存 |
|------|----------|----------|-----|------|
| minimal | 2GB | 4GB | 4 核 | 8GB |
| balanced | 4GB | 6GB | 6 核 | 12GB |
| full | 6GB | 8GB | 8 核 | 16GB |
| expert | 8GB | 12GB+ | 8+ 核 | 16GB+ |

## 项目依赖

### 核心依赖 [CORE] (必需)
```bash
pip install torch>=2.0.0 torchvision>=0.15.0 torchaudio>=2.0.0 numpy>=1.21.0 PyYAML>=6.0 Jinja2>=3.0.0 psutil>=5.8.0 nvidia-ml-py>=12.535.133
```

### 依赖分级
- **🔥 核心依赖 [CORE]**: 运行训练脚本的最小依赖集合
- **🚀 自动部署 [AUTO-DEPLOY]**: 硬件检测、参数调优、配置生成
- **📊 可视化监控 [VIZ]**: 训练监控、数据可视化、进度显示
- **🧪 开发测试 [DEV]**: 代码质量、测试框架、调试工具
- **🎯 高级功能 [ADVANCED]**: 图神经网络、贝叶斯优化等

### 快速安装选项
```bash
# 最小核心安装 (约100MB)
pip install torch>=2.0.0 numpy>=1.21.0 PyYAML>=6.0 Jinja2>=3.0.0 psutil>=5.8.0 nvidia-ml-py>=12.535.133 tqdm>=4.62.0 colorlog>=6.0.0

# 完整安装 (约1GB)
pip install -r requirements.txt
```

## 配置文件说明

### 主要配置文件
- `optimized_fixed_config.yaml`: 固定优化配置，包含所有算法和Phase 4功能
- `configs/unified_training_config.yaml`: 统一训练配置
- `pytest.ini`: 测试配置，包含标记和覆盖率要求

### 重要配置项
- 观察空间: 116 维（从 656 维压缩）
- 动作空间: 2300 维神经网络空间
- Phase 4预设: `expert` 模式
- GPU利用率目标: 98%+（WSL环境适配为40%）