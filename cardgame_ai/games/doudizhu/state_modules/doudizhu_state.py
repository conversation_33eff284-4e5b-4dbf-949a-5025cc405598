"""
斗地主状态管理模块基础导入
"""

import numpy as np
from typing import Dict, List, Tuple, Optional, Any, Set
from enum import Enum
from dataclasses import dataclass
from cardgame_ai.utils.logging import get_logger

logger = get_logger(__name__)

from .state_manager import StateManager
from .card_manager import CardManager
from .state_types import GamePhase, PlayerRole, GameScore

class DoudizhuState:
    """斗地主状态类"""
    
    def __init__(self):
        """初始化斗地主状态"""
        self.state_manager = StateManager()
        self.observation_cache = {}
        logger.info("斗地主状态初始化完成")
    
    @property
    def current_player(self) -> str:
        """当前玩家"""
        return self.state_manager.current_player
    
    @property
    def phase(self) -> GamePhase:
        """当前阶段"""
        return self.state_manager.phase
    
    @property
    def landlord(self) -> Optional[str]:
        """地主"""
        return self.state_manager.landlord
    
    def get_observation(self, player: str) -> np.ndarray:
        """获取观察"""
        if player in self.observation_cache:
            return self.observation_cache[player]
        
        obs = self._compute_observation(player)
        self.observation_cache[player] = obs
        return obs
    
    def _compute_observation(self, player: str) -> np.ndarray:
        """计算观察"""
        # 基础状态向量
        state_vec = self.state_manager.get_state_vector()
        
        # 玩家手牌信息
        hand_cards = self.state_manager.card_manager.get_hand_cards(player)
        hand_encoding = self._encode_cards(hand_cards)
        
        # 合并观察
        obs = np.concatenate([state_vec, hand_encoding])
        return obs
    
    def _encode_cards(self, cards) -> np.ndarray:
        """编码牌"""
        # 简化的牌编码
        encoding = np.zeros(54)  # 54张牌
        for i, card in enumerate(cards):
            if i < 54:
                encoding[i] = 1
        return encoding
    
    def step(self, action):
        """执行动作"""
        # 清空观察缓存
        self.observation_cache.clear()
        
        # 执行动作逻辑
        self._process_action(action)
        
        # 检查游戏是否结束
        done = self.state_manager.is_game_finished()
        
        # 计算奖励
        reward = self._compute_reward() if done else 0
        
        # 下一回合
        if not done:
            self.state_manager.next_turn()
        
        return reward, done
    
    def _process_action(self, action):
        """处理动作"""
        # 简化的动作处理
        logger.debug(f"处理动作: {action}")
    
    def _compute_reward(self) -> float:
        """计算奖励"""
        winner = self.state_manager.get_winner()
        if winner == "landlord":
            return 1.0 if self.current_player == "landlord" else -1.0
        elif winner == "farmers":
            return 1.0 if self.current_player != "landlord" else -1.0
        return 0.0
    
    def reset(self):
        """重置状态"""
        self.state_manager = StateManager()
        self.observation_cache.clear()
        self.state_manager.start_bidding_phase()
        logger.info("状态重置完成")
