"""
数据缓冲模块
提供高效的数据缓冲和存储功能
"""

from typing import Any, List, Dict, Optional, Iterator
from collections import deque
from datetime import datetime, timedelta
import threading
import pickle
import json
from pathlib import Path

class CircularBuffer:
    """循环缓冲区"""
    
    def __init__(self, capacity: int):
        self.capacity = capacity
        self.buffer = deque(maxlen=capacity)
        self._lock = threading.Lock()
        
    def append(self, item: Any):
        """添加项目"""
        with self._lock:
            self.buffer.append(item)
            
    def extend(self, items: List[Any]):
        """批量添加"""
        with self._lock:
            self.buffer.extend(items)
            
    def get_all(self) -> List[Any]:
        """获取所有数据"""
        with self._lock:
            return list(self.buffer)
            
    def get_last_n(self, n: int) -> List[Any]:
        """获取最后n个数据"""
        with self._lock:
            return list(self.buffer)[-n:] if n <= len(self.buffer) else list(self.buffer)
            
    def clear(self):
        """清空缓冲区"""
        with self._lock:
            self.buffer.clear()
            
    def __len__(self) -> int:
        with self._lock:
            return len(self.buffer)
            
    def __iter__(self) -> Iterator[Any]:
        with self._lock:
            return iter(list(self.buffer))

class TimeSeriesBuffer:
    """时间序列缓冲区"""
    
    def __init__(self, capacity: int = 10000, ttl: Optional[timedelta] = None):
        self.capacity = capacity
        self.ttl = ttl
        self.data = deque(maxlen=capacity)
        self._lock = threading.Lock()
        
    def append(self, value: Any, timestamp: Optional[datetime] = None):
        """添加数据点"""
        timestamp = timestamp or datetime.now()
        
        with self._lock:
            self.data.append({
                'value': value,
                'timestamp': timestamp
            })
            
            # 清理过期数据
            if self.ttl:
                self._cleanup_expired(timestamp)
                
    def _cleanup_expired(self, current_time: datetime):
        """清理过期数据"""
        cutoff_time = current_time - self.ttl
        
        while self.data and self.data[0]['timestamp'] < cutoff_time:
            self.data.popleft()
            
    def get_range(self, start_time: datetime, end_time: datetime) -> List[Dict[str, Any]]:
        """获取时间范围内的数据"""
        with self._lock:
            result = []
            for item in self.data:
                if start_time <= item['timestamp'] <= end_time:
                    result.append(item)
            return result
            
    def get_latest(self, count: int = 1) -> List[Dict[str, Any]]:
        """获取最新数据"""
        with self._lock:
            return list(self.data)[-count:] if count <= len(self.data) else list(self.data)
            
    def get_values_in_range(self, start_time: datetime, end_time: datetime) -> List[Any]:
        """获取时间范围内的值"""
        data = self.get_range(start_time, end_time)
        return [item['value'] for item in data]
        
    def clear(self):
        """清空缓冲区"""
        with self._lock:
            self.data.clear()

class PersistentBuffer:
    """持久化缓冲区"""
    
    def __init__(self, file_path: str, max_memory_size: int = 10000):
        self.file_path = Path(file_path)
        self.max_memory_size = max_memory_size
        self.memory_buffer = CircularBuffer(max_memory_size)
        self._lock = threading.Lock()
        
        # 确保目录存在
        self.file_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 加载已有数据
        self._load_from_disk()
        
    def append(self, item: Any):
        """添加数据"""
        with self._lock:
            self.memory_buffer.append(item)
            
            # 定期保存到磁盘
            if len(self.memory_buffer) >= self.max_memory_size:
                self._save_to_disk()
                
    def _save_to_disk(self):
        """保存到磁盘"""
        try:
            # 读取现有数据
            existing_data = []
            if self.file_path.exists():
                with open(self.file_path, 'rb') as f:
                    existing_data = pickle.load(f)
                    
            # 合并新数据
            new_data = self.memory_buffer.get_all()
            all_data = existing_data + new_data
            
            # 保持最大容量
            if len(all_data) > self.max_memory_size * 10:  # 磁盘保存10倍内存容量
                all_data = all_data[-self.max_memory_size * 10:]
                
            # 保存到磁盘
            with open(self.file_path, 'wb') as f:
                pickle.dump(all_data, f)
                
            # 清空内存缓冲区
            self.memory_buffer.clear()
            
        except Exception as e:
            print(f"Error saving to disk: {e}")
            
    def _load_from_disk(self):
        """从磁盘加载"""
        try:
            if self.file_path.exists():
                with open(self.file_path, 'rb') as f:
                    data = pickle.load(f)
                    
                # 加载最新的数据到内存
                latest_data = data[-self.max_memory_size:]
                for item in latest_data:
                    self.memory_buffer.append(item)
                    
        except Exception as e:
            print(f"Error loading from disk: {e}")
            
    def get_all(self) -> List[Any]:
        """获取所有数据（内存+磁盘）"""
        with self._lock:
            # 先保存当前内存数据
            self._save_to_disk()
            
            # 加载所有磁盘数据
            if self.file_path.exists():
                with open(self.file_path, 'rb') as f:
                    return pickle.load(f)
            return []
            
    def get_latest(self, count: int) -> List[Any]:
        """获取最新数据"""
        with self._lock:
            memory_data = self.memory_buffer.get_all()
            
            if len(memory_data) >= count:
                return memory_data[-count:]
                
            # 需要从磁盘获取更多数据
            all_data = self.get_all()
            return all_data[-count:] if count <= len(all_data) else all_data

class MetricsBufferManager:
    """指标缓冲管理器"""
    
    def __init__(self, base_dir: str = "monitoring_buffers"):
        self.base_dir = Path(base_dir)
        self.buffers: Dict[str, Any] = {}
        self._lock = threading.Lock()
        
    def get_buffer(self, name: str, buffer_type: str = "circular", **kwargs) -> Any:
        """获取或创建缓冲区"""
        with self._lock:
            if name not in self.buffers:
                self.buffers[name] = self._create_buffer(name, buffer_type, **kwargs)
            return self.buffers[name]
            
    def _create_buffer(self, name: str, buffer_type: str, **kwargs) -> Any:
        """创建缓冲区"""
        if buffer_type == "circular":
            capacity = kwargs.get('capacity', 10000)
            return CircularBuffer(capacity)
        elif buffer_type == "timeseries":
            capacity = kwargs.get('capacity', 10000)
            ttl = kwargs.get('ttl')
            return TimeSeriesBuffer(capacity, ttl)
        elif buffer_type == "persistent":
            file_path = self.base_dir / f"{name}.pkl"
            max_memory_size = kwargs.get('max_memory_size', 10000)
            return PersistentBuffer(str(file_path), max_memory_size)
        else:
            raise ValueError(f"Unknown buffer type: {buffer_type}")
            
    def remove_buffer(self, name: str):
        """移除缓冲区"""
        with self._lock:
            self.buffers.pop(name, None)
            
    def clear_all(self):
        """清空所有缓冲区"""
        with self._lock:
            for buffer in self.buffers.values():
                if hasattr(buffer, 'clear'):
                    buffer.clear()
                    
    def get_buffer_stats(self) -> Dict[str, Dict[str, Any]]:
        """获取缓冲区统计信息"""
        with self._lock:
            stats = {}
            for name, buffer in self.buffers.items():
                stats[name] = {
                    'type': type(buffer).__name__,
                    'size': len(buffer) if hasattr(buffer, '__len__') else 0
                }
            return stats
