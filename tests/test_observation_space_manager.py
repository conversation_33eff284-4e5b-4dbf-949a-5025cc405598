#!/usr/bin/env python3
"""
ObservationSpaceManager单元测试

专门测试ObservationSpaceManager的各种功能和边界情况。

作者：AI系统
创建时间：2025-07-05
"""

import sys
import os
import pytest
import torch
import numpy as np
from unittest.mock import patch, MagicMock

# 添加项目路径
sys.path.insert(0, os.path.abspath('.'))

from cardgame_ai.core.config.observation_space_manager import (
    ObservationSpaceManager, 
    get_observation_space_manager
)


class TestObservationSpaceManager:
    """ObservationSpaceManager单元测试类"""
    
    def setup_method(self):
        """每个测试方法前的设置"""
        # 重置单例状态
        if hasattr(ObservationSpaceManager, '_instance'):
            ObservationSpaceManager._instance = None
        
    def test_singleton_pattern(self):
        """测试单例模式"""
        manager1 = get_observation_space_manager()
        manager2 = get_observation_space_manager()
        
        assert manager1 is manager2, "ObservationSpaceManager应该是单例"
        
    def test_initial_state(self):
        """测试初始状态"""
        manager = get_observation_space_manager()
        
        # 检查默认配置
        assert manager._original_dim == 656, "原始维度应为656"
        assert manager._compressed_dim == 116, "压缩维度应为116"
        assert manager.compression_enabled == True, "默认应启用压缩"
        
        # 检查计算属性
        assert manager.compression_ratio == pytest.approx(5.655, rel=1e-2), "压缩比计算错误"
        assert manager.memory_saved_percent == pytest.approx(82.3, rel=1e-1), "内存节省百分比计算错误"
        
    def test_get_observation_shape(self):
        """测试获取观察空间形状"""
        manager = get_observation_space_manager()
        
        # 启用压缩时
        manager.set_compression_mode(True)
        assert manager.get_observation_shape() == [116], "压缩模式下应返回116维"
        
        # 禁用压缩时
        manager.set_compression_mode(False)
        assert manager.get_observation_shape() == [656], "非压缩模式下应返回656维"
        
    def test_get_observation_dim(self):
        """测试获取观察空间维度"""
        manager = get_observation_space_manager()
        
        # 启用压缩时
        manager.set_compression_mode(True)
        assert manager.get_observation_dim() == 116, "压缩模式下应返回116"
        
        # 禁用压缩时
        manager.set_compression_mode(False)
        assert manager.get_observation_dim() == 656, "非压缩模式下应返回656"
        
    def test_compression_mode_toggle(self):
        """测试压缩模式切换"""
        manager = get_observation_space_manager()
        
        # 初始状态
        assert manager.compression_enabled == True
        
        # 关闭压缩
        manager.set_compression_mode(False)
        assert manager.compression_enabled == False
        assert manager.get_observation_dim() == 656
        
        # 重新开启压缩
        manager.set_compression_mode(True)
        assert manager.compression_enabled == True
        assert manager.get_observation_dim() == 116
        
    def test_reset_functionality(self):
        """测试重置功能"""
        manager = get_observation_space_manager()
        
        # 修改状态
        manager.set_compression_mode(False)
        assert manager.compression_enabled == False
        
        # 重置
        manager.reset()
        assert manager.compression_enabled == True, "重置后应恢复默认压缩模式"
        assert manager.get_observation_dim() == 116, "重置后应返回压缩维度"
        
    def test_simple_compress_numpy(self):
        """测试简单压缩功能 - NumPy数组"""
        manager = get_observation_space_manager()
        manager.set_compression_mode(True)
        
        # 测试数据
        batch_size = 8
        input_data = np.random.rand(batch_size, 656).astype(np.float32)
        
        # 执行压缩
        compressed = manager._simple_compress(input_data)
        
        # 验证结果
        assert compressed.shape == (batch_size, 116), f"压缩结果形状错误: {compressed.shape}"
        assert compressed.dtype == np.float32, "压缩结果数据类型应为float32"
        assert not np.allclose(compressed, 0), "压缩结果不应该全为零"
        
    def test_simple_compress_torch(self):
        """测试简单压缩功能 - PyTorch张量"""
        manager = get_observation_space_manager()
        manager.set_compression_mode(True)
        
        # 测试数据
        batch_size = 4
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        input_data = torch.randn(batch_size, 656, device=device)
        
        # 执行压缩
        compressed = manager._simple_compress(input_data.cpu().numpy())
        
        # 验证结果
        assert compressed.shape == (batch_size, 116), f"压缩结果形状错误: {compressed.shape}"
        assert isinstance(compressed, np.ndarray), "压缩结果应为NumPy数组"
        
    def test_simple_compress_edge_cases(self):
        """测试压缩功能边界情况"""
        manager = get_observation_space_manager()
        manager.set_compression_mode(True)
        
        # 测试单个样本
        single_sample = np.random.rand(1, 656).astype(np.float32)
        compressed = manager._simple_compress(single_sample)
        assert compressed.shape == (1, 116), "单样本压缩失败"
        
        # 测试大批次
        large_batch = np.random.rand(64, 656).astype(np.float32)
        compressed = manager._simple_compress(large_batch)
        assert compressed.shape == (64, 116), "大批次压缩失败"
        
        # 测试极值
        zeros = np.zeros((2, 656), dtype=np.float32)
        compressed_zeros = manager._simple_compress(zeros)
        assert compressed_zeros.shape == (2, 116), "零值压缩失败"
        
        ones = np.ones((2, 656), dtype=np.float32)
        compressed_ones = manager._simple_compress(ones)
        assert compressed_ones.shape == (2, 116), "全1压缩失败"
        
    def test_dimension_validation(self):
        """测试维度验证"""
        manager = get_observation_space_manager()
        
        # 测试错误的输入维度
        wrong_dim_data = np.random.rand(4, 512).astype(np.float32)
        
        with pytest.raises(ValueError, match="输入观察空间维度错误"):
            manager._simple_compress(wrong_dim_data)
            
        # 测试错误的输入形状
        wrong_shape_data = np.random.rand(656).astype(np.float32)  # 缺少批次维度
        
        with pytest.raises(ValueError, match="输入形状应为"):
            manager._simple_compress(wrong_shape_data)
            
    def test_compression_disabled_behavior(self):
        """测试禁用压缩时的行为"""
        manager = get_observation_space_manager()
        manager.set_compression_mode(False)
        
        # 压缩禁用时，_simple_compress应该返回原始数据
        input_data = np.random.rand(4, 656).astype(np.float32)
        result = manager._simple_compress(input_data)
        
        # 应该返回原始数据（可能是副本）
        assert result.shape == (4, 656), "禁用压缩时应返回原始维度"
        np.testing.assert_array_equal(result, input_data)
        
    def test_performance_metrics(self):
        """测试性能指标计算"""
        manager = get_observation_space_manager()
        
        # 测试压缩比
        expected_ratio = 656 / 116
        assert abs(manager.compression_ratio - expected_ratio) < 0.01, "压缩比计算错误"
        
        # 测试内存节省百分比
        expected_saved = (1 - 116/656) * 100
        assert abs(manager.memory_saved_percent - expected_saved) < 0.1, "内存节省百分比计算错误"
        
    def test_thread_safety(self):
        """测试线程安全性（基础测试）"""
        import threading
        import time
        
        results = []
        
        def worker():
            manager = get_observation_space_manager()
            results.append(id(manager))
            
        # 创建多个线程
        threads = []
        for _ in range(5):
            thread = threading.Thread(target=worker)
            threads.append(thread)
            thread.start()
            
        # 等待所有线程完成
        for thread in threads:
            thread.join()
            
        # 验证所有线程获得相同的实例
        assert len(set(results)) == 1, "线程安全性测试失败，获得了不同的实例"
        
    def test_memory_efficiency(self):
        """测试内存效率"""
        manager = get_observation_space_manager()
        manager.set_compression_mode(True)
        
        # 测试大批次数据的内存使用
        batch_size = 1000
        original_data = np.random.rand(batch_size, 656).astype(np.float32)
        compressed_data = manager._simple_compress(original_data)
        
        # 计算内存使用
        original_memory = original_data.nbytes
        compressed_memory = compressed_data.nbytes
        memory_ratio = compressed_memory / original_memory
        
        # 验证内存节省
        expected_ratio = 116 / 656
        assert abs(memory_ratio - expected_ratio) < 0.01, f"内存压缩比不符合预期: {memory_ratio}"
        
        print(f"内存效率测试: {original_memory/1024/1024:.2f}MB -> {compressed_memory/1024/1024:.2f}MB")
        print(f"压缩比: {memory_ratio:.3f} (预期: {expected_ratio:.3f})")


@pytest.mark.integration
class TestObservationSpaceManagerIntegration:
    """ObservationSpaceManager集成测试"""
    
    def test_integration_with_compact_encoder(self):
        """测试与CompactObservationEncoder集成"""
        try:
            from cardgame_ai.games.doudizhu.compact_observation import CompactObservationEncoder
            
            manager = get_observation_space_manager()
            encoder = CompactObservationEncoder()
            
            # 验证维度一致性
            assert manager.get_observation_dim() == encoder.get_observation_space_size(), \
                "管理器与编码器维度不一致"
                
        except ImportError:
            pytest.skip("CompactObservationEncoder不可用")
            
    def test_integration_with_model_config(self):
        """测试与模型配置集成"""
        try:
            from cardgame_ai.algorithms.configs import EfficientZeroConfig
            
            manager = get_observation_space_manager()
            config = EfficientZeroConfig.create_v2_config()
            
            # 更新配置
            config.observation_shape = tuple(manager.get_observation_shape())
            
            # 验证配置正确
            assert config.observation_shape == (116,), "模型配置观察空间不正确"
            
        except ImportError:
            pytest.skip("EfficientZeroConfig不可用")


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v", "--tb=short"])