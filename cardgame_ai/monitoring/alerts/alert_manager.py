"""
告警管理器模块
管理系统告警规则和通知
"""

from typing import Dict, Any, List, Optional, Callable
from datetime import datetime, timedelta
from enum import Enum
from dataclasses import dataclass
import threading
from ..core.base import MonitoringEvent, MonitoringLevel

class AlertSeverity(Enum):
    """告警严重程度"""
    INFO = "info"
    WARNING = "warning" 
    ERROR = "error"
    CRITICAL = "critical"

class AlertStatus(Enum):
    """告警状态"""
    ACTIVE = "active"
    RESOLVED = "resolved"
    SUPPRESSED = "suppressed"

@dataclass
class Alert:
    """告警对象"""
    id: str
    name: str
    description: str
    severity: AlertSeverity
    status: AlertStatus
    created_at: datetime
    resolved_at: Optional[datetime] = None
    source: str = ""
    metric_name: str = ""
    current_value: Any = None
    threshold: Any = None
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}

class AlertRule:
    """告警规则"""
    
    def __init__(self, rule_id: str, name: str, condition: Callable[[Any], bool],
                 severity: AlertSeverity = AlertSeverity.WARNING,
                 description: str = "", cooldown_minutes: int = 5):
        self.rule_id = rule_id
        self.name = name
        self.condition = condition
        self.severity = severity
        self.description = description
        self.cooldown = timedelta(minutes=cooldown_minutes)
        self.last_triggered = None
        self.is_enabled = True
        
    def should_trigger(self, value: Any) -> bool:
        """检查是否应该触发告警"""
        if not self.is_enabled:
            return False
            
        # 检查冷却时间
        if self.last_triggered and datetime.now() - self.last_triggered < self.cooldown:
            return False
            
        return self.condition(value)
        
    def trigger(self):
        """触发告警"""
        self.last_triggered = datetime.now()

class AlertManager:
    """告警管理器"""
    
    def __init__(self):
        self.rules: Dict[str, AlertRule] = {}
        self.active_alerts: Dict[str, Alert] = {}
        self.alert_history: List[Alert] = []
        self.notifiers: List[Callable[[Alert], None]] = []
        self._lock = threading.Lock()
        self._alert_counter = 0
        
    def add_rule(self, rule: AlertRule):
        """添加告警规则"""
        with self._lock:
            self.rules[rule.rule_id] = rule
            
    def remove_rule(self, rule_id: str):
        """移除告警规则"""
        with self._lock:
            self.rules.pop(rule_id, None)
            
    def add_notifier(self, notifier: Callable[[Alert], None]):
        """添加通知器"""
        self.notifiers.append(notifier)
        
    def remove_notifier(self, notifier: Callable[[Alert], None]):
        """移除通知器"""
        if notifier in self.notifiers:
            self.notifiers.remove(notifier)
            
    def check_metrics(self, metrics: Dict[str, Any]):
        """检查指标并触发告警"""
        with self._lock:
            for metric_name, value in metrics.items():
                self._check_metric_rules(metric_name, value)
                
    def _check_metric_rules(self, metric_name: str, value: Any):
        """检查单个指标的规则"""
        for rule in self.rules.values():
            if rule.should_trigger(value):
                self._create_alert(rule, metric_name, value)
                rule.trigger()
                
    def _create_alert(self, rule: AlertRule, metric_name: str, value: Any):
        """创建告警"""
        self._alert_counter += 1
        alert_id = f"alert_{self._alert_counter}_{rule.rule_id}"
        
        alert = Alert(
            id=alert_id,
            name=rule.name,
            description=rule.description,
            severity=rule.severity,
            status=AlertStatus.ACTIVE,
            created_at=datetime.now(),
            source="system",
            metric_name=metric_name,
            current_value=value,
            metadata={"rule_id": rule.rule_id}
        )
        
        self.active_alerts[alert_id] = alert
        self.alert_history.append(alert)
        
        # 保持历史长度
        if len(self.alert_history) > 1000:
            self.alert_history = self.alert_history[-1000:]
            
        # 发送通知
        for notifier in self.notifiers:
            try:
                notifier(alert)
            except Exception as e:
                print(f"Notifier error: {e}")
                
    def resolve_alert(self, alert_id: str, resolution_note: str = ""):
        """解决告警"""
        with self._lock:
            if alert_id in self.active_alerts:
                alert = self.active_alerts[alert_id]
                alert.status = AlertStatus.RESOLVED
                alert.resolved_at = datetime.now()
                alert.metadata['resolution_note'] = resolution_note
                
                del self.active_alerts[alert_id]
                
    def suppress_alert(self, alert_id: str, duration_minutes: int = 60):
        """抑制告警"""
        with self._lock:
            if alert_id in self.active_alerts:
                alert = self.active_alerts[alert_id]
                alert.status = AlertStatus.SUPPRESSED
                alert.metadata['suppressed_until'] = datetime.now() + timedelta(minutes=duration_minutes)
                
    def get_active_alerts(self, severity: Optional[AlertSeverity] = None) -> List[Alert]:
        """获取活跃告警"""
        with self._lock:
            alerts = list(self.active_alerts.values())
            if severity:
                alerts = [alert for alert in alerts if alert.severity == severity]
            return sorted(alerts, key=lambda x: x.created_at, reverse=True)
            
    def get_alert_history(self, hours: int = 24, severity: Optional[AlertSeverity] = None) -> List[Alert]:
        """获取告警历史"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        with self._lock:
            alerts = [alert for alert in self.alert_history if alert.created_at >= cutoff_time]
            if severity:
                alerts = [alert for alert in alerts if alert.severity == severity]
            return sorted(alerts, key=lambda x: x.created_at, reverse=True)
            
    def get_alert_statistics(self, hours: int = 24) -> Dict[str, Any]:
        """获取告警统计"""
        recent_alerts = self.get_alert_history(hours)
        
        stats = {
            'total_alerts': len(recent_alerts),
            'active_alerts': len(self.active_alerts),
            'by_severity': {},
            'by_status': {},
            'most_frequent_rules': {},
            'alert_rate_per_hour': len(recent_alerts) / hours if hours > 0 else 0
        }
        
        # 按严重程度统计
        for severity in AlertSeverity:
            count = len([alert for alert in recent_alerts if alert.severity == severity])
            stats['by_severity'][severity.value] = count
            
        # 按状态统计
        for status in AlertStatus:
            count = len([alert for alert in recent_alerts if alert.status == status])
            stats['by_status'][status.value] = count
            
        # 最频繁的规则
        rule_counts = {}
        for alert in recent_alerts:
            rule_id = alert.metadata.get('rule_id', 'unknown')
            rule_counts[rule_id] = rule_counts.get(rule_id, 0) + 1
            
        if rule_counts:
            stats['most_frequent_rules'] = dict(sorted(rule_counts.items(), 
                                                     key=lambda x: x[1], reverse=True)[:5])
            
        return stats
        
    def create_metric_threshold_rule(self, metric_name: str, threshold: float, 
                                   comparison: str = 'greater', severity: AlertSeverity = AlertSeverity.WARNING,
                                   description: str = "") -> AlertRule:
        """创建指标阈值规则"""
        rule_id = f"threshold_{metric_name}_{comparison}_{threshold}"
        
        if comparison == 'greater':
            condition = lambda value: isinstance(value, (int, float)) and value > threshold
        elif comparison == 'less':
            condition = lambda value: isinstance(value, (int, float)) and value < threshold
        elif comparison == 'equal':
            condition = lambda value: value == threshold
        else:
            raise ValueError(f"Unknown comparison: {comparison}")
            
        name = f"{metric_name} {comparison} {threshold}"
        if not description:
            description = f"Alert when {metric_name} is {comparison} than {threshold}"
            
        return AlertRule(
            rule_id=rule_id,
            name=name,
            condition=condition,
            severity=severity,
            description=description
        )
        
    def create_change_rate_rule(self, metric_name: str, max_change_percent: float,
                               severity: AlertSeverity = AlertSeverity.WARNING) -> AlertRule:
        """创建变化率规则"""
        rule_id = f"change_rate_{metric_name}_{max_change_percent}"
        
        # 这需要访问历史数据，简化实现
        def condition(value):
            # 实际实现需要比较当前值与历史值
            return False  # 简化实现
            
        return AlertRule(
            rule_id=rule_id,
            name=f"{metric_name} change rate > {max_change_percent}%",
            condition=condition,
            severity=severity,
            description=f"Alert when {metric_name} changes more than {max_change_percent}%"
        )

# 预定义的常用告警规则
def create_system_alert_rules() -> List[AlertRule]:
    """创建系统告警规则"""
    rules = []
    
    # CPU使用率告警
    rules.append(AlertRule(
        "high_cpu_usage",
        "High CPU Usage",
        lambda value: isinstance(value, (int, float)) and value > 90,
        AlertSeverity.WARNING,
        "CPU usage is above 90%"
    ))
    
    rules.append(AlertRule(
        "critical_cpu_usage", 
        "Critical CPU Usage",
        lambda value: isinstance(value, (int, float)) and value > 95,
        AlertSeverity.CRITICAL,
        "CPU usage is above 95%"
    ))
    
    # 内存使用率告警
    rules.append(AlertRule(
        "high_memory_usage",
        "High Memory Usage", 
        lambda value: isinstance(value, (int, float)) and value > 85,
        AlertSeverity.WARNING,
        "Memory usage is above 85%"
    ))
    
    rules.append(AlertRule(
        "critical_memory_usage",
        "Critical Memory Usage",
        lambda value: isinstance(value, (int, float)) and value > 95,
        AlertSeverity.CRITICAL,
        "Memory usage is above 95%"
    ))
    
    # GPU温度告警
    rules.append(AlertRule(
        "high_gpu_temperature",
        "High GPU Temperature",
        lambda value: isinstance(value, (int, float)) and value > 85,
        AlertSeverity.WARNING,
        "GPU temperature is above 85°C"
    ))
    
    return rules

# 全局告警管理器
_global_alert_manager = None

def get_alert_manager() -> AlertManager:
    """获取全局告警管理器"""
    global _global_alert_manager
    if _global_alert_manager is None:
        _global_alert_manager = AlertManager()
        
        # 添加默认规则
        for rule in create_system_alert_rules():
            _global_alert_manager.add_rule(rule)
            
    return _global_alert_manager
