"""
MCTS配置模块
统一管理所有MCTS相关配置
"""

from dataclasses import dataclass
from typing import Dict, Any, Optional, List
from pathlib import Path

from .base import BaseConfig


@dataclass
class MCTSConfig(BaseConfig):
    """MCTS搜索配置"""
    
    # 基础搜索参数
    num_simulations: int = 450  # Story 1.5统一优化：450次模拟
    max_depth: int = 100
    c_puct: float = 1.25
    discount: float = 0.997
    
    # 探索参数
    dirichlet_alpha: float = 0.25
    exploration_fraction: float = 0.25
    pb_c_base: int = 19652
    pb_c_init: float = 1.25
    root_exploration_noise: bool = True
    
    # 并行搜索
    enable_parallel: bool = True
    num_threads: int = 4
    batch_size: int = 16
    
    # PPB-MCTS特定配置
    num_workers: int = 8
    pipeline_depth: int = 4
    virtual_loss: float = 1.0
    virtual_loss_decay: float = 0.99
    use_priority_queue: bool = True
    dynamic_batch_size: bool = True
    min_batch_size: int = 8
    max_batch_size: int = 64
    
    # 动态预算分配
    dynamic_budget_enabled: bool = True
    min_simulations: int = 50
    max_simulations: int = 800
    
    # 游戏阶段倍数
    phase_multipliers: Dict[str, float] = None
    special_multipliers: Dict[str, float] = None
    
    # 优化配置
    enable_ucb_cache: bool = True
    enable_node_pool: bool = True
    node_pool_size: int = 10000
    
    # 超时配置
    max_time_ms: Optional[int] = None
    enable_timeout: bool = False
    
    def __post_init__(self):
        super().__post_init__()
        
        # 设置默认的游戏阶段倍数
        if self.phase_multipliers is None:
            self.phase_multipliers = {
                'bidding': 0.3,
                'early_game': 0.6,
                'mid_game': 1.0,
                'late_game': 1.5,
                'critical': 2.0
            }
        
        # 设置默认的特殊情况倍数
        if self.special_multipliers is None:
            self.special_multipliers = {
                'bomb_decision': 2.0,
                'winning_move': 2.5,
                'cooperation': 1.8
            }


@dataclass
class DynamicBudgetConfig(BaseConfig):
    """动态预算分配配置"""
    
    enabled: bool = True
    min_simulations: int = 50
    max_simulations: int = 800
    
    # 基础规则权重
    base_weights: Dict[str, float] = None
    
    # 阶段识别阈值
    phase_thresholds: Dict[str, Dict[str, Any]] = None
    
    # 自适应参数
    adaptation_rate: float = 0.1
    history_window: int = 100
    
    def __post_init__(self):
        super().__post_init__()
        
        if self.base_weights is None:
            self.base_weights = {
                'remaining_cards': 0.3,
                'hand_strength': 0.2,
                'position_advantage': 0.15,
                'cooperation_potential': 0.2,
                'bomb_count': 0.15
            }
        
        if self.phase_thresholds is None:
            self.phase_thresholds = {
                'bidding': {'max_cards': 17, 'min_cards': 17},
                'early_game': {'max_cards': 15, 'min_cards': 10},
                'mid_game': {'max_cards': 9, 'min_cards': 4},
                'late_game': {'max_cards': 3, 'min_cards': 1},
                'critical': {'game_ending_probability': 0.7}
            }


@dataclass
class PPBMCTSConfig(BaseConfig):
    """PPB-MCTS流水线配置"""
    
    # 流水线设置
    num_workers: int = 8
    pipeline_depth: int = 4
    
    # 批量处理
    batch_size: int = 32
    dynamic_batch_size: bool = True
    min_batch_size: int = 8
    max_batch_size: int = 64
    
    # 虚拟损失
    virtual_loss: float = 1.0
    virtual_loss_decay: float = 0.99
    
    # 优化设置
    use_priority_queue: bool = True
    leaf_cache_size: int = 10000
    enable_profiling: bool = False
    profile_interval: int = 100
    
    # 队列配置
    queue_maxsize: int = 1000
    timeout_seconds: float = 0.01


# 配置工厂函数
def create_mcts_config(config_type: str = "standard", **kwargs) -> MCTSConfig:
    """
    创建MCTS配置
    
    Args:
        config_type: 配置类型 (standard, aggressive, conservative)
        **kwargs: 额外参数
    
    Returns:
        MCTS配置实例
    """
    base_config = {}
    
    if config_type == "aggressive":
        base_config = {
            'num_simulations': 800,
            'c_puct': 1.5,
            'max_simulations': 1200,
            'dynamic_budget_enabled': True
        }
    elif config_type == "conservative":
        base_config = {
            'num_simulations': 300,
            'c_puct': 1.0,
            'max_simulations': 500,
            'dynamic_budget_enabled': False
        }
    else:  # standard
        base_config = {
            'num_simulations': 450,
            'c_puct': 1.25,
            'max_simulations': 800,
            'dynamic_budget_enabled': True
        }
    
    # 合并用户提供的参数
    base_config.update(kwargs)
    
    return MCTSConfig(**base_config)


def create_ppb_mcts_config(performance_level: str = "balanced", **kwargs) -> PPBMCTSConfig:
    """
    创建PPB-MCTS配置
    
    Args:
        performance_level: 性能级别 (minimal, balanced, aggressive)
        **kwargs: 额外参数
    
    Returns:
        PPB-MCTS配置实例
    """
    base_config = {}
    
    if performance_level == "minimal":
        base_config = {
            'num_workers': 4,
            'pipeline_depth': 2,
            'batch_size': 16,
            'max_batch_size': 32
        }
    elif performance_level == "aggressive":
        base_config = {
            'num_workers': 12,
            'pipeline_depth': 6,
            'batch_size': 64,
            'max_batch_size': 128
        }
    else:  # balanced
        base_config = {
            'num_workers': 8,
            'pipeline_depth': 4,
            'batch_size': 32,
            'max_batch_size': 64
        }
    
    base_config.update(kwargs)
    return PPBMCTSConfig(**base_config)


# 导出配置类
__all__ = [
    'MCTSConfig',
    'DynamicBudgetConfig', 
    'PPBMCTSConfig',
    'create_mcts_config',
    'create_ppb_mcts_config'
]