"""
状态适配器 - 优化状态对象类型转换性能

解决DouDizhuState对象频繁转换为numpy数组导致的性能损失。
通过缓存和智能转换策略，显著提升训练效率。

设计原则：
1. 避免重复转换：缓存转换结果
2. 延迟转换：仅在需要时进行转换
3. 批量转换：批量处理多个状态
4. 内存优化：自动清理过期缓存

作者：AI共生者
创建时间：2025-07-04
"""

import time
import weakref
from typing import Dict, Any, Optional, Union, List, Tuple
from collections import OrderedDict
import numpy as np
import torch

from cardgame_ai.utils.logging import get_logger
from cardgame_ai.games.doudizhu.states.state import DouDizhuState

logger = get_logger(__name__)


class StateAdapterCache:
    """状态转换缓存管理器"""
    
    def __init__(self, max_size: int = 1000, ttl_seconds: float = 30.0):
        """
        初始化缓存管理器
        
        Args:
            max_size: 最大缓存条目数
            ttl_seconds: 缓存过期时间（秒）
        """
        self.max_size = max_size
        self.ttl_seconds = ttl_seconds
        self.cache: OrderedDict = OrderedDict()
        self.access_times: Dict[str, float] = {}
        
        # 统计信息
        self.hit_count = 0
        self.miss_count = 0
        self.eviction_count = 0
        
    def _generate_key(self, state: DouDizhuState, player_id: str) -> str:
        """生成缓存键"""
        # 使用状态的关键特征生成唯一键
        key_components = [
            f"phase_{state.phase.value}",
            f"player_{player_id}",
            f"current_{state.current_player}",
            f"landlord_{state.landlord}",
            f"hands_{len(state.hands.get(player_id, []))}",
            f"last_{state.last_player}_{state.last_move}",
            f"deck_{len(state.deck.cards)}"
        ]
        return "|".join(key_components)
    
    def get(self, state: DouDizhuState, player_id: str) -> Optional[np.ndarray]:
        """获取缓存的观察"""
        key = self._generate_key(state, player_id)
        current_time = time.time()
        
        if key in self.cache:
            # 检查是否过期
            if current_time - self.access_times[key] <= self.ttl_seconds:
                self.access_times[key] = current_time
                # 移动到末尾（LRU）
                self.cache.move_to_end(key)
                self.hit_count += 1
                return self.cache[key]
            else:
                # 过期，删除
                del self.cache[key]
                del self.access_times[key]
                self.eviction_count += 1
        
        self.miss_count += 1
        return None
    
    def put(self, state: DouDizhuState, player_id: str, observation: np.ndarray):
        """存储观察到缓存"""
        key = self._generate_key(state, player_id)
        current_time = time.time()
        
        # 检查是否需要清理
        if len(self.cache) >= self.max_size:
            self._evict_oldest()
        
        self.cache[key] = observation.copy()
        self.access_times[key] = current_time
    
    def _evict_oldest(self):
        """清理最旧的缓存条目"""
        if self.cache:
            oldest_key = next(iter(self.cache))
            del self.cache[oldest_key]
            del self.access_times[oldest_key]
            self.eviction_count += 1
    
    def clear(self):
        """清空缓存"""
        self.cache.clear()
        self.access_times.clear()
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        total_requests = self.hit_count + self.miss_count
        hit_rate = self.hit_count / total_requests if total_requests > 0 else 0.0
        
        return {
            'cache_size': len(self.cache),
            'max_size': self.max_size,
            'hit_count': self.hit_count,
            'miss_count': self.miss_count,
            'hit_rate': hit_rate,
            'eviction_count': self.eviction_count,
            'memory_usage_mb': self._estimate_memory_usage()
        }
    
    def _estimate_memory_usage(self) -> float:
        """估算内存使用量（MB）"""
        if not self.cache:
            return 0.0
        
        # 估算：每个numpy数组约656维 * 4字节（float32）+ 开销
        array_size_bytes = 656 * 4 + 100  # 100字节开销
        total_bytes = len(self.cache) * array_size_bytes
        return total_bytes / (1024 * 1024)


class OptimizedStateAdapter:
    """优化的状态适配器
    
    功能：
    1. 高效的状态类型转换
    2. 智能缓存管理
    3. 批量处理优化
    4. 性能监控
    """
    
    def __init__(self, cache_size: int = 1000, cache_ttl: float = 30.0):
        """
        初始化状态适配器
        
        Args:
            cache_size: 缓存大小
            cache_ttl: 缓存过期时间（秒）
        """
        self.cache = StateAdapterCache(cache_size, cache_ttl)
        
        # 性能统计
        self.conversion_count = 0
        self.total_conversion_time = 0.0
        self.batch_conversion_count = 0
        
        # 环境引用缓存
        self._env_cache = weakref.WeakKeyDictionary()
        
        logger.info(f"状态适配器初始化完成，缓存大小: {cache_size}, TTL: {cache_ttl}s")
    
    def ensure_observation(self, state_or_obs: Union[DouDizhuState, np.ndarray], 
                          env: Any = None, player_id: Optional[str] = None) -> np.ndarray:
        """
        确保输入是观察数组，智能处理状态转换
        
        Args:
            state_or_obs: 状态对象或观察数组
            env: 环境实例
            player_id: 玩家ID
            
        Returns:
            观察数组
        """
        # 如果已经是numpy数组，直接返回
        if isinstance(state_or_obs, np.ndarray):
            return state_or_obs
        
        # 如果是torch张量，转换为numpy
        if torch.is_tensor(state_or_obs):
            return state_or_obs.detach().cpu().numpy()
        
        # 如果是DouDizhuState对象，进行转换
        if isinstance(state_or_obs, DouDizhuState):
            return self._convert_state_to_observation(state_or_obs, env, player_id)
        
        # 如果是字典类型，尝试提取观察
        if isinstance(state_or_obs, dict):
            if 'observation' in state_or_obs:
                return self.ensure_observation(state_or_obs['observation'], env, player_id)
            elif 'obs' in state_or_obs:
                return self.ensure_observation(state_or_obs['obs'], env, player_id)
        
        # 未知类型，尝试转换为numpy数组
        try:
            return np.array(state_or_obs, dtype=np.float32)
        except Exception as e:
            logger.error(f"无法转换状态类型 {type(state_or_obs)}: {e}")
            raise TypeError(f"不支持的状态类型: {type(state_or_obs)}")
    
    def _convert_state_to_observation(self, state: DouDizhuState, env: Any, 
                                    player_id: Optional[str]) -> np.ndarray:
        """
        将状态对象转换为观察数组（带缓存）
        
        Args:
            state: 状态对象
            env: 环境实例
            player_id: 玩家ID
            
        Returns:
            观察数组
        """
        start_time = time.time()
        
        # 确定玩家ID
        if player_id is None:
            if hasattr(env, 'get_current_player'):
                player_id = env.get_current_player()
            elif hasattr(state, 'current_player'):
                player_id = state.current_player
            else:
                player_id = 'player_0'  # 默认值
        
        # 尝试从缓存获取
        cached_obs = self.cache.get(state, player_id)
        if cached_obs is not None:
            return cached_obs
        
        # 缓存未命中，进行转换
        if env is None:
            raise ValueError("环境实例不能为None进行状态转换")
        
        # 调用环境的get_observation方法
        observation = env.get_observation(state, player_id)
        
        # 确保是正确的numpy数组格式
        if not isinstance(observation, np.ndarray):
            observation = np.array(observation, dtype=np.float32)
        elif observation.dtype != np.float32:
            observation = observation.astype(np.float32)
        
        # 存储到缓存
        self.cache.put(state, player_id, observation)
        
        # 更新统计信息
        conversion_time = time.time() - start_time
        self.conversion_count += 1
        self.total_conversion_time += conversion_time
        
        return observation
    
    def batch_ensure_observations(self, states_or_obs: List[Union[DouDizhuState, np.ndarray]], 
                                 env: Any = None, player_ids: Optional[List[str]] = None) -> List[np.ndarray]:
        """
        批量确保观察数组，优化批处理性能
        
        Args:
            states_or_obs: 状态或观察列表
            env: 环境实例
            player_ids: 玩家ID列表
            
        Returns:
            观察数组列表
        """
        if not states_or_obs:
            return []
        
        start_time = time.time()
        
        # 确保player_ids长度匹配
        if player_ids is None:
            player_ids = [None] * len(states_or_obs)
        elif len(player_ids) != len(states_or_obs):
            # 扩展或截断player_ids
            if len(player_ids) < len(states_or_obs):
                player_ids.extend([None] * (len(states_or_obs) - len(player_ids)))
            else:
                player_ids = player_ids[:len(states_or_obs)]
        
        observations = []
        for i, state_or_obs in enumerate(states_or_obs):
            obs = self.ensure_observation(state_or_obs, env, player_ids[i])
            observations.append(obs)
        
        # 更新批处理统计
        batch_time = time.time() - start_time
        self.batch_conversion_count += 1
        
        logger.debug(f"批处理转换 {len(states_or_obs)} 个状态，耗时 {batch_time:.4f}s")
        
        return observations
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        avg_conversion_time = (self.total_conversion_time / self.conversion_count 
                             if self.conversion_count > 0 else 0.0)
        
        cache_stats = self.cache.get_stats()
        
        return {
            'conversion_count': self.conversion_count,
            'total_conversion_time': self.total_conversion_time,
            'avg_conversion_time': avg_conversion_time,
            'batch_conversion_count': self.batch_conversion_count,
            'cache_stats': cache_stats,
            'performance_gain': {
                'cache_hit_rate': cache_stats['hit_rate'],
                'estimated_time_saved': cache_stats['hit_count'] * avg_conversion_time,
                'memory_overhead_mb': cache_stats['memory_usage_mb']
            }
        }
    
    def optimize_cache(self):
        """优化缓存性能"""
        # 清理过期条目
        self.cache._evict_oldest()
        
        # 如果命中率太低，增加缓存大小
        stats = self.cache.get_stats()
        if stats['hit_rate'] < 0.3 and stats['cache_size'] < 2000:
            self.cache.max_size = min(2000, self.cache.max_size * 2)
            logger.info(f"缓存命中率较低({stats['hit_rate']:.2f})，增加缓存大小至 {self.cache.max_size}")
    
    def clear_cache(self):
        """清空缓存"""
        self.cache.clear()
        logger.info("状态适配器缓存已清空")
    
    def __del__(self):
        """析构函数，打印性能统计"""
        if hasattr(self, 'conversion_count') and self.conversion_count > 0:
            stats = self.get_performance_stats()
            logger.info(f"状态适配器性能统计: 转换次数={stats['conversion_count']}, "
                       f"平均耗时={stats['avg_conversion_time']:.4f}s, "
                       f"缓存命中率={stats['cache_stats']['hit_rate']:.2f}")


# 全局状态适配器实例
_global_state_adapter = None


def get_global_state_adapter() -> OptimizedStateAdapter:
    """获取全局状态适配器实例"""
    global _global_state_adapter
    if _global_state_adapter is None:
        _global_state_adapter = OptimizedStateAdapter()
    return _global_state_adapter


def ensure_observation(state_or_obs: Union[DouDizhuState, np.ndarray], 
                      env: Any = None, player_id: Optional[str] = None) -> np.ndarray:
    """
    便捷函数：确保输入是观察数组
    
    Args:
        state_or_obs: 状态对象或观察数组
        env: 环境实例
        player_id: 玩家ID
        
    Returns:
        观察数组
    """
    adapter = get_global_state_adapter()
    return adapter.ensure_observation(state_or_obs, env, player_id)


def batch_ensure_observations(states_or_obs: List[Union[DouDizhuState, np.ndarray]], 
                             env: Any = None, player_ids: Optional[List[str]] = None) -> List[np.ndarray]:
    """
    便捷函数：批量确保观察数组
    
    Args:
        states_or_obs: 状态或观察列表
        env: 环境实例
        player_ids: 玩家ID列表
        
    Returns:
        观察数组列表
    """
    adapter = get_global_state_adapter()
    return adapter.batch_ensure_observations(states_or_obs, env, player_ids)