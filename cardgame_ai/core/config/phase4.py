"""
Phase 4功能配置模块
管理所有Phase 4高级功能的配置
"""

from dataclasses import dataclass, field
from typing import Optional, Dict, Any, List
from enum import Enum
from .base import BaseConfig, ConfigRegistry

class HumanlikeDifficulty(str, Enum):
    """拟人化难度等级"""
    NOVICE = "novice"      # 新手
    AMATEUR = "amateur"    # 业余
    ADVANCED = "advanced"  # 高级
    EXPERT = "expert"      # 专家
    MASTER = "master"      # 大师

@dataclass
class IntelligentBiddingConfig(BaseConfig):
    """智能叫地主配置"""
    enable: bool = True
    
    # 决策因子权重
    hand_strength_weight: float = 0.4
    position_weight: float = 0.2
    opponent_pattern_weight: float = 0.2
    expected_value_weight: float = 0.2
    
    # 阈值设置
    min_bid_threshold: float = 0.6
    aggressive_threshold: float = 0.8
    
    # 位置策略
    first_position_bonus: float = 0.1
    last_position_penalty: float = -0.05
    
    # 学习设置
    learn_from_outcomes: bool = True
    learning_rate: float = 0.01

@dataclass
class HumanlikeMemoryConfig(BaseConfig):
    """拟人化记忆配置"""
    enable: bool = True
    difficulty: HumanlikeDifficulty = HumanlikeDifficulty.AMATEUR
    
    # 记忆容量（根据难度）
    memory_capacity: Dict[str, int] = field(default_factory=lambda: {
        "novice": 5,
        "amateur": 10,
        "advanced": 20,
        "expert": 50,
        "master": 100
    })
    
    # 遗忘率（根据难度）
    forgetting_rate: Dict[str, float] = field(default_factory=lambda: {
        "novice": 0.3,
        "amateur": 0.2,
        "advanced": 0.1,
        "expert": 0.05,
        "master": 0.01
    })
    
    # 记忆特性
    prioritize_important_cards: bool = True
    memory_decay_function: str = "exponential"  # exponential, linear
    interference_factor: float = 0.1

@dataclass
class GameTheoryBombConfig(BaseConfig):
    """博弈论炸弹管理配置"""
    enable: bool = True
    
    # 纳什均衡参数
    nash_iterations: int = 100
    convergence_threshold: float = 0.01
    
    # 期望值计算
    bomb_base_value: float = 2.0
    timing_multiplier: float = 1.5
    
    # 风险评估
    risk_aversion: float = 0.7
    opponent_bomb_probability: float = 0.3
    
    # 决策阈值
    min_expected_value: float = 1.5
    critical_moment_threshold: float = 0.8

@dataclass
class FarmerCooperationConfig(BaseConfig):
    """农民协作配置"""
    enable: bool = True
    
    # 信号系统
    use_implicit_signals: bool = True
    signal_strength: float = 0.8
    
    # 协作策略
    cooperation_threshold: float = 0.7
    trust_factor: float = 0.9
    
    # 信息共享
    share_card_count: bool = True
    share_bomb_info: bool = True
    
    # 自适应协作
    adaptive_strategy: bool = True
    strategy_update_interval: int = 10

@dataclass
class Phase4Config(BaseConfig):
    """Phase 4统一配置"""
    enable: bool = True
    preset: str = "balanced"  # minimal, balanced, full, expert
    
    # 功能模块
    intelligent_bidding: IntelligentBiddingConfig = field(default_factory=IntelligentBiddingConfig)
    humanlike_memory: HumanlikeMemoryConfig = field(default_factory=HumanlikeMemoryConfig)
    game_theory_bomb: GameTheoryBombConfig = field(default_factory=GameTheoryBombConfig)
    farmer_cooperation: FarmerCooperationConfig = field(default_factory=FarmerCooperationConfig)
    
    # 额外功能开关
    features: Dict[str, bool] = field(default_factory=lambda: {
        "intelligent_bidding": True,
        "humanlike_memory": True,
        "game_theory_bomb": True,
        "farmer_cooperation": True,
        "spring_rules": True,
        "statistical_analysis": True,
        "decision_visualization": False,
        "observation_compression": True,
        "zero_copy_batch": True
    })
    
    # 性能优化
    optimization_level: int = 2  # 0-3, higher is more aggressive
    
    def __post_init__(self):
        """初始化后处理"""
        super().__post_init__()
        
        # 根据预设配置功能
        preset_configs = {
            "minimal": {
                "intelligent_bidding": True,
                "spring_rules": True,
                "humanlike_memory": False,
                "game_theory_bomb": False,
                "farmer_cooperation": False,
                "statistical_analysis": False,
                "decision_visualization": False,
                "observation_compression": True,
                "zero_copy_batch": True
            },
            "balanced": {
                "intelligent_bidding": True,
                "humanlike_memory": True,
                "game_theory_bomb": True,
                "spring_rules": True,
                "farmer_cooperation": False,
                "statistical_analysis": False,
                "decision_visualization": False,
                "observation_compression": True,
                "zero_copy_batch": True
            },
            "full": {
                "intelligent_bidding": True,
                "humanlike_memory": True,
                "game_theory_bomb": True,
                "farmer_cooperation": True,
                "spring_rules": True,
                "statistical_analysis": True,
                "decision_visualization": False,
                "observation_compression": True,
                "zero_copy_batch": True
            },
            "expert": {
                "intelligent_bidding": True,
                "humanlike_memory": True,
                "game_theory_bomb": True,
                "farmer_cooperation": True,
                "spring_rules": True,
                "statistical_analysis": True,
                "decision_visualization": True,
                "observation_compression": True,
                "zero_copy_batch": True
            }
        }
        
        if self.preset in preset_configs:
            self.features.update(preset_configs[self.preset])
            
    def validate(self) -> bool:
        """验证配置有效性"""
        # 验证子配置
        if self.features.get("intelligent_bidding", False):
            self.intelligent_bidding.validate()
        if self.features.get("humanlike_memory", False):
            self.humanlike_memory.validate()
        if self.features.get("game_theory_bomb", False):
            self.game_theory_bomb.validate()
        if self.features.get("farmer_cooperation", False):
            self.farmer_cooperation.validate()
            
        return True
        
    def get_enabled_features(self) -> List[str]:
        """获取启用的功能列表"""
        return [name for name, enabled in self.features.items() if enabled]

# 注册配置类
ConfigRegistry.register(IntelligentBiddingConfig)
ConfigRegistry.register(HumanlikeMemoryConfig)
ConfigRegistry.register(GameTheoryBombConfig)
ConfigRegistry.register(FarmerCooperationConfig)
ConfigRegistry.register(Phase4Config)
