#!/usr/bin/env python3
"""
实时训练监控器

提供训练过程中的实时监控、预警和异常检测功能，包括：
- GPU利用率和温度监控
- 内存使用和泄漏检测
- 训练速度监控
- 损失曲线和收敛检测
- 异常预警机制

作者：AI系统
创建时间：2025-07-05
"""

import os
import sys
import time
import json
import threading
import psutil
import numpy as np
import torch
import gc
from typing import Dict, List, Any, Optional, Callable, Tuple
from datetime import datetime
from collections import deque, defaultdict
from pathlib import Path
import warnings

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from cardgame_ai.utils.logging import get_logger
from cardgame_ai.core.monitoring.gpu_memory_manager import GPUMemoryManager
from cardgame_ai.core.interrupt_manager import InterruptManager
from cardgame_ai.core.advanced_gpu_optimizer import AdvancedGPUOptimizer, GPUOptimizationConfig

# 尝试导入nvidia-ml-py
try:
    import pynvml as nvml
    NVML_AVAILABLE = True
    nvml.nvmlInit()
except (ImportError, Exception) as e:
    NVML_AVAILABLE = False
    warnings.warn(f"GPU监控功能不可用: {e}")

logger = get_logger(__name__)


class MetricTracker:
    """指标追踪器"""
    
    def __init__(self, window_size: int = 100, name: str = "metric"):
        self.name = name
        self.window_size = window_size
        self.values = deque(maxlen=window_size)
        self.timestamps = deque(maxlen=window_size)
        
    def add(self, value: float, timestamp: Optional[float] = None):
        """添加数据点"""
        if timestamp is None:
            timestamp = time.time()
        self.values.append(value)
        self.timestamps.append(timestamp)
        
    def get_latest(self) -> Optional[float]:
        """获取最新值"""
        return self.values[-1] if self.values else None
    
    def get_average(self, last_n: Optional[int] = None) -> float:
        """获取平均值"""
        if not self.values:
            return 0.0
        if last_n is None:
            return np.mean(self.values)
        return np.mean(list(self.values)[-last_n:])
    
    def get_trend(self) -> str:
        """获取趋势（上升/下降/稳定）"""
        if len(self.values) < 3:
            return "unknown"
        
        recent = list(self.values)[-10:]
        if len(recent) < 3:
            return "stable"
        
        # 计算斜率
        x = np.arange(len(recent))
        slope = np.polyfit(x, recent, 1)[0]
        
        # 相对于均值的变化率
        mean_val = np.mean(recent)
        if mean_val != 0:
            relative_slope = abs(slope) / mean_val
            
            if relative_slope < 0.01:
                return "stable"
            elif slope > 0:
                return "increasing"
            else:
                return "decreasing"
        
        return "stable"
    
    def detect_anomaly(self, threshold: float = 3.0) -> bool:
        """检测异常值（基于Z分数）"""
        if len(self.values) < 10:
            return False
        
        recent = list(self.values)[-20:]
        mean = np.mean(recent)
        std = np.std(recent)
        
        if std == 0:
            return False
        
        latest = self.values[-1]
        z_score = abs(latest - mean) / std
        
        return z_score > threshold


class OptimizationHistory:
    """优化历史记录器"""
    
    def __init__(self, max_history: int = 1000):
        self.max_history = max_history
        self.history = deque(maxlen=max_history)
        self.parameter_changes = deque(maxlen=100)
        self.performance_trends = deque(maxlen=500)
        
    def record_optimization(self, 
                          optimization_type: str,
                          old_value: Any,
                          new_value: Any,
                          reason: str,
                          expected_improvement: str):
        """记录优化操作"""
        record = {
            'timestamp': time.time(),
            'type': optimization_type,
            'old_value': old_value,
            'new_value': new_value,
            'reason': reason,
            'expected_improvement': expected_improvement,
            'applied': True
        }
        self.parameter_changes.append(record)
        
    def record_performance_trend(self,
                               metric_name: str,
                               current_value: float,
                               trend: str,
                               confidence: float):
        """记录性能趋势"""
        trend_record = {
            'timestamp': time.time(),
            'metric': metric_name,
            'value': current_value,
            'trend': trend,
            'confidence': confidence
        }
        self.performance_trends.append(trend_record)
        
    def get_optimization_summary(self) -> Dict[str, Any]:
        """获取优化总结"""
        if not self.parameter_changes:
            return {'total_optimizations': 0}
            
        optimization_types = defaultdict(int)
        successful_optimizations = 0
        
        for change in self.parameter_changes:
            optimization_types[change['type']] += 1
            if change.get('applied', False):
                successful_optimizations += 1
                
        return {
            'total_optimizations': len(self.parameter_changes),
            'successful_optimizations': successful_optimizations,
            'optimization_types': dict(optimization_types),
            'recent_trends': list(self.performance_trends)[-10:]
        }


class PerformanceAutoTuner:
    """性能自动调优器"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or {}
        self.logger = get_logger(__name__)
        
        # WSL环境检测
        is_wsl = self._detect_wsl_environment()
        
        # 调优阈值 - WSL环境适配
        if is_wsl:
            default_target = 40.0  # WSL环境目标GPU利用率
            default_low = 25.0     # WSL环境低阈值
            default_critical = 15.0 # WSL环境临界低阈值
            self.logger.info("🔧 WSL环境已检测到，GPU利用率目标调整为40%（适应WSL环境下的GPU读数特性）")
        else:
            default_target = 75.0  # 非WSL环境目标
            default_low = 50.0     # 非WSL环境低阈值
            default_critical = 30.0 # 非WSL环境临界阈值
            
        self.thresholds = {
            'gpu_utilization_target': self.config.get('gpu_utilization_target', default_target),
            'gpu_utilization_low': self.config.get('gpu_utilization_low', default_low),
            'gpu_utilization_critical_low': self.config.get('gpu_utilization_critical_low', default_critical),
            'batch_size_adjustment_factor': self.config.get('batch_size_adjustment_factor', 1.2),
            'learning_rate_adjustment_factor': self.config.get('learning_rate_adjustment_factor', 1.1),
            'memory_pressure_threshold': self.config.get('memory_pressure_threshold', 85.0)
        }
        
        # 调优状态
        self.tuning_enabled = True
        self.last_tuning_time = 0
        self.tuning_cooldown = self.config.get('tuning_cooldown', 60.0)  # 60秒冷却
        
        # 调优历史
        self.optimization_history = OptimizationHistory()
        
        # 当前参数
        self.current_batch_size = self.config.get('initial_batch_size', 32)
        self.current_learning_rate = self.config.get('initial_learning_rate', 0.001)
        
        self.logger.info("性能自动调优器已初始化")
    
    def _detect_wsl_environment(self) -> bool:
        """检测是否为WSL环境"""
        try:
            # 检查 /proc/version 文件中是否包含Microsoft
            with open('/proc/version', 'r') as f:
                version_info = f.read().lower()
                return 'microsoft' in version_info or 'wsl' in version_info
        except (FileNotFoundError, PermissionError):
            # 如果无法读取文件，使用环境变量检测
            import os
            wsl_env = os.environ.get('WSL_DISTRO_NAME')
            return wsl_env is not None
        
    def analyze_performance_bottlenecks(self, metrics: Dict[str, Any]) -> List[Dict[str, Any]]:
        """分析性能瓶颈"""
        bottlenecks = []
        
        # GPU利用率瓶颈分析
        gpu_utilization = metrics.get('gpu_utilization', {}).get('value', 0)
        if gpu_utilization < self.thresholds['gpu_utilization_critical_low']:
            bottlenecks.append({
                'type': 'gpu_utilization_critical',
                'severity': 'high',
                'metric': 'gpu_utilization',
                'current_value': gpu_utilization,
                'target_value': self.thresholds['gpu_utilization_target'],
                'description': f'GPU利用率严重偏低 ({gpu_utilization:.1f}% < {self.thresholds["gpu_utilization_critical_low"]}%)',
                'suggested_actions': ['increase_batch_size', 'optimize_data_loading', 'check_cpu_bottleneck']
            })
        elif gpu_utilization < self.thresholds['gpu_utilization_low']:
            bottlenecks.append({
                'type': 'gpu_utilization_low',
                'severity': 'medium',
                'metric': 'gpu_utilization',
                'current_value': gpu_utilization,
                'target_value': self.thresholds['gpu_utilization_target'],
                'description': f'GPU利用率偏低 ({gpu_utilization:.1f}% < {self.thresholds["gpu_utilization_low"]}%)',
                'suggested_actions': ['increase_batch_size', 'optimize_model_complexity']
            })
            
        # 内存利用率分析
        gpu_memory = metrics.get('gpu_memory', {}).get('value', 0)
        if gpu_memory > self.thresholds['memory_pressure_threshold']:
            bottlenecks.append({
                'type': 'memory_pressure_high',
                'severity': 'high',
                'metric': 'gpu_memory',
                'current_value': gpu_memory,
                'target_value': self.thresholds['memory_pressure_threshold'] - 10,
                'description': f'GPU内存压力过大 ({gpu_memory:.1f}% > {self.thresholds["memory_pressure_threshold"]}%)',
                'suggested_actions': ['decrease_batch_size', 'enable_gradient_checkpointing', 'clear_cache']
            })
            
        # 训练速度分析
        eps = metrics.get('episodes_per_second', {}).get('value', 0)
        avg_eps = metrics.get('episodes_per_second', {}).get('average', 0)
        if eps > 0 and avg_eps > 0 and eps < avg_eps * 0.7:
            bottlenecks.append({
                'type': 'training_speed_degradation',
                'severity': 'medium',
                'metric': 'episodes_per_second',
                'current_value': eps,
                'target_value': avg_eps,
                'description': f'训练速度下降显著 ({eps:.2f} < {avg_eps * 0.7:.2f} eps)',
                'suggested_actions': ['check_model_complexity', 'optimize_data_pipeline', 'reduce_batch_size']
            })
            
        # 损失趋势分析
        loss_trend = metrics.get('loss', {}).get('trend', 'unknown')
        if loss_trend == 'stable':
            recent_loss_values = metrics.get('loss', {}).get('recent_values', [])
            if len(recent_loss_values) > 10:
                loss_variance = np.var(recent_loss_values[-20:])
                if loss_variance < 1e-6:
                    bottlenecks.append({
                        'type': 'loss_plateau',
                        'severity': 'medium',
                        'metric': 'loss',
                        'current_value': recent_loss_values[-1] if recent_loss_values else 0,
                        'description': '损失进入平台期，学习停滞',
                        'suggested_actions': ['adjust_learning_rate', 'change_optimizer', 'add_regularization']
                    })
                    
        return bottlenecks
        
    def generate_optimization_recommendations(self, bottlenecks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """生成优化建议"""
        recommendations = []
        
        for bottleneck in bottlenecks:
            if bottleneck['type'] in ['gpu_utilization_critical', 'gpu_utilization_low']:
                # GPU利用率优化建议
                current_util = bottleneck['current_value']
                target_util = bottleneck['target_value']
                
                if current_util < target_util:
                    # 建议增加批次大小
                    new_batch_size = int(self.current_batch_size * self.thresholds['batch_size_adjustment_factor'])
                    recommendations.append({
                        'type': 'batch_size_increase',
                        'parameter': 'batch_size',
                        'current_value': self.current_batch_size,
                        'recommended_value': new_batch_size,
                        'reason': f'提高GPU利用率从{current_util:.1f}%到{target_util:.1f}%',
                        'expected_improvement': f'GPU利用率提升{(target_util - current_util):.1f}%',
                        'confidence': 0.8,
                        'auto_apply': True
                    })
                    
            elif bottleneck['type'] == 'memory_pressure_high':
                # 内存压力优化建议
                new_batch_size = int(self.current_batch_size * 0.8)
                recommendations.append({
                    'type': 'batch_size_decrease',
                    'parameter': 'batch_size',
                    'current_value': self.current_batch_size,
                    'recommended_value': new_batch_size,
                    'reason': f'降低GPU内存压力从{bottleneck["current_value"]:.1f}%',
                    'expected_improvement': '内存压力降低15-20%',
                    'confidence': 0.9,
                    'auto_apply': True
                })
                
            elif bottleneck['type'] == 'loss_plateau':
                # 学习率调整建议
                new_lr = self.current_learning_rate * 0.8
                recommendations.append({
                    'type': 'learning_rate_adjustment',
                    'parameter': 'learning_rate',
                    'current_value': self.current_learning_rate,
                    'recommended_value': new_lr,
                    'reason': '损失平台期，降低学习率有助于精细调优',
                    'expected_improvement': '损失继续下降',
                    'confidence': 0.7,
                    'auto_apply': False  # 学习率调整需要更谨慎
                })
                
        return recommendations
        
    def should_apply_tuning(self) -> bool:
        """判断是否应该应用调优"""
        if not self.tuning_enabled:
            return False
            
        current_time = time.time()
        if current_time - self.last_tuning_time < self.tuning_cooldown:
            return False
            
        return True
        
    def apply_optimization(self, recommendation: Dict[str, Any]) -> bool:
        """应用优化建议"""
        if not recommendation.get('auto_apply', False):
            self.logger.info(f"优化建议需要手动确认: {recommendation['type']}")
            return False
            
        if not self.should_apply_tuning():
            self.logger.debug("优化冷却期内，跳过自动调优")
            return False
            
        try:
            param_type = recommendation['parameter']
            new_value = recommendation['recommended_value']
            old_value = recommendation['current_value']
            
            if param_type == 'batch_size':
                self.current_batch_size = new_value
                self.logger.info(f"自动调整批次大小: {old_value} -> {new_value}")
                
            elif param_type == 'learning_rate':
                self.current_learning_rate = new_value
                self.logger.info(f"自动调整学习率: {old_value} -> {new_value}")
                
            # 记录优化历史
            self.optimization_history.record_optimization(
                optimization_type=recommendation['type'],
                old_value=old_value,
                new_value=new_value,
                reason=recommendation['reason'],
                expected_improvement=recommendation['expected_improvement']
            )
            
            self.last_tuning_time = time.time()
            return True
            
        except Exception as e:
            self.logger.error(f"应用优化失败: {e}")
            return False
            
    def get_current_parameters(self) -> Dict[str, Any]:
        """获取当前参数"""
        return {
            'batch_size': self.current_batch_size,
            'learning_rate': self.current_learning_rate,
            'tuning_enabled': self.tuning_enabled,
            'last_tuning_time': self.last_tuning_time
        }
        
    def enable_tuning(self, enable: bool = True):
        """启用/禁用自动调优"""
        self.tuning_enabled = enable
        self.logger.info(f"自动调优已{'启用' if enable else '禁用'}")


class RealtimeTrainingMonitor:
    """实时训练监控器"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化监控器
        
        Args:
            config: 监控配置
        """
        self.config = config or {}
        self.logger = get_logger(__name__)
        
        # 监控间隔
        self.monitor_interval = self.config.get('monitor_interval', 1.0)
        
        # GPU内存管理器
        self.gpu_memory_manager = GPUMemoryManager(
            target_usage=self.config.get('gpu_target_usage', 0.75),
            critical_threshold=self.config.get('gpu_critical_threshold', 0.90)
        )
        
        # 中断管理器
        self.interrupt_manager = InterruptManager.get_instance()
        
        # 高级GPU优化器
        gpu_config = GPUOptimizationConfig()
        self.gpu_optimizer = AdvancedGPUOptimizer(gpu_config)
        
        # WSL环境检测
        is_wsl = self._detect_wsl_environment()
        
        # 性能自动调优器 - WSL适配
        if is_wsl:
            default_gpu_target = 40.0
            logger.info("🎯 性能调优器：WSL环境GPU目标利用率设置为40%")
        else:
            default_gpu_target = 75.0
            
        tuner_config = {
            'gpu_utilization_target': self.config.get('gpu_utilization_target', default_gpu_target),
            'initial_batch_size': self.config.get('batch_size', 32),
            'initial_learning_rate': self.config.get('learning_rate', 0.001)
        }
        self.auto_tuner = PerformanceAutoTuner(tuner_config)
        
        # 指标追踪器
        self.metrics = {
            # GPU指标
            'gpu_utilization': MetricTracker(window_size=300, name='gpu_utilization'),
            'gpu_memory': MetricTracker(window_size=300, name='gpu_memory'),
            'gpu_temperature': MetricTracker(window_size=300, name='gpu_temperature'),
            
            # 内存指标
            'cpu_memory': MetricTracker(window_size=300, name='cpu_memory'),
            'cpu_percent': MetricTracker(window_size=300, name='cpu_percent'),
            
            # 训练指标
            'episodes_per_second': MetricTracker(window_size=100, name='episodes_per_second'),
            'samples_per_second': MetricTracker(window_size=100, name='samples_per_second'),
            'loss': MetricTracker(window_size=1000, name='loss'),
            'win_rate': MetricTracker(window_size=100, name='win_rate'),
            
            # 时间指标
            'step_time': MetricTracker(window_size=100, name='step_time'),
            'checkpoint_time': MetricTracker(window_size=50, name='checkpoint_time'),
            
            # 新增优化指标
            'batch_size': MetricTracker(window_size=100, name='batch_size'),
            'learning_rate': MetricTracker(window_size=100, name='learning_rate'),
            'optimization_score': MetricTracker(window_size=100, name='optimization_score')
        }
        
        # WSL环境检测
        self.is_wsl_environment = self._detect_wsl_environment()
        
        # 预警阈值 - WSL环境适配
        if self.is_wsl_environment:
            default_gpu_low = 30  # WSL环境下GPU利用率读数偏低，调整目标
            logger.info("✅ 检测到WSL环境，已调整GPU监控阈值以适应WSL的GPU利用率读数特性")
        else:
            default_gpu_low = 80  # 非WSL环境保持原有阈值
            
        self.alert_thresholds = {
            'gpu_utilization_low': self.config.get('gpu_utilization_low', default_gpu_low),
            'gpu_memory_high': self.config.get('gpu_memory_high', 90),
            'gpu_temperature_high': self.config.get('gpu_temperature_high', 85),
            'cpu_memory_high': self.config.get('cpu_memory_high', 85),
            'loss_nan_threshold': self.config.get('loss_nan_threshold', 3),
            'training_stagnation_steps': self.config.get('training_stagnation_steps', 1000)
        }
        
        # 监控状态
        self.monitoring_active = False
        self.monitor_thread = None
        self.start_time = None
        
        # 警报状态
        self.active_alerts = set()
        self.alert_callbacks = []
        
        # 检查点信息
        self.last_checkpoint_time = None
        self.checkpoint_count = 0
        
        # 训练进度
        self.total_episodes = 0
        self.total_samples = 0
        self.last_loss_update = 0
        
        # 内存泄漏检测
        self.memory_baseline = None
        self.memory_leak_threshold = self.config.get('memory_leak_threshold', 100)  # MB
        
        # 输出环境信息
        if self.is_wsl_environment:
            self.logger.info("🐧 实时训练监控器初始化完成 [WSL环境 - GPU监控已优化]")
            self.logger.info(f"📊 GPU利用率目标：{self.alert_thresholds['gpu_utilization_low']}% (WSL适配)")
        else:
            self.logger.info("💻 实时训练监控器初始化完成 [本地环境]")
            self.logger.info(f"📊 GPU利用率目标：{self.alert_thresholds['gpu_utilization_low']}%")
    
    def start_monitoring(self):
        """启动监控"""
        if self.monitoring_active:
            self.logger.warning("监控已在运行中")
            return
        
        self.monitoring_active = True
        self.start_time = time.time()
        
        # 设置内存基线
        self.memory_baseline = psutil.Process().memory_info().rss / (1024**2)  # MB
        
        # 启动GPU内存监控
        self.gpu_memory_manager.start_monitoring()
        
        # 启动高级GPU优化器监控
        self.gpu_optimizer.start_monitoring()
        
        # 启动主监控线程
        self.monitor_thread = threading.Thread(
            target=self._monitoring_loop,
            daemon=True
        )
        self.monitor_thread.start()
        
        # 注册中断回调
        self.interrupt_manager.register_shutdown_callback(self.stop_monitoring)
        
        # 输出启动信息
        env_type = "WSL环境" if self.is_wsl_environment else "本地环境"
        self.logger.info(f"🚀 实时监控已启动（包含性能自动调优） - {env_type}")
    
    def stop_monitoring(self):
        """停止监控"""
        if not self.monitoring_active:
            return
        
        self.monitoring_active = False
        
        # 停止GPU内存监控
        self.gpu_memory_manager.stop_monitoring()
        
        # 停止高级GPU优化器监控
        self.gpu_optimizer.stop_monitoring()
        
        # 等待监控线程结束
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5.0)
        
        # 生成最终报告
        self._generate_summary_report()
        
        # 生成优化报告
        self._generate_optimization_report()
        
        self.logger.info("实时监控已停止")
    
    def _detect_wsl_environment(self) -> bool:
        """检测是否为WSL环境"""
        try:
            # 检查 /proc/version 文件中是否包含Microsoft
            with open('/proc/version', 'r') as f:
                version_info = f.read().lower()
                return 'microsoft' in version_info or 'wsl' in version_info
        except (FileNotFoundError, PermissionError):
            # 如果无法读取文件，使用环境变量检测
            import os
            wsl_env = os.environ.get('WSL_DISTRO_NAME')
            return wsl_env is not None
    
    def _monitoring_loop(self):
        """主监控循环"""
        while self.monitoring_active and not self.interrupt_manager.is_interrupted():
            try:
                # 收集系统指标
                self._collect_system_metrics()
                
                # 检查异常和警报
                self._check_alerts()
                
                # 检查内存泄漏
                self._check_memory_leak()
                
                # 检查训练停滞
                self._check_training_stagnation()
                
                # 执行性能自动调优
                self._perform_auto_tuning()
                
            except Exception as e:
                self.logger.error(f"监控循环错误: {e}")
            
            time.sleep(self.monitor_interval)
    
    def _collect_system_metrics(self):
        """收集系统指标"""
        timestamp = time.time()
        
        # CPU和内存
        cpu_percent = psutil.cpu_percent(interval=0.1)
        memory = psutil.virtual_memory()
        process = psutil.Process()
        process_memory = process.memory_info().rss / (1024**2)  # MB
        
        self.metrics['cpu_percent'].add(cpu_percent, timestamp)
        self.metrics['cpu_memory'].add(process_memory, timestamp)
        
        # GPU指标（增强版）
        if torch.cuda.is_available():
            # 使用高级优化器获取GPU指标
            gpu_metrics = self.gpu_optimizer.gpu_monitor.get_current_metrics()
            if gpu_metrics:
                self.metrics['gpu_utilization'].add(gpu_metrics.utilization, timestamp)
                gpu_memory_percent = (gpu_metrics.memory_used_mb / gpu_metrics.memory_total_mb * 100) if gpu_metrics.memory_total_mb > 0 else 0
                self.metrics['gpu_memory'].add(gpu_memory_percent, timestamp)
                self.metrics['gpu_temperature'].add(gpu_metrics.temperature, timestamp)
            else:
                # 回退到基础GPU监控
                gpu_memory_allocated = torch.cuda.memory_allocated() / (1024**3)  # GB
                gpu_memory_reserved = torch.cuda.memory_reserved() / (1024**3)  # GB
                self.metrics['gpu_memory'].add(gpu_memory_reserved * 100 / 12, timestamp)  # 假设12GB显存
                
                # GPU利用率和温度（nvidia-ml-py）
                if NVML_AVAILABLE:
                    try:
                        handle = nvml.nvmlDeviceGetHandleByIndex(0)
                        
                        # 利用率
                        utilization = nvml.nvmlDeviceGetUtilizationRates(handle)
                        self.metrics['gpu_utilization'].add(utilization.gpu, timestamp)
                        
                        # 温度
                        temperature = nvml.nvmlDeviceGetTemperature(handle, nvml.NVML_TEMPERATURE_GPU)
                        self.metrics['gpu_temperature'].add(temperature, timestamp)
                        
                    except Exception as e:
                        self.logger.debug(f"NVML指标收集失败: {e}")
        
        # 记录当前优化参数
        current_params = self.auto_tuner.get_current_parameters()
        self.metrics['batch_size'].add(current_params['batch_size'], timestamp)
        self.metrics['learning_rate'].add(current_params['learning_rate'], timestamp)
        
        # 计算优化分数
        optimization_score = self._calculate_optimization_score()
        self.metrics['optimization_score'].add(optimization_score, timestamp)
    
    def _check_alerts(self):
        """检查预警条件"""
        current_alerts = set()
        
        # GPU利用率过低
        gpu_util = self.metrics['gpu_utilization'].get_latest()
        if gpu_util is not None and gpu_util < self.alert_thresholds['gpu_utilization_low']:
            current_alerts.add('gpu_utilization_low')
            if 'gpu_utilization_low' not in self.active_alerts:
                env_info = " [WSL环境适配]" if self.is_wsl_environment else ""
                self._trigger_alert('gpu_utilization_low', 
                                  f"GPU利用率较低: {gpu_util:.1f}% (目标: >{self.alert_thresholds['gpu_utilization_low']}%){env_info}")
        
        # GPU内存过高
        gpu_mem = self.metrics['gpu_memory'].get_latest()
        if gpu_mem is not None and gpu_mem > self.alert_thresholds['gpu_memory_high']:
            current_alerts.add('gpu_memory_high')
            if 'gpu_memory_high' not in self.active_alerts:
                self._trigger_alert('gpu_memory_high',
                                  f"GPU内存使用过高: {gpu_mem:.1f}% > {self.alert_thresholds['gpu_memory_high']}%")
        
        # GPU温度过高
        gpu_temp = self.metrics['gpu_temperature'].get_latest()
        if gpu_temp is not None and gpu_temp > self.alert_thresholds['gpu_temperature_high']:
            current_alerts.add('gpu_temperature_high')
            if 'gpu_temperature_high' not in self.active_alerts:
                self._trigger_alert('gpu_temperature_high',
                                  f"GPU温度过高: {gpu_temp}°C > {self.alert_thresholds['gpu_temperature_high']}°C")
        
        # CPU内存过高
        cpu_mem = self.metrics['cpu_memory'].get_latest()
        total_mem = psutil.virtual_memory().total / (1024**2)  # MB
        cpu_mem_percent = (cpu_mem / total_mem * 100) if total_mem > 0 else 0
        if cpu_mem_percent > self.alert_thresholds['cpu_memory_high']:
            current_alerts.add('cpu_memory_high')
            if 'cpu_memory_high' not in self.active_alerts:
                self._trigger_alert('cpu_memory_high',
                                  f"内存使用过高: {cpu_mem_percent:.1f}% > {self.alert_thresholds['cpu_memory_high']}%")
        
        # 更新活动警报
        self.active_alerts = current_alerts
    
    def _check_memory_leak(self):
        """检查内存泄漏"""
        if self.memory_baseline is None:
            return
        
        current_memory = self.metrics['cpu_memory'].get_latest()
        if current_memory is None:
            return
        
        memory_increase = current_memory - self.memory_baseline
        
        # 检查是否持续增长
        memory_trend = self.metrics['cpu_memory'].get_trend()
        
        if memory_increase > self.memory_leak_threshold and memory_trend == "increasing":
            if 'memory_leak' not in self.active_alerts:
                self._trigger_alert('memory_leak',
                                  f"检测到可能的内存泄漏: 内存增长 {memory_increase:.1f}MB")
                self.active_alerts.add('memory_leak')
    
    def _check_training_stagnation(self):
        """检查训练停滞"""
        # 检查损失是否长时间不更新
        if self.last_loss_update > 0:
            steps_since_update = self.total_samples - self.last_loss_update
            if steps_since_update > self.alert_thresholds['training_stagnation_steps']:
                if 'training_stagnation' not in self.active_alerts:
                    self._trigger_alert('training_stagnation',
                                      f"训练可能停滞: {steps_since_update}步未更新损失")
                    self.active_alerts.add('training_stagnation')
        
        # 检查损失趋势
        loss_trend = self.metrics['loss'].get_trend()
        if loss_trend == "stable" and len(self.metrics['loss'].values) > 100:
            recent_losses = list(self.metrics['loss'].values)[-50:]
            loss_variance = np.var(recent_losses)
            if loss_variance < 1e-6:  # 损失几乎不变
                if 'loss_plateau' not in self.active_alerts:
                    self._trigger_alert('loss_plateau',
                                      "损失达到平台期，可能需要调整学习率")
                    self.active_alerts.add('loss_plateau')
                    
    def _perform_auto_tuning(self):
        """执行性能自动调优"""
        try:
            # 获取当前指标
            current_metrics = {}
            for name, tracker in self.metrics.items():
                latest = tracker.get_latest()
                if latest is not None:
                    current_metrics[name] = {
                        'value': latest,
                        'average': tracker.get_average(last_n=10),
                        'trend': tracker.get_trend(),
                        'recent_values': list(tracker.values)[-20:] if hasattr(tracker, 'values') else []
                    }
            
            # 分析性能瓶颈
            bottlenecks = self.auto_tuner.analyze_performance_bottlenecks(current_metrics)
            
            if bottlenecks:
                self.logger.info(f"检测到 {len(bottlenecks)} 个性能瓶颈")
                
                # 生成优化建议
                recommendations = self.auto_tuner.generate_optimization_recommendations(bottlenecks)
                
                if recommendations:
                    self.logger.info(f"生成 {len(recommendations)} 个优化建议")
                    
                    # 应用自动优化
                    applied_count = 0
                    for recommendation in recommendations:
                        if self.auto_tuner.apply_optimization(recommendation):
                            applied_count += 1
                            
                    if applied_count > 0:
                        self.logger.info(f"自动应用了 {applied_count} 个优化")
                        
                        # 触发优化警报
                        self._trigger_alert('auto_optimization_applied',
                                          f"应用了{applied_count}个自动优化: {[r['type'] for r in recommendations if r.get('auto_apply', False)]}")
                        
        except Exception as e:
            self.logger.error(f"自动调优失败: {e}")
            
    def _calculate_optimization_score(self) -> float:
        """计算优化分数 (0-100)"""
        try:
            score = 0.0
            
            # GPU利用率得分 (40%权重)
            gpu_util = self.metrics['gpu_utilization'].get_latest()
            if gpu_util is not None:
                target_util = self.auto_tuner.thresholds['gpu_utilization_target']
                if gpu_util >= target_util:
                    gpu_score = 40.0
                else:
                    gpu_score = (gpu_util / target_util) * 40.0
                score += gpu_score
            
            # 内存使用效率得分 (20%权重)
            gpu_memory = self.metrics['gpu_memory'].get_latest()
            if gpu_memory is not None:
                # 理想内存使用率在60-80%之间
                if 60 <= gpu_memory <= 80:
                    memory_score = 20.0
                elif gpu_memory < 60:
                    memory_score = (gpu_memory / 60) * 20.0
                else:
                    memory_score = max(0, 20.0 - (gpu_memory - 80) * 0.5)
                score += memory_score
            
            # 训练速度得分 (25%权重)
            eps = self.metrics['episodes_per_second'].get_latest()
            avg_eps = self.metrics['episodes_per_second'].get_average()
            if eps is not None and avg_eps > 0:
                speed_ratio = eps / avg_eps
                if speed_ratio >= 1.0:
                    speed_score = 25.0
                else:
                    speed_score = speed_ratio * 25.0
                score += speed_score
            
            # 损失趋势得分 (15%权重)
            loss_trend = self.metrics['loss'].get_trend()
            if loss_trend == 'decreasing':
                trend_score = 15.0
            elif loss_trend == 'stable':
                trend_score = 10.0
            else:
                trend_score = 5.0
            score += trend_score
            
            return min(100.0, max(0.0, score))
            
        except Exception as e:
            self.logger.error(f"计算优化分数失败: {e}")
            return 50.0  # 默认中等分数
    
    def _trigger_alert(self, alert_type: str, message: str):
        """触发警报"""
        self.logger.warning(f"⚠️ {message}")
        
        # 执行回调
        for callback in self.alert_callbacks:
            try:
                callback(alert_type, message)
            except Exception as e:
                self.logger.error(f"警报回调执行失败: {e}")
    
    def register_alert_callback(self, callback: Callable[[str, str], None]):
        """注册警报回调函数"""
        self.alert_callbacks.append(callback)
    
    def update_training_metrics(self,
                              loss: Optional[float] = None,
                              win_rate: Optional[float] = None,
                              episodes: Optional[int] = None,
                              samples: Optional[int] = None):
        """
        更新训练指标
        
        Args:
            loss: 损失值
            win_rate: 胜率
            episodes: 完成的回合数
            samples: 处理的样本数
        """
        timestamp = time.time()
        
        # 更新损失
        if loss is not None:
            # 检查NaN/Inf
            if np.isnan(loss) or np.isinf(loss):
                self._trigger_alert('loss_nan', f"检测到异常损失值: {loss}")
                # 不记录异常值
            else:
                self.metrics['loss'].add(loss, timestamp)
                self.last_loss_update = self.total_samples
                
                # 检测损失异常
                if self.metrics['loss'].detect_anomaly():
                    self._trigger_alert('loss_anomaly', 
                                      f"损失异常: {loss:.4f} (正常范围外)")
        
        # 更新胜率
        if win_rate is not None:
            self.metrics['win_rate'].add(win_rate, timestamp)
        
        # 更新训练速度
        if episodes is not None:
            self.total_episodes = episodes
            if hasattr(self, '_last_episode_update'):
                time_delta = timestamp - self._last_episode_update[0]
                episode_delta = episodes - self._last_episode_update[1]
                if time_delta > 0:
                    eps = episode_delta / time_delta
                    self.metrics['episodes_per_second'].add(eps, timestamp)
            self._last_episode_update = (timestamp, episodes)
        
        if samples is not None:
            self.total_samples = samples
            if hasattr(self, '_last_sample_update'):
                time_delta = timestamp - self._last_sample_update[0]
                sample_delta = samples - self._last_sample_update[1]
                if time_delta > 0:
                    sps = sample_delta / time_delta
                    self.metrics['samples_per_second'].add(sps, timestamp)
            self._last_sample_update = (timestamp, samples)
    
    def record_checkpoint_save(self, save_time: float):
        """记录检查点保存"""
        self.checkpoint_count += 1
        self.last_checkpoint_time = time.time()
        self.metrics['checkpoint_time'].add(save_time)
        
        if save_time > 10.0:  # 保存时间超过10秒
            self._trigger_alert('slow_checkpoint',
                              f"检查点保存缓慢: {save_time:.1f}秒")
    
    def get_current_stats(self) -> Dict[str, Any]:
        """获取当前统计信息"""
        stats = {
            'uptime': time.time() - self.start_time if self.start_time else 0,
            'total_episodes': self.total_episodes,
            'total_samples': self.total_samples,
            'checkpoint_count': self.checkpoint_count,
            'active_alerts': list(self.active_alerts),
            'metrics': {}
        }
        
        # 添加各项指标的当前值和趋势
        for name, tracker in self.metrics.items():
            latest = tracker.get_latest()
            if latest is not None:
                stats['metrics'][name] = {
                    'value': latest,
                    'average': tracker.get_average(last_n=10),
                    'trend': tracker.get_trend()
                }
        
        # 添加GPU内存管理器的统计
        stats['gpu_memory_stats'] = self.gpu_memory_manager.get_memory_stats()
        
        # 添加自动调优统计
        stats['auto_tuning_stats'] = {
            'current_parameters': self.auto_tuner.get_current_parameters(),
            'optimization_history': self.auto_tuner.optimization_history.get_optimization_summary(),
            'tuning_enabled': self.auto_tuner.tuning_enabled
        }
        
        # 添加优化器统计
        stats['gpu_optimization_stats'] = self.gpu_optimizer.get_optimization_report()
        
        return stats
    
    def _generate_summary_report(self):
        """生成监控总结报告"""
        if self.start_time is None:
            return
        
        duration = time.time() - self.start_time
        
        report = {
            'duration': duration,
            'total_episodes': self.total_episodes,
            'total_samples': self.total_samples,
            'checkpoint_count': self.checkpoint_count,
            'alerts_triggered': len(self.active_alerts),
            'metrics_summary': {}
        }
        
        # 汇总各项指标
        for name, tracker in self.metrics.items():
            if len(tracker.values) > 0:
                report['metrics_summary'][name] = {
                    'min': float(np.min(tracker.values)),
                    'max': float(np.max(tracker.values)),
                    'mean': float(np.mean(tracker.values)),
                    'std': float(np.std(tracker.values)),
                    'final': float(tracker.get_latest())
                }
        
        # 保存报告
        report_dir = Path("monitoring_reports")
        report_dir.mkdir(exist_ok=True)
        
        report_file = report_dir / f"monitor_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2)
        
        self.logger.info(f"监控报告已保存: {report_file}")
        
        # 打印摘要
        self.logger.info("=" * 60)
        self.logger.info("监控总结")
        self.logger.info("=" * 60)
        self.logger.info(f"监控时长: {duration/3600:.1f}小时")
        self.logger.info(f"总回合数: {self.total_episodes}")
        self.logger.info(f"总样本数: {self.total_samples}")
        self.logger.info(f"检查点数: {self.checkpoint_count}")
        self.logger.info(f"触发警报: {len(self.active_alerts)}")
        
        if 'gpu_utilization' in report['metrics_summary']:
            gpu_stats = report['metrics_summary']['gpu_utilization']
            self.logger.info(f"GPU利用率: 平均{gpu_stats['mean']:.1f}%, 最高{gpu_stats['max']:.1f}%")
        
        if 'loss' in report['metrics_summary']:
            loss_stats = report['metrics_summary']['loss']
            self.logger.info(f"损失: 初始{loss_stats['max']:.4f}, 最终{loss_stats['final']:.4f}")
        
        if 'win_rate' in report['metrics_summary']:
            wr_stats = report['metrics_summary']['win_rate']
            self.logger.info(f"胜率: 平均{wr_stats['mean']:.1f}%, 最终{wr_stats['final']:.1f}%")
            
        # 优化相关摘要
        if 'optimization_score' in report['metrics_summary']:
            opt_stats = report['metrics_summary']['optimization_score']
            self.logger.info(f"优化分数: 平均{opt_stats['mean']:.1f}, 最终{opt_stats['final']:.1f}")
            
        if 'batch_size' in report['metrics_summary']:
            bs_stats = report['metrics_summary']['batch_size']
            self.logger.info(f"批次大小: 初始{bs_stats['min']:.0f}, 最终{bs_stats['final']:.0f}")
            
    def _generate_optimization_report(self):
        """生成优化报告"""
        try:
            optimization_summary = self.auto_tuner.optimization_history.get_optimization_summary()
            gpu_optimization_report = self.gpu_optimizer.get_optimization_report()
            
            report = {
                'timestamp': datetime.now().isoformat(),
                'auto_tuning_summary': optimization_summary,
                'gpu_optimization_report': gpu_optimization_report,
                'final_parameters': self.auto_tuner.get_current_parameters()
            }
            
            # 保存优化报告
            report_dir = Path("optimization_reports")
            report_dir.mkdir(exist_ok=True)
            
            report_file = report_dir / f"optimization_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(report_file, 'w') as f:
                json.dump(report, f, indent=2, default=str)
            
            self.logger.info(f"优化报告已保存: {report_file}")
            
            # 打印优化摘要
            self.logger.info("=" * 60)
            self.logger.info("优化总结")
            self.logger.info("=" * 60)
            
            total_optimizations = optimization_summary.get('total_optimizations', 0)
            successful_optimizations = optimization_summary.get('successful_optimizations', 0)
            
            self.logger.info(f"总优化次数: {total_optimizations}")
            self.logger.info(f"成功优化次数: {successful_optimizations}")
            
            if total_optimizations > 0:
                success_rate = (successful_optimizations / total_optimizations) * 100
                self.logger.info(f"优化成功率: {success_rate:.1f}%")
                
            optimization_types = optimization_summary.get('optimization_types', {})
            if optimization_types:
                self.logger.info("优化类型分布:")
                for opt_type, count in optimization_types.items():
                    self.logger.info(f"  {opt_type}: {count}次")
                    
        except Exception as e:
            self.logger.error(f"生成优化报告失败: {e}")


class TrainingMonitorDashboard:
    """训练监控仪表板（文本版）"""
    
    def __init__(self, monitor: RealtimeTrainingMonitor):
        self.monitor = monitor
        self.refresh_interval = 5.0
        self.running = False
        
    def start(self):
        """启动仪表板"""
        self.running = True
        
        while self.running and not self.monitor.interrupt_manager.is_interrupted():
            self._display_dashboard()
            time.sleep(self.refresh_interval)
    
    def stop(self):
        """停止仪表板"""
        self.running = False
    
    def _display_dashboard(self):
        """显示仪表板"""
        # 清屏（跨平台）
        os.system('cls' if os.name == 'nt' else 'clear')
        
        stats = self.monitor.get_current_stats()
        
        print("=" * 80)
        print("实时训练监控仪表板".center(80))
        print("=" * 80)
        
        # 运行时间
        uptime = stats['uptime']
        hours = int(uptime // 3600)
        minutes = int((uptime % 3600) // 60)
        seconds = int(uptime % 60)
        print(f"运行时间: {hours:02d}:{minutes:02d}:{seconds:02d}")
        print(f"总回合数: {stats['total_episodes']:,}")
        print(f"总样本数: {stats['total_samples']:,}")
        print(f"检查点数: {stats['checkpoint_count']}")
        
        print("\n" + "-" * 80)
        print("系统指标")
        print("-" * 80)
        
        # 显示各项指标
        metrics = stats['metrics']
        
        # GPU指标
        if 'gpu_utilization' in metrics:
            gpu_util = metrics['gpu_utilization']
            print(f"GPU利用率: {gpu_util['value']:5.1f}% (平均: {gpu_util['average']:5.1f}%) [{gpu_util['trend']}]")
        
        if 'gpu_memory' in metrics:
            gpu_mem = metrics['gpu_memory']
            print(f"GPU内存:   {gpu_mem['value']:5.1f}% (平均: {gpu_mem['average']:5.1f}%) [{gpu_mem['trend']}]")
        
        if 'gpu_temperature' in metrics:
            gpu_temp = metrics['gpu_temperature']
            print(f"GPU温度:   {gpu_temp['value']:5.0f}°C (平均: {gpu_temp['average']:5.1f}°C) [{gpu_temp['trend']}]")
        
        # CPU指标
        if 'cpu_percent' in metrics:
            cpu = metrics['cpu_percent']
            print(f"CPU使用率: {cpu['value']:5.1f}% (平均: {cpu['average']:5.1f}%) [{cpu['trend']}]")
        
        if 'cpu_memory' in metrics:
            mem = metrics['cpu_memory']
            print(f"内存使用:  {mem['value']:6.0f}MB (平均: {mem['average']:6.0f}MB) [{mem['trend']}]")
        
        print("\n" + "-" * 80)
        print("训练指标")
        print("-" * 80)
        
        # 训练速度
        if 'episodes_per_second' in metrics:
            eps = metrics['episodes_per_second']
            print(f"回合速度: {eps['value']:6.2f} eps (平均: {eps['average']:6.2f} eps)")
        
        if 'samples_per_second' in metrics:
            sps = metrics['samples_per_second']
            print(f"样本速度: {sps['value']:6.0f} sps (平均: {sps['average']:6.0f} sps)")
        
        # 损失和胜率
        if 'loss' in metrics:
            loss = metrics['loss']
            print(f"损失:     {loss['value']:8.4f} (平均: {loss['average']:8.4f}) [{loss['trend']}]")
        
        if 'win_rate' in metrics:
            wr = metrics['win_rate']
            print(f"胜率:     {wr['value']:5.1f}% (平均: {wr['average']:5.1f}%)")
        
        # 警报
        if stats['active_alerts']:
            print("\n" + "-" * 80)
            print("⚠️  活动警报")
            print("-" * 80)
            for alert in stats['active_alerts']:
                print(f"• {alert}")
        
        print("\n按Ctrl+C停止监控...")


def create_training_monitor_with_callbacks() -> RealtimeTrainingMonitor:
    """创建带有默认回调的训练监控器"""
    
    monitor = RealtimeTrainingMonitor()
    
    # 添加警报回调
    def alert_callback(alert_type: str, message: str):
        # 这里可以添加自定义的警报处理，如发送通知、记录到文件等
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        with open("training_alerts.log", "a") as f:
            f.write(f"[{timestamp}] {alert_type}: {message}\n")
    
    monitor.register_alert_callback(alert_callback)
    
    # 注册GPU内存压力回调
    def memory_pressure_callback(pressure: str, memory_info: Tuple[float, float, float]):
        if pressure == 'critical':
            logger.error(f"GPU内存压力严重！建议立即降低batch_size")
        elif pressure == 'high':
            logger.warning(f"GPU内存压力较高，考虑优化内存使用")
    
    monitor.gpu_memory_manager.register_callback(memory_pressure_callback)
    
    return monitor


if __name__ == "__main__":
    """测试监控器"""
    import argparse
    
    parser = argparse.ArgumentParser(description="实时训练监控器")
    parser.add_argument('--dashboard', action='store_true', help='启动监控仪表板')
    parser.add_argument('--test', action='store_true', help='运行测试模式')
    
    args = parser.parse_args()
    
    if args.test:
        # 测试模式
        print("启动监控器测试...")
        monitor = create_training_monitor_with_callbacks()
        monitor.start_monitoring()
        
        # 模拟训练过程
        import random
        
        try:
            for i in range(100):
                # 模拟训练指标更新
                loss = 1.0 / (i + 1) + random.random() * 0.1
                win_rate = min(95, 50 + i * 0.5 + random.random() * 5)
                
                monitor.update_training_metrics(
                    loss=loss,
                    win_rate=win_rate,
                    episodes=i * 10,
                    samples=i * 1000
                )
                
                # 偶尔保存检查点
                if i % 20 == 0:
                    monitor.record_checkpoint_save(random.uniform(0.5, 3.0))
                
                time.sleep(0.5)
                
                # 打印当前状态
                if i % 10 == 0:
                    stats = monitor.get_current_stats()
                    print(f"\n步骤 {i}:")
                    print(f"  损失: {loss:.4f}")
                    print(f"  胜率: {win_rate:.1f}%")
                    print(f"  活动警报: {stats['active_alerts']}")
                    
        except KeyboardInterrupt:
            print("\n停止测试...")
        finally:
            monitor.stop_monitoring()
    
    elif args.dashboard:
        # 启动仪表板
        monitor = create_training_monitor_with_callbacks()
        monitor.start_monitoring()
        
        dashboard = TrainingMonitorDashboard(monitor)
        try:
            dashboard.start()
        except KeyboardInterrupt:
            print("\n停止仪表板...")
        finally:
            monitor.stop_monitoring()
    
    else:
        parser.print_help()