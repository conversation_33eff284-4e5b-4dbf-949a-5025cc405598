"""
质量验证测试模块

提供训练质量验证的pytest集成
"""

from .test_training_quality import (
    TestPrerequisites,
    TestArchitectureConsistency,
    TestPerformanceBenchmarks,
    TestRealtimeMonitoring,
    TestModelEvaluation,
    TestQualityIntegration,
    TestReportGeneration,
    TestPerformanceRegression,
    run_quality_tests
)

__all__ = [
    'TestPrerequisites',
    'TestArchitectureConsistency', 
    'TestPerformanceBenchmarks',
    'TestRealtimeMonitoring',
    'TestModelEvaluation',
    'TestQualityIntegration',
    'TestReportGeneration',
    'TestPerformanceRegression',
    'run_quality_tests'
]