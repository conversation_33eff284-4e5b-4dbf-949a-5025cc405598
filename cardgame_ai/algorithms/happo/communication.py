"""
HAPPO农民通信模块
实现农民之间的隐式通信机制
"""

import torch
import torch.nn as nn
from typing import Tuple
from cardgame_ai.utils.logging import get_logger

logger = get_logger(__name__)

class FarmerCommunicationModule(nn.Module):
    """农民通信模块 - 实现农民之间的隐式通信"""
    
    def __init__(self, hidden_dim: int, communication_dim: int, num_rounds: int = 2):
        """
        初始化通信模块
        
        Args:
            hidden_dim: 隐藏层维度
            communication_dim: 通信消息维度
            num_rounds: 通信轮数
        """
        super().__init__()
        self.hidden_dim = hidden_dim
        self.communication_dim = communication_dim
        self.num_rounds = num_rounds
        
        # 消息编码器
        self.message_encoder = nn.Sequential(
            nn.Linear(hidden_dim, communication_dim),
            nn.ReLU(),
            nn.LayerNorm(communication_dim)
        )
        
        # 消息解码器
        self.message_decoder = nn.GRU(
            input_size=communication_dim,
            hidden_size=hidden_dim,
            num_layers=1,
            batch_first=True
        )
        
        # 注意力机制
        self.attention = nn.MultiheadAttention(
            embed_dim=communication_dim,
            num_heads=4,
            dropout=0.1,
            batch_first=True
        )
        
        logger.info(f"农民通信模块初始化: dim={communication_dim}, rounds={num_rounds}")
        
    def forward(self, farmer1_hidden: torch.Tensor, farmer2_hidden: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        农民之间的通信
        
        Args:
            farmer1_hidden: 农民1的隐藏状态
            farmer2_hidden: 农民2的隐藏状态
            
        Returns:
            (增强后的农民1状态, 增强后的农民2状态)
        """
        batch_size = farmer1_hidden.size(0)
        
        # 多轮通信
        h1, h2 = farmer1_hidden, farmer2_hidden
        
        for round_idx in range(self.num_rounds):
            # 编码消息
            msg1 = self.message_encoder(h1)
            msg2 = self.message_encoder(h2)
            
            # 注意力机制交换信息
            # 农民1接收农民2的信息
            msg1_attended, _ = self.attention(
                msg1.unsqueeze(1),
                msg2.unsqueeze(1),
                msg2.unsqueeze(1)
            )
            
            # 农民2接收农民1的信息
            msg2_attended, _ = self.attention(
                msg2.unsqueeze(1),
                msg1.unsqueeze(1),
                msg1.unsqueeze(1)
            )
            
            # 解码并更新隐藏状态
            h1_new, _ = self.message_decoder(msg1_attended, h1.unsqueeze(0))
            h2_new, _ = self.message_decoder(msg2_attended, h2.unsqueeze(0))
            
            h1 = h1_new.squeeze(0)
            h2 = h2_new.squeeze(0)
            
        return h1, h2