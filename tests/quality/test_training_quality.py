#!/usr/bin/env python3
"""
训练质量验证pytest测试集

将所有质量检查功能集成到pytest框架中，支持CI/CD自动化
"""

import pytest
import sys
import time
import torch
import numpy as np
from pathlib import Path
from typing import Dict, Any, List

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from cardgame_ai.validation.training_quality_validator import (
    TrainingQualityValidator, 
    QualityCheckResult,
    QualityReport
)
from cardgame_ai.monitoring.realtime_training_monitor import (
    RealtimeTrainingMonitor,
    MetricTracker
)
from cardgame_ai.evaluation.model_quality_evaluator import (
    ModelQualityEvaluator,
    EvaluationConfig,
    EvaluationResult
)
from cardgame_ai.utils.logging import get_logger

logger = get_logger(__name__)


# ==================== Pytest Markers ====================
pytestmark = [
    pytest.mark.quality,  # 所有测试都标记为质量测试
]


# ==================== Fixtures ====================

@pytest.fixture
def quality_validator(test_config):
    """创建质量验证器实例"""
    validator = TrainingQualityValidator(config=test_config)
    return validator


@pytest.fixture
def training_monitor():
    """创建训练监控器实例"""
    config = {
        'monitor_interval': 0.1,  # 快速测试
        'gpu_target_usage': 0.75,
        'gpu_critical_threshold': 0.90
    }
    monitor = RealtimeTrainingMonitor(config)
    yield monitor
    # 清理
    if monitor.monitoring_active:
        monitor.stop_monitoring()


@pytest.fixture
def model_evaluator():
    """创建模型评估器实例"""
    config = EvaluationConfig(
        num_games_per_opponent=10,  # 快速测试
        num_parallel_workers=2,
        generate_visualizations=False
    )
    return ModelQualityEvaluator(config)


@pytest.fixture
def mock_training_process():
    """模拟训练过程"""
    class MockTrainingProcess:
        def __init__(self):
            self.step = 0
            self.loss = 1.0
            self.win_rate = 50.0
            
        def train_step(self):
            """模拟一步训练"""
            self.step += 1
            self.loss *= 0.99  # 模拟损失下降
            self.win_rate = min(95, self.win_rate + np.random.uniform(0, 2))
            
            return {
                'loss': self.loss,
                'win_rate': self.win_rate,
                'episodes': self.step * 10,
                'samples': self.step * 1000
            }
    
    return MockTrainingProcess()


# ==================== 环境和配置验证测试 ====================

class TestPrerequisites:
    """前提条件验证测试"""
    
    @pytest.mark.order(1)
    def test_environment_check(self, quality_validator):
        """测试环境检查"""
        passed, results = quality_validator.validate_prerequisites()
        
        # 检查结果
        assert isinstance(results, list)
        assert len(results) > 0
        
        # 查找环境检查结果
        env_result = next((r for r in results if r.check_name == 'environment'), None)
        assert env_result is not None
        
        # 验证环境检查内容
        assert 'python_version' in env_result.details
        assert 'pytorch_installed' in env_result.details
        assert 'cuda_available' in env_result.details
        assert 'memory_sufficient' in env_result.details
        
        logger.info(f"环境检查完成: {env_result.passed}")
    
    @pytest.mark.order(2)
    def test_config_validation(self, quality_validator):
        """测试配置验证"""
        passed, results = quality_validator.validate_prerequisites()
        
        # 查找配置验证结果
        config_result = next((r for r in results if r.check_name == 'config'), None)
        assert config_result is not None
        
        # 验证配置检查内容
        assert config_result.score >= 0
        assert config_result.score <= 100
        
        if not config_result.passed:
            logger.warning(f"配置验证未通过: {config_result.error_message}")
    
    @pytest.mark.gpu
    def test_gpu_availability(self, quality_validator):
        """测试GPU可用性"""
        if not torch.cuda.is_available():
            pytest.skip("GPU不可用，跳过GPU测试")
        
        passed, results = quality_validator.validate_prerequisites()
        
        # 查找环境检查结果
        env_result = next((r for r in results if r.check_name == 'environment'), None)
        assert env_result.details.get('cuda_available', False) == True
        assert env_result.details.get('gpu_count', 0) >= 1


# ==================== 架构一致性测试 ====================

class TestArchitectureConsistency:
    """架构一致性测试"""
    
    @pytest.mark.order(3)
    def test_dimension_consistency(self, quality_validator):
        """测试维度一致性"""
        passed, result = quality_validator.validate_architecture()
        
        assert isinstance(result, QualityCheckResult)
        assert result.check_name == 'architecture'
        assert result.score >= 0
        assert result.score <= 100
        
        # 检查维度一致性细节
        if 'dimension_violations' in result.details:
            violations = result.details['dimension_violations']
            assert isinstance(violations, list)
            if violations:
                logger.warning(f"发现维度不一致: {violations}")
        
        # 验证通过率
        assert result.details.get('passed_tests', 0) >= 0
        assert result.details.get('total_tests', 0) > 0
    
    @pytest.mark.performance
    def test_architecture_performance(self, quality_validator):
        """测试架构性能影响"""
        # 运行架构验证并计时
        start_time = time.time()
        passed, result = quality_validator.validate_architecture()
        duration = time.time() - start_time
        
        # 架构验证不应该太慢
        assert duration < 10.0, f"架构验证耗时过长: {duration:.2f}秒"
        
        logger.info(f"架构验证耗时: {duration:.2f}秒")


# ==================== 性能基准测试 ====================

class TestPerformanceBenchmarks:
    """性能基准测试"""
    
    @pytest.mark.performance
    @pytest.mark.order(4)
    def test_performance_validation(self, quality_validator):
        """测试性能验证"""
        passed, result = quality_validator.validate_performance()
        
        assert isinstance(result, QualityCheckResult)
        assert result.check_name == 'performance'
        
        # 检查性能测试结果
        if 'summary' in result.details:
            summary = result.details['summary']
            assert 'success_rate' in summary
            assert 'total_duration' in summary
            
            logger.info(f"性能测试成功率: {summary['success_rate']:.1f}%")
            logger.info(f"性能测试耗时: {summary['total_duration']:.1f}秒")
    
    @pytest.mark.gpu
    @pytest.mark.performance
    def test_gpu_utilization(self, quality_validator):
        """测试GPU利用率基准"""
        if not torch.cuda.is_available():
            pytest.skip("GPU不可用")
        
        passed, result = quality_validator.validate_performance()
        
        # 检查GPU利用率测试结果
        if 'gpu_utilization' in result.details:
            gpu_tests = result.details['gpu_utilization']
            if 'pass_rate' in gpu_tests:
                assert gpu_tests['pass_rate'] >= 0
                logger.info(f"GPU利用率测试通过率: {gpu_tests['pass_rate']:.1f}%")
    
    @pytest.mark.performance
    def test_memory_efficiency(self, quality_validator):
        """测试内存效率基准"""
        passed, result = quality_validator.validate_performance()
        
        # 检查内存效率测试结果
        if 'memory_efficiency' in result.details:
            mem_tests = result.details['memory_efficiency']
            if 'reduction_achieved' in mem_tests:
                reduction = mem_tests['reduction_achieved']
                assert reduction >= 0
                logger.info(f"内存减少率: {reduction:.1f}%")


# ==================== 实时监控测试 ====================

class TestRealtimeMonitoring:
    """实时监控测试"""
    
    @pytest.mark.order(5)
    def test_monitor_initialization(self, training_monitor):
        """测试监控器初始化"""
        assert training_monitor is not None
        assert not training_monitor.monitoring_active
        assert len(training_monitor.metrics) > 0
        
        # 检查关键指标追踪器
        assert 'gpu_utilization' in training_monitor.metrics
        assert 'loss' in training_monitor.metrics
        assert 'win_rate' in training_monitor.metrics
    
    def test_monitor_start_stop(self, training_monitor):
        """测试监控器启动和停止"""
        # 启动监控
        training_monitor.start_monitoring()
        assert training_monitor.monitoring_active
        
        # 等待一段时间
        time.sleep(0.5)
        
        # 停止监控
        training_monitor.stop_monitoring()
        assert not training_monitor.monitoring_active
    
    def test_metric_tracking(self, training_monitor, mock_training_process):
        """测试指标追踪"""
        training_monitor.start_monitoring()
        
        # 模拟训练过程
        for _ in range(5):
            metrics = mock_training_process.train_step()
            training_monitor.update_training_metrics(**metrics)
            time.sleep(0.1)
        
        # 获取统计信息
        stats = training_monitor.get_current_stats()
        
        assert 'metrics' in stats
        assert 'loss' in stats['metrics']
        assert 'win_rate' in stats['metrics']
        
        training_monitor.stop_monitoring()
    
    @pytest.mark.performance
    def test_monitor_performance_impact(self, training_monitor, mock_training_process):
        """测试监控器性能影响"""
        # 无监控情况下的训练速度
        start_time = time.time()
        for _ in range(100):
            mock_training_process.train_step()
        baseline_time = time.time() - start_time
        
        # 有监控情况下的训练速度
        training_monitor.start_monitoring()
        start_time = time.time()
        for _ in range(100):
            metrics = mock_training_process.train_step()
            training_monitor.update_training_metrics(**metrics)
        monitored_time = time.time() - start_time
        training_monitor.stop_monitoring()
        
        # 监控开销不应该超过10%
        overhead = (monitored_time - baseline_time) / baseline_time
        assert overhead < 0.1, f"监控开销过大: {overhead*100:.1f}%"
        
        logger.info(f"监控开销: {overhead*100:.1f}%")


# ==================== 模型评估测试 ====================

class TestModelEvaluation:
    """模型评估测试"""
    
    @pytest.mark.slow
    @pytest.mark.order(6)
    def test_model_evaluation_basic(self, model_evaluator, trained_model_path):
        """测试基本模型评估"""
        # 执行评估
        result = model_evaluator.evaluate_model(trained_model_path)
        
        assert isinstance(result, EvaluationResult)
        assert result.total_games > 0
        assert 0 <= result.overall_win_rate <= 1
        assert result.mean_decision_time >= 0
        
        logger.info(f"模型评估完成: 胜率={result.overall_win_rate:.2%}")
    
    def test_evaluation_metrics(self, model_evaluator, trained_model_path):
        """测试评估指标完整性"""
        result = model_evaluator.evaluate_model(trained_model_path)
        
        # 检查所有必需的指标
        assert hasattr(result, 'overall_win_rate')
        assert hasattr(result, 'landlord_win_rate')
        assert hasattr(result, 'farmer_win_rate')
        assert hasattr(result, 'win_rates_by_difficulty')
        assert hasattr(result, 'cooperation_score')
        assert hasattr(result, 'bomb_strategy_score')
        assert hasattr(result, 'mean_decision_time')
        
        # 检查分难度胜率
        assert isinstance(result.win_rates_by_difficulty, dict)
        assert len(result.win_rates_by_difficulty) > 0
    
    @pytest.mark.performance
    def test_evaluation_performance(self, model_evaluator, trained_model_path):
        """测试评估性能"""
        # 配置快速评估
        model_evaluator.config.num_games_per_opponent = 5
        model_evaluator.config.num_parallel_workers = 1
        
        start_time = time.time()
        result = model_evaluator.evaluate_model(trained_model_path)
        duration = time.time() - start_time
        
        # 快速评估应该在合理时间内完成
        assert duration < 60.0, f"评估耗时过长: {duration:.1f}秒"
        
        # 计算每局游戏的平均时间
        time_per_game = duration / result.total_games
        logger.info(f"每局游戏评估时间: {time_per_game:.3f}秒")


# ==================== 集成测试 ====================

class TestQualityIntegration:
    """质量系统集成测试"""
    
    @pytest.mark.integration
    @pytest.mark.slow
    def test_full_quality_pipeline(self, quality_validator, training_monitor, model_evaluator):
        """测试完整的质量验证流程"""
        # 1. 启动前验证
        prereq_passed, prereq_results = quality_validator.validate_prerequisites()
        assert prereq_passed or True  # 允许部分失败
        
        # 2. 架构验证
        arch_passed, arch_result = quality_validator.validate_architecture()
        assert arch_result.score > 0
        
        # 3. 启动监控
        training_monitor.start_monitoring()
        
        # 4. 模拟训练
        mock_process = MockTrainingProcess()
        for _ in range(10):
            metrics = mock_process.train_step()
            training_monitor.update_training_metrics(**metrics)
            time.sleep(0.05)
        
        # 5. 停止监控并获取统计
        training_monitor.stop_monitoring()
        monitor_stats = training_monitor.get_current_stats()
        
        # 6. 生成质量报告
        report = quality_validator.generate_report()
        
        assert isinstance(report, QualityReport)
        assert report.overall_score >= 0
        assert report.overall_score <= 100
        
        logger.info(f"质量验证完成，总分: {report.overall_score:.1f}/100")
    
    @pytest.mark.integration
    def test_alert_system(self, training_monitor):
        """测试预警系统"""
        alerts_triggered = []
        
        def alert_callback(alert_type, message):
            alerts_triggered.append((alert_type, message))
        
        # 注册回调
        training_monitor.register_alert_callback(alert_callback)
        training_monitor.start_monitoring()
        
        # 模拟异常情况
        training_monitor.update_training_metrics(
            loss=float('nan'),  # NaN损失
            win_rate=0.0,       # 极低胜率
            episodes=100,
            samples=1000
        )
        
        time.sleep(0.5)
        training_monitor.stop_monitoring()
        
        # 应该触发损失异常警报
        assert len(alerts_triggered) > 0
        assert any('loss_nan' in alert[0] for alert in alerts_triggered)


# ==================== 报告生成测试 ====================

class TestReportGeneration:
    """报告生成测试"""
    
    def test_quality_report_generation(self, quality_validator):
        """测试质量报告生成"""
        # 运行验证
        quality_validator.validate_prerequisites()
        quality_validator.validate_architecture()
        
        # 生成报告
        report = quality_validator.generate_report()
        
        assert report is not None
        assert hasattr(report, 'report_id')
        assert hasattr(report, 'timestamp')
        assert hasattr(report, 'overall_score')
        assert hasattr(report, 'categories')
        assert hasattr(report, 'recommendations')
    
    def test_report_formats(self, quality_validator, tmp_path):
        """测试不同格式的报告输出"""
        # 生成报告
        report = quality_validator.generate_report()
        
        # 测试JSON格式
        json_path = tmp_path / "report.json"
        report_dict = report.to_dict()
        assert isinstance(report_dict, dict)
        assert 'overall_score' in report_dict
        
        # 测试Markdown格式
        md_content = report.to_markdown()
        assert isinstance(md_content, str)
        assert '质量验证报告' in md_content


# ==================== 性能回归测试 ====================

class TestPerformanceRegression:
    """性能回归测试"""
    
    @pytest.mark.performance
    def test_startup_time(self, quality_validator):
        """测试启动时间基准"""
        start_time = time.time()
        validator = TrainingQualityValidator()
        duration = time.time() - start_time
        
        # 启动时间应该在合理范围内
        assert duration < 5.0, f"启动时间过长: {duration:.2f}秒"
        logger.info(f"验证器启动时间: {duration:.2f}秒")
    
    @pytest.mark.performance
    def test_validation_speed(self, quality_validator):
        """测试验证速度基准"""
        # 测试各项验证的速度
        timings = {}
        
        # 前提条件验证
        start = time.time()
        quality_validator.validate_prerequisites()
        timings['prerequisites'] = time.time() - start
        
        # 架构验证
        start = time.time()
        quality_validator.validate_architecture()
        timings['architecture'] = time.time() - start
        
        # 性能验证
        start = time.time()
        quality_validator.validate_performance()
        timings['performance'] = time.time() - start
        
        # 所有验证应该在合理时间内完成
        for name, duration in timings.items():
            assert duration < 30.0, f"{name}验证耗时过长: {duration:.2f}秒"
            logger.info(f"{name}验证耗时: {duration:.2f}秒")


# ==================== 辅助函数 ====================

def run_quality_tests(markers: List[str] = None):
    """运行质量测试
    
    Args:
        markers: 要运行的测试标记列表
    """
    args = [__file__, '-v']
    
    if markers:
        for marker in markers:
            args.extend(['-m', marker])
    
    return pytest.main(args)


if __name__ == "__main__":
    """直接运行测试"""
    import argparse
    
    parser = argparse.ArgumentParser(description="运行质量验证测试")
    parser.add_argument('--performance', action='store_true', help='只运行性能测试')
    parser.add_argument('--integration', action='store_true', help='只运行集成测试')
    parser.add_argument('--gpu', action='store_true', help='只运行GPU测试')
    parser.add_argument('--slow', action='store_true', help='包括慢速测试')
    
    args = parser.parse_args()
    
    markers = []
    if args.performance:
        markers.append('performance')
    if args.integration:
        markers.append('integration')
    if args.gpu:
        markers.append('gpu')
    if not args.slow:
        markers.append('not slow')
    
    exit_code = run_quality_tests(markers)