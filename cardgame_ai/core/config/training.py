"""
训练配置模块
统一管理所有训练相关参数
"""

from dataclasses import dataclass, field
from typing import Optional, Dict, Any, List
from .base import BaseConfig, ConfigRegistry

@dataclass
class OptimizerConfig(BaseConfig):
    """优化器配置"""
    type: str = "adam"  # adam, sgd, adamw, lamb
    lr: float = 0.0003
    betas: List[float] = field(default_factory=lambda: [0.9, 0.999])
    eps: float = 1e-8
    weight_decay: float = 0.0001
    momentum: float = 0.9  # for SGD
    
@dataclass
class SchedulerConfig(BaseConfig):
    """学习率调度器配置"""
    type: str = "cosine"  # cosine, step, exponential, polynomial
    warmup_steps: int = 1000
    warmup_ratio: float = 0.1
    # Step scheduler
    step_size: int = 10000
    gamma: float = 0.1
    # Cosine scheduler
    T_max: int = 100000
    eta_min: float = 1e-6
    # Polynomial scheduler
    power: float = 1.0
    
@dataclass
class DataLoaderConfig(BaseConfig):
    """数据加载器配置"""
    batch_size: int = 256
    num_workers: int = 4
    pin_memory: bool = True
    prefetch_factor: int = 2
    persistent_workers: bool = True
    drop_last: bool = True
    
@dataclass
class CheckpointConfig(BaseConfig):
    """检查点配置"""
    save_dir: str = "checkpoints"
    save_interval: int = 1000
    keep_last_n: int = 5
    save_best: bool = True
    best_metric: str = "win_rate"
    
@dataclass
class TrainingConfig(BaseConfig):
    """统一的训练配置"""
    # 基础参数
    total_steps: int = 1000000
    eval_interval: int = 10000
    log_interval: int = 100
    
    # 组件配置
    optimizer: OptimizerConfig = field(default_factory=OptimizerConfig)
    scheduler: SchedulerConfig = field(default_factory=SchedulerConfig)
    dataloader: DataLoaderConfig = field(default_factory=DataLoaderConfig)
    checkpoint: CheckpointConfig = field(default_factory=CheckpointConfig)
    
    # 训练技巧
    gradient_accumulation_steps: int = 1
    mixed_precision: bool = True
    gradient_checkpointing: bool = False
    compile_model: bool = True  # torch.compile
    
    # 早停
    early_stopping: bool = True
    early_stopping_patience: int = 10
    early_stopping_min_delta: float = 0.001
    
    # 分布式训练（保留接口，实际为单机）
    distributed: bool = False
    local_rank: int = 0
    world_size: int = 1
    
    def validate(self) -> bool:
        """验证配置有效性"""
        if self.total_steps <= 0:
            raise ValueError("total_steps must be positive")
        if self.eval_interval <= 0:
            raise ValueError("eval_interval must be positive")
        
        # 验证子配置
        self.optimizer.validate()
        self.scheduler.validate()
        self.dataloader.validate()
        self.checkpoint.validate()
        
        return True
        
    def get_batch_size(self) -> int:
        """获取有效批量大小（考虑梯度累积）"""
        return self.dataloader.batch_size * self.gradient_accumulation_steps

# 注册配置类
ConfigRegistry.register(OptimizerConfig)
ConfigRegistry.register(SchedulerConfig)
ConfigRegistry.register(DataLoaderConfig)
ConfigRegistry.register(CheckpointConfig)
ConfigRegistry.register(TrainingConfig)
