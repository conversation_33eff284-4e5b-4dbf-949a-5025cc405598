"""
HAPPO算法配置模块
"""

from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from ..mappo import MAPPOConfig

@dataclass
class HAPPOConfig(MAPPOConfig):
    """HAPPO算法配置"""
    # 从MAPPOConfig继承的属性（需要显式声明）
    lr: float = 3e-4
    gamma: float = 0.99
    gae_lambda: float = 0.95
    clip_epsilon: float = 0.2
    value_loss_coef: float = 0.5
    entropy_coef: float = 0.01
    max_grad_norm: float = 0.5
    ppo_epochs: int = 10
    mini_batch_size: int = 64
    use_central_critic: bool = True
    use_gae: bool = True
    
    # 异构智能体设置
    use_heterogeneous_agents: bool = True
    landlord_hidden_dim: int = 512  # 地主网络更大
    farmer_hidden_dim: int = 256    # 农民网络较小
    
    # 优势分解设置
    advantage_decomposition: str = "credit"  # credit, shapley, attention
    credit_temperature: float = 1.0
    
    # 农民通信设置
    enable_farmer_communication: bool = True
    communication_dim: int = 64  # 调整为适合116维观察空间的大小
    communication_rounds: int = 2
    
    # 协作奖励设置
    cooperation_reward_weight: float = 0.3
    synergy_bonus: float = 0.2
    
    # 角色特定学习率
    landlord_lr: float = 0.0003
    farmer_lr: float = 0.0005
    
    # 序列化训练
    sequential_updates: bool = True
    update_order: List[str] = None  # 默认: ["landlord", "farmer1", "farmer2"]
    
    def __post_init__(self):
        # 调用父类的__post_init__（如果存在）
        if hasattr(super(), '__post_init__'):
            super().__post_init__()
        
        # 设置默认值
        if self.update_order is None:
            self.update_order = ["landlord", "farmer1", "farmer2"]