"""
数据收集器模块
提供各种数据收集功能
"""

from typing import Dict, Any, List, Optional, Callable
from datetime import datetime, timedelta
import threading
from collections import deque, defaultdict
from .base import MonitoringBase, MetricType, MonitoringLevel

class MetricsCollector(MonitoringBase):
    """通用指标收集器"""
    
    def __init__(self, name: str = "metrics_collector", buffer_size: int = 10000):
        super().__init__(name)
        self.buffer_size = buffer_size
        self.data_buffers = defaultdict(lambda: deque(maxlen=buffer_size))
        self.aggregators = {}
        
    def collect(self, metric_name: str, value: Any, timestamp: Optional[datetime] = None):
        """收集单个指标"""
        timestamp = timestamp or datetime.now()
        
        self.data_buffers[metric_name].append({
            'value': value,
            'timestamp': timestamp
        })
        
        self.record_metric(metric_name, value)
        
    def collect_batch(self, metrics: Dict[str, Any], timestamp: Optional[datetime] = None):
        """批量收集指标"""
        timestamp = timestamp or datetime.now()
        
        for name, value in metrics.items():
            self.collect(name, value, timestamp)
            
    def get_data(self, metric_name: str, 
                 start_time: Optional[datetime] = None,
                 end_time: Optional[datetime] = None) -> List[Dict[str, Any]]:
        """获取指标数据"""
        if metric_name not in self.data_buffers:
            return []
            
        data = list(self.data_buffers[metric_name])
        
        # 时间过滤
        if start_time or end_time:
            filtered_data = []
            for item in data:
                ts = item['timestamp']
                if start_time and ts < start_time:
                    continue
                if end_time and ts > end_time:
                    continue
                filtered_data.append(item)
            data = filtered_data
            
        return data
        
    def get_latest(self, metric_name: str, count: int = 1) -> List[Dict[str, Any]]:
        """获取最新数据"""
        if metric_name not in self.data_buffers:
            return []
            
        data = list(self.data_buffers[metric_name])
        return data[-count:] if data else []
        
    def register_aggregator(self, metric_name: str, 
                          aggregator: Callable[[List[Any]], Any],
                          window_size: int = 100):
        """注册聚合器"""
        self.aggregators[metric_name] = {
            'func': aggregator,
            'window_size': window_size
        }
        
    def get_aggregated(self, metric_name: str) -> Any:
        """获取聚合值"""
        if metric_name not in self.aggregators:
            return None
            
        aggregator_config = self.aggregators[metric_name]
        latest_data = self.get_latest(metric_name, aggregator_config['window_size'])
        
        if not latest_data:
            return None
            
        values = [item['value'] for item in latest_data]
        return aggregator_config['func'](values)
        
    def start(self):
        """启动收集器"""
        self.is_running = True
        
    def stop(self):
        """停止收集器"""
        self.is_running = False
        
    def collect_metrics(self) -> Dict[str, Any]:
        """收集当前指标状态"""
        return {
            'buffer_count': len(self.data_buffers),
            'total_points': sum(len(buffer) for buffer in self.data_buffers.values()),
            'metric_names': list(self.data_buffers.keys())
        }

class SystemMetricsCollector(MetricsCollector):
    """系统指标收集器"""
    
    def __init__(self):
        super().__init__("system_metrics")
        self.system_collectors = {}
        
    def register_system_collector(self, name: str, collector: Callable[[], Dict[str, Any]]):
        """注册系统指标收集器"""
        self.system_collectors[name] = collector
        
    def collect_all_system_metrics(self):
        """收集所有系统指标"""
        all_metrics = {}
        
        for name, collector in self.system_collectors.items():
            try:
                metrics = collector()
                for metric_name, value in metrics.items():
                    full_name = f"{name}.{metric_name}"
                    self.collect(full_name, value)
                    all_metrics[full_name] = value
            except Exception as e:
                self.emit_event(
                    MonitoringLevel.ERROR,
                    f"{name}_collection_error",
                    str(e)
                )
                
        return all_metrics

class TrainingMetricsCollector(MetricsCollector):
    """训练指标收集器"""
    
    def __init__(self):
        super().__init__("training_metrics")
        self.episode_data = []
        self.step_data = []
        
    def record_episode(self, episode_num: int, metrics: Dict[str, Any]):
        """记录训练episode"""
        episode_data = {
            'episode': episode_num,
            'timestamp': datetime.now(),
            'metrics': metrics
        }
        
        self.episode_data.append(episode_data)
        
        # 保持最近10000个episode
        if len(self.episode_data) > 10000:
            self.episode_data.pop(0)
            
        # 收集各个指标
        for name, value in metrics.items():
            self.collect(f"episode.{name}", value)
            
    def record_step(self, step_num: int, metrics: Dict[str, Any]):
        """记录训练step"""
        step_data = {
            'step': step_num,
            'timestamp': datetime.now(),
            'metrics': metrics
        }
        
        self.step_data.append(step_data)
        
        # 保持最近100000个step
        if len(self.step_data) > 100000:
            self.step_data.pop(0)
            
        # 收集各个指标
        for name, value in metrics.items():
            self.collect(f"step.{name}", value)
            
    def get_episode_stats(self) -> Dict[str, Any]:
        """获取episode统计"""
        if not self.episode_data:
            return {}
            
        recent_episodes = self.episode_data[-100:]  # 最近100个episode
        
        stats = {}
        for episode_data in recent_episodes:
            for name, value in episode_data['metrics'].items():
                if name not in stats:
                    stats[name] = []
                stats[name].append(value)
                
        # 计算统计值
        result = {}
        for name, values in stats.items():
            if values and isinstance(values[0], (int, float)):
                result[f"{name}_mean"] = sum(values) / len(values)
                result[f"{name}_latest"] = values[-1]
                
        return result

class GameMetricsCollector(MetricsCollector):
    """游戏指标收集器"""
    
    def __init__(self):
        super().__init__("game_metrics")
        self.game_results = []
        self.player_stats = defaultdict(list)
        
    def record_game_result(self, result: Dict[str, Any]):
        """记录游戏结果"""
        result['timestamp'] = datetime.now()
        self.game_results.append(result)
        
        # 保持最近10000局游戏
        if len(self.game_results) > 10000:
            self.game_results.pop(0)
            
        # 更新玩家统计
        for player, stats in result.get('player_stats', {}).items():
            self.player_stats[player].append(stats)
            
        # 收集关键指标
        self.collect('game.winner', result.get('winner'))
        self.collect('game.duration', result.get('duration', 0))
        self.collect('game.total_moves', result.get('total_moves', 0))
        
    def get_win_rate(self, player: str, recent_games: int = 1000) -> float:
        """获取玩家胜率"""
        recent_results = self.game_results[-recent_games:]
        if not recent_results:
            return 0.0
            
        wins = sum(1 for result in recent_results if result.get('winner') == player)
        return wins / len(recent_results)
        
    def get_game_statistics(self) -> Dict[str, Any]:
        """获取游戏统计"""
        if not self.game_results:
            return {}
            
        recent_games = self.game_results[-1000:]  # 最近1000局
        
        total_games = len(recent_games)
        total_duration = sum(game.get('duration', 0) for game in recent_games)
        avg_duration = total_duration / total_games if total_games > 0 else 0
        
        # 胜者统计
        winners = [game.get('winner') for game in recent_games if game.get('winner')]
        winner_counts = defaultdict(int)
        for winner in winners:
            winner_counts[winner] += 1
            
        return {
            'total_games': total_games,
            'avg_duration': avg_duration,
            'winner_distribution': dict(winner_counts),
            'win_rates': {
                player: self.get_win_rate(player, len(recent_games))
                for player in winner_counts.keys()
            }
        }
